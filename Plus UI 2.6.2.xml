<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html b:css='false' b:defaultwidgetversion='2' b:layoutsVersion='3' b:responsive='true' b:templateUrl='indie.xml' b:templateVersion='1.3.0' expr:dir='data:blog.languageDirection' expr:lang='data:blog.locale.language' xmlns='http://www.w3.org/1999/xhtml' xmlns:b='http://www.google.com/2005/gml/b' xmlns:data='http://www.google.com/2005/gml/data' xmlns:expr='http://www.google.com/2005/gml/expr'>
  <b:attr name='xmlns' value=''/>
  <b:attr name='xmlns:b' value=''/>
  <b:attr name='xmlns:expr' value=''/>
  <b:attr name='xmlns:data' value=''/>
  
<!--[

    Name      : Plus UI
    Version   : 2.6.2
    Date      : Sep 10, 2022
    Type      : Premium
    ============================================================================
    NOTE :
    This theme is premium (paid).
    You can only get it by purchasing officially.
    If you get it for free through any method, that means you get it illegally.
    ============================================================================

]-->
  <!--[By Dr-FarFar.CoM]-->
  <!--[ <head> Open ]-->
  &lt;head class=&#39;notranslate&#39;&gt;

  <b:if cond='data:view.isMultipleItems'>
    <b:if cond='data:view.isHomepage'>
      <!--[ Homepage title ]-->
      <title><data:blog.title.escaped/></title>
      <b:elseif cond='data:view.search.query'/>
      <!--[ Search title ]-->
      <title><data:messages.search/>: <data:view.search.query/></title>
      <b:elseif cond='data:view.search.label'/>
      <!--[ Label title ]-->
      <title><data:blog.pageName.escaped/> - <data:blog.title.escaped/></title>
      <b:elseif cond='data:view.isArchive'/>
      <!--[ Archive title ]-->
      <title>Blog archive in: <data:blog.pageName.escaped/></title>
      <b:else/>
      <title>Blog: <data:blog.title.escaped/></title>
    </b:if>
    <b:elseif cond='data:view.isError'/>
    <!--[ Error title ]-->
    <title>Error 404: Not Found</title>
    <b:else/>
    <!--[ SingleItem title ]-->
    <title><data:blog.pageName.escaped/> - <data:blog.title.escaped/></title>
  </b:if>

  <!--[ Meta for browser ]-->
  <meta charset='UTF-8'/>
  <meta content='width=device-width, initial-scale=1, user-scalable=1, minimum-scale=1, maximum-scale=5' name='viewport'/>
  <meta content='IE=edge' http-equiv='X-UA-Compatible'/>
  <meta content='max-image-preview:large' name='robots'/>

  <!--[ Link Canonical ]-->
  <link expr:href='data:blog.url.canonical' rel='canonical'/>

  <b:if cond='!data:view.isError'>
    <!--[ Browser data, description and keyword ]-->
    <b:if cond='data:blog.metaDescription'>
      <meta expr:content='data:blog.metaDescription.escaped' name='description'/>
      <b:elseif cond='data:view.isSingleItem'/>
      <meta expr:content='data:post.snippet.escaped snippet { length: 147, links: false, linebreaks: false, ellipsis: false }' name='description'/>
      <b:else/>
      <meta expr:content='data:blog.pageName.escaped ? data:blog.pageName.escaped : data:blog.title' name='description'/>
    </b:if>
    <meta expr:content='data:blog.title.escaped + &quot;, &quot; + data:blog.pageName.escaped + &quot;, Keyword_1, Keyword_2, Keyword_3 &quot;' name='keywords'/>
    <b:tag cond='data:view.isPost' expr:href='resizeImage(data:blog.postImageUrl, 0)' name='link' rel='image_src'/>
  
    <!--[ Generator and rrs ]-->
    <meta content='blogger' name='generator'/>
    <link expr:href='data:blog.homepageUrl.canonical + &quot;feeds/posts/default&quot;' expr:title='data:blog.title + &quot; Â» Atom&quot;' rel='alternate' type='application/atom+xml'/>
    <link expr:href='data:blog.homepageUrl.canonical + &quot;feeds/posts/default?alt=rss&quot;' expr:title='data:blog.title + &quot; Â» Feed&quot;' rel='alternate' type='application/rss+xml'/>
    <link expr:href='data:blog.homepageUrl.canonical + &quot;feeds/comments/default?alt=rss&quot;' expr:title='data:blog.title + &quot; Â» Comments Feed&quot;' rel='alternate' type='application/rss+xml'/>
  
    <!--[ Theme Color ]-->
    <meta expr:content='data:skin.vars.themeColor' name='theme-color'/>
    <meta expr:content='data:skin.vars.themeColor' name='msapplication-navbutton-color'/>
    <meta expr:content='data:skin.vars.themeColor' name='apple-mobile-web-app-status-bar-style'/>
    <meta expr:content='yes' name='apple-mobile-web-app-capable'/>
  
    <!--[ Favicon ]-->
    <link expr:href='data:blog.blogspotFaviconUrl' rel='apple-touch-icon' sizes='120x120'/>
    <link expr:href='data:blog.blogspotFaviconUrl' rel='apple-touch-icon' sizes='152x152'/>
    <link expr:href='data:blog.blogspotFaviconUrl' rel='icon' type='image/x-icon'/>
    <link expr:href='data:blog.blogspotFaviconUrl' rel='shortcut icon' type='image/x-icon'/>
    
    <!--[ Open graph ]-->
    <meta expr:content='data:blog.pageName.escaped ? data:blog.pageName.escaped : data:blog.title.escaped' property='og:title'/>
    <meta expr:content='data:blog.canonicalUrl' property='og:url'/>
    <meta expr:content='data:blog.title.escaped' property='og:site_name'/>
    <b:if cond='data:view.isMultipleItems'>
      <meta content='website' property='og:type'/>
      <b:if cond='data:blog.metaDescription'>
        <meta expr:content='data:blog.metaDescription.escaped' property='og:description'/>         
        <b:else/>
        <meta expr:content='data:blog.pageName.escaped ? data:blog.pageName.escaped : data:blog.title' property='og:description'/>
      </b:if>
      <b:else/>
      <meta content='article' property='og:type'/>
      <b:if cond='data:blog.metaDescription'>
        <meta expr:content='data:blog.metaDescription.escaped' property='og:description'/>            
        <b:else/>
        <meta expr:content='data:post.snippet.escaped snippet { length: 147, links: false, linebreaks: false, ellipsis: false }' property='og:description'/>
      </b:if>
    </b:if>
    <meta expr:content='data:blog.pageName.escaped ? data:blog.pageName.escaped : data:blog.title.escaped' property='og:image:alt'/>
    <b:if cond='data:blog.postImageUrl'>
      <meta expr:content='resizeImage(data:blog.postImageUrl, 0)' property='og:image'/>
      <b:else/>
      <meta content='Add_your_image_url_here' property='og:image'/>
    </b:if>
    
    <!--[ Twitter Card ]-->
    <meta expr:content='data:blog.pageName.escaped ? data:blog.pageName.escaped : data:blog.title.escaped' name='twitter:title'/>
    <meta expr:content='data:blog.canonicalUrl' name='twitter:url'/>
    <b:if cond='data:view.isMultipleItems'>
      <b:if cond='data:blog.metaDescription'>
        <meta expr:content='data:blog.metaDescription.escaped' name='twitter:description'/>         
        <b:else/>
        <meta expr:content='data:blog.pageName.escaped ? data:blog.pageName.escaped : data:blog.title' name='twitter:description'/>
      </b:if>
      <b:else/>
      <b:if cond='data:blog.metaDescription'>
        <meta expr:content='data:blog.metaDescription.escaped' name='twitter:description'/>            
        <b:else/>
        <meta expr:content='data:post.snippet.escaped snippet { length: 147, links: false, linebreaks: false, ellipsis: false }' name='twitter:description'/>
      </b:if>
    </b:if>
    <meta content='summary_large_image' name='twitter:card'/>
    <meta expr:content='data:blog.pageName.escaped ? data:blog.pageName.escaped : data:blog.title.escaped' name='twitter:image:alt'/>
    <b:if cond='data:blog.postImageUrl'>
      <meta expr:content='resizeImage(data:blog.postImageUrl, 0)' name='twitter:image:src'/>
      <b:else/>
      <meta content='Add_your_image_url_here' name='twitter:image:src'/>
    </b:if>
  
    <!--[ Site Verification ]-->
    <!--<meta content='' name='msvalidate.01'/>
    <meta content='' name='copyright'/>-->
    
    <!--[ Preconnect and DNS Prefetch ]-->
    <link href='//1.bp.blogspot.com' rel='preconnect dns-prefetch'/><link href='//2.bp.blogspot.com' rel='preconnect dns-prefetch'/><link href='//3.bp.blogspot.com' rel='preconnect dns-prefetch'/><link href='//4.bp.blogspot.com' rel='preconnect dns-prefetch'/><link href='//blogger.googleusercontent.com' rel='preconnect dns-prefetch'/><link href='//lh3.googleusercontent.com' rel='preconnect dns-prefetch'/><link href='//www.blogger.com' rel='preconnect dns-prefetch'/><link href='//feeds.feedburner.com' rel='preconnect dns-prefetch'/><link expr:href='data:blog.homepageUrl.canonical path &quot;feeds/posts/default&quot;' rel='preconnect dns-prefetch'/><link expr:href='data:blog.homepageUrl.canonical path &quot;feeds/comments/default&quot;' rel='preconnect dns-prefetch'/><link href='//cdn.widgetpack.com' rel='preconnect dns-prefetch'/><link href='//translate.google.com' rel='preconnect dns-prefetch'/><link href='//maxcdn.bootstrapcdn.com' rel='preconnect dns-prefetch'/><link href='//fonts.googleapis.com' rel='preconnect dns-prefetch'/><link href='//use.fontawesome.com' rel='preconnect dns-prefetch'/><link href='//ajax.googleapis.com' rel='preconnect dns-prefetch'/><link href='//resources.blogblog.com' rel='preconnect dns-prefetch'/><link href='//ajax.microsoft.com ' rel='preconnect dns-prefetch'/><link href='//ajax.aspnetcdn.com ' rel='preconnect dns-prefetch'/><link href='//github.com' rel='preconnect dns-prefetch'/><link href='//cdn.rawgit.com' rel='preconnect dns-prefetch'/><link href='//cdnjs.cloudflare.com' rel='preconnect dns-prefetch'/><link href='//www.google-analytics.com' rel='preconnect dns-prefetch'/><link href='//themes.googleusercontent.com ' rel='preconnect dns-prefetch'/><link href='//pagead2.googlesyndication.com' rel='preconnect dns-prefetch'/><link href='//googleads.g.doubleclick.net' rel='preconnect dns-prefetch'/><link href='//www.gstatic.com' rel='preconnect dns-prefetch'/><link href='//www.googletagservices.com' rel='preconnect dns-prefetch'/><link href='//static.xx.fbcdn.net' rel='preconnect dns-prefetch'/><link href='//tpc.googlesyndication.com' rel='preconnect dns-prefetch'/><link href='//apis.google.com' rel='preconnect dns-prefetch'/><link href='//www.w3.org' rel='preconnect dns-prefetch'/><link href='//www.facebook.com' rel='preconnect dns-prefetch'/><link href='//connect.facebook.net' rel='preconnect dns-prefetch'/><link href='//disqus.com' rel='preconnect dns-prefetch'/><link href='//plus.google.com' rel='preconnect dns-prefetch'/><link href='//twitter.com' rel='preconnect dns-prefetch'/><link href='//platform.twitter.com' rel='preconnect dns-prefetch'/><link href='//syndication.twitter.com' rel='preconnect dns-prefetch'/><link href='//r.twimg.com ' rel='preconnect dns-prefetch'/><link href='//p.twitter.com ' rel='preconnect dns-prefetch'/><link href='//cdn.api.twitter.com ' rel='preconnect dns-prefetch'/><link href='//www.youtube.com' rel='preconnect dns-prefetch'/><link href='//img.youtube.com' rel='preconnect dns-prefetch'/><link href='//www.pinterest.com' rel='preconnect dns-prefetch'/><link href='//statically.io/' rel='preconnect dns-prefetch'/><link href='//www.linkedin.com' rel='preconnect dns-prefetch'/><link href='//player.vimeo.com' rel='preconnect dns-prefetch'/><link href='//s3.amazonaws.com ' rel='preconnect dns-prefetch'/><link href='//s3.buysellads.com ' rel='preconnect dns-prefetch'/><link href='//stats.buysellads.com ' rel='preconnect dns-prefetch'/><link href='//cdn.adpacks ' rel='preconnect dns-prefetch'/>
    
  </b:if>
  
  <b:if cond='data:view.isLayoutMode'>
    <!--[By Dr-FarFar.CoM]-->
    <!--[ CSS Layout ]-->
    <b:template-skin><![CDATA[
body#layout::before{content:'Plus UI v2.6 by Dr-FarFar.CoM';position:absolute;top:15px;right:15px;font-size:.8rem;font-family:Roboto, sans-serif;color:rgba(0,0,0,0.52)} body#layout{width:1025px;margin:0 !important;padding:60px 0 0 !important;border:0 !important;text-align:left !important;position:relative} body#layout div.layout-widget-description{font-size:12px !important;line-height:1.6em} body#layout div.layout-title{font-size:15px !important} body#layout div.section{border-radius:2px} body#layout .section h4{font-size:15px !important;margin-left:0!important} body#layout .add_widget a{font-size:13px !important} body#layout .Blog .widget-content{height:auto!important} body#layout #anchor-ad, body#layout #notif-widget .widget-content, body#layout #side-sticky, body#layout #HTML91 .widget-content, body#layout #HTML92 .widget-content, body#layout #HTML93 .widget-content, body#layout #HTML94 .widget-content, body#layout #HTML01 .widget-content, body#layout #HTML02 .widget-content, body#layout #HTML03 .widget-content, body#layout #HTML04 .widget-content{background:#f0f8ff !important} body#layout #maintenance-mode, body#layout .mobMn, body#layout #anchor-ad{margin-bottom:30px!important} body#layout .mainIn, body#layout .headCn, body#layout .blogM{display:flex} body#layout .mainbar, body#layout .headR{width:55%} body#layout .mainIn .blogCont{width:70%} body#layout .sidebar, body#layout .headL{width:45%} body#layout header, body#layout .mainIn, body#layout footer, body#layout .erroP, body#layout .addonsWidgets{border-top:1px solid #e5e5e5;padding:30px 0;position:relative} body#layout header::before, body#layout .mainIn::before, body#layout footer::before, body#layout .erroP::before, body#layout .addonsWidgets::before{content:'Header';position:absolute;top:-14px;left:20px;padding:5px 20px;border:1px solid #e5e5e5;border-radius:20px;font-size:.8rem;font-family:Roboto,sans-serif;color:rgba(0,0,0,0.52);background:#f1f1f1} body#layout #notif-widget h4{display:none} body#layout .mainIn{padding-bottom:0} body#layout .mainIn::before{content:'Main Content'} body#layout footer::before{content:'Footer'} body#layout .erroP::before{content:'Custom Error Page'} body#layout .addonsWidgets::before{content:'Add-ons'} ]]></b:template-skin>
  </b:if>
  <!--[By Dr-FarFar.CoM]-->
  <!--[ CSS stylesheet ]-->
  &lt;style&gt;&lt;!-- /* <b:skin version='1.3.0'><![CDATA[
/*
<Group description="Theme Color">
<Variable name="themeColor" description="Light Mode Address bar color" type="color" default="#2e51a2" value="#482dff"/>
<Variable name="themeDark" description="Dark Mode Address bar color" type="color" default="#1e1e1e" value="#1e1e1e"/>
</Group>

<Group description="Basic colors and Background">
<Variable name="head.color" description="Heading colors" type="color" default="#262d3d" value="#08102b"/>
<Variable name="body.color" description="Body colors" type="color" default="#48525c" value="#08102b"/>
<Variable name="body.colorAlt" description="Alternative body colors" type="color" default="#767676" value="#989b9f"/>
<Variable name="body.bgColor" description="Main page bakground color" type="color" default="#fffdfc" value="#fdfcff"/>
<Variable name="link.color" description="Link colors" type="color" default="#204ecf" value="#482dff"/>
<Variable name="link.bgColor" description="Button link colors" type="color" default="#204ecf" value="#482dff"/>
<Variable name="wave.bgColor" description="Wave background color" type="color" default="#204ecf" value="#C6DAFC"/>
</Group>

<Group description="Icon colors">
<Variable name="icon.color" description="Icon colors" type="color" default="#48525c" value="#08102b"/>
<Variable name="icon.colorAlt" description="Alternative icon colors" type="color" default="#767676" value="#08102b"/>
<Variable name="icon.colorSec" description="Secondary icon colors" type="color" default="#767676" value="#767676"/>
</Group>

<Group description="Notification">
<Variable name="notif.height" description="Notification max height" type="length" max="60px" default="45px" value="60px"/>
<Variable name="notif.bg" description="Notification background color" type="color" default="#e1f5fe" value="#e8f0fe"/>
<Variable name="notif.color" description="Notification text color" type="color" default="#08102b" value="#01579b"/>
</Group>

<Group description="Header colors, background and height">
<Variable name="header.height" description="Header height" type="length" max="80px" default="60px" value="60px"/>
<Variable name="header.heightM" description="Header height(mobile)" type="length" max="80px" default="60px" value="60px"/>
<Variable name="header.titleSize" description="Header title font size" type="length" max="20px" default="16px" value="16px"/>
<Variable name="header.text" description="Header text colors" type="color" default="#48525c" value="#343435"/>
<Variable name="header.icon" description="Header icon colors" type="color" default="#262d3d" value="#08102b"/>
<Variable name="header.bgColor" description="Header background color" type="color" default="#fffdfc" value="#fffdfc"/>
<Variable name="header.border" description="Header border" type="length" max="1px" default="0px" value="1px"/>
</Group>

<Group description="Navbar colors">
<Variable name="nav.width" description="Navbar max width" type="length" max="260px" default="230px" value="230px"/>
<Variable name="nav.border" description="Navbar border" type="length" max="1px" default="0px" value="1px"/>
<Variable name="nav.text" description="Navbar text colors" type="color" default="#48525c" value="#08102b"/>
<Variable name="nav.icon" description="Navbar icon color" type="color" default="#48525c" value="#08102b"/>
<Variable name="nav.bg" description="Navbar background color" type="color" default="#fffdfc" value="#fffdfc"/>
</Group>

<Group description="Searchbar colors">
<Variable name="search.icon" description="Searchbar icon color" type="color" default="#48525c" value="#48525c"/>
<Variable name="search.bg" description="Searchbar background color" type="color" default="#fffdfc" value="#fffdfc"/>
</Group>

<Group description="Content background">
<Variable name="content.bg" description="Content background color" type="color" default="#fffdfc" value="#fffdfc"/>
<Variable name="content.border" description="Content border color" type="color" default="#eceff1" value="#e6e6e6"/>
</Group>

<Group description="Content max width">
<Variable name="content.maxWidth" description="Max width of main content" type="length" max="1440px" default="1024px" value="1280px"/>
<Variable name="sidebar.maxWidth" description="Max width of sidebar" type="length" max="360px" default="300px" value="300px"/>
<Variable name="post.maxContent" description="Post max width" type="length" max="900px" default="720px" value="780px"/>
<Variable name="page.maxContent" description="Static page max width" type="length" max="900px" default="780px" value="780px"/>
</Group>

<Group description="Post font size">
<Variable name="post.titleSize" description="Post title font size" type="length" max="42px" default="28px" value="36px"/>
<Variable name="post.fontSize" description="Post font size (Default)" type="length" max="18px" default="16px" value="16px"/>
<Variable name="post.titleSizeMobile" description="Post title size (onMobile)" type="length" max="38px" default="22px" value="28px"/>
<Variable name="post.fontSizeMobile" description="Post font size (onMobile)" type="length" max="18px" default="16px" value="15px"/>
</Group>

<Group description="Widget title font size">
<Variable name="widget.titleSize" description="Widget title font size" type="length" max="18px" default="14px" value="15px"/>
<Variable name="widget.titleAfter" description="Widget title border" type="length" max="40px" default="25px" value="25px"/>
<Variable name="widget.titleAfterC" description="Widget title border color" type="color" default="#989b9f" value="#989b9f"/>
</Group>

<Group description="Mobile menu colors">
<Variable name="mob.border" description="Mobile menu border" type="length" max="2px" default="0px" value="1px"/>
<Variable name="mob.borderR" description="Mobile corder radius" type="length" max="60px" default="0px" value="12px"/>
<Variable name="mob.text" description="Mobile text colors" type="color" default="#48525c" value="#08102b"/>
<Variable name="mob.bg" description="Mobile background color" type="color" default="#fffdfc" value="#fffdfc"/>
<Variable name="mob.hovBg" description="Mobile hover background color" type="color" default="#f6f6f6" value="#f1f1f0"/>
</Group>

<Group description="Footer colors">
<Variable name="foot.border" description="Footer border" type="length" max="2px" default="0px" value="1px"/>
<Variable name="foot.text" description="Footer text colors" type="color" default="#48525c" value="#08102b"/>
<Variable name="foot.bg" description="Footer background color" type="color" default="#fffdfc" value="#fffdfc"/>
</Group>

<Group description="Dark mode colors and background">
<Variable name="dark.text" description="Dark mode text color" type="color" default="#fffdfc" value="#fffdfc"/>
<Variable name="dark.textAlt" description="Alternative text colors" type="color" default="#989b9f" value="#989b9f"/>
<Variable name="dark.link" description="Dark mode link colors" type="color" default="#005af0" value="#41B375"/>
<Variable name="dark.wave" description="Dark mode wave background color" type="color" default="#005af0" value="#343435"/>
<Variable name="dark.bg" description="Dark mode background color" type="color" default="#1e1e1e" value="#1e1e1e"/>
<Variable name="dark.bgAlt" description="Alternative background color" type="color" default="#2d2d30" value="#2d2d30"/>
<Variable name="dark.bgSec" description="Secondary background color" type="color" default="#252526" value="#252526"/>
</Group>

<Group description="Border Radius">
<Variable name="button.radius" description="Buttons Border Radius" type="length" max="50px" default="3px" value="50px"/>
<Variable name="greet.radius" description="Greetings and Views Border Radius" type="length" max="25px" default="3px" value="10px"/>
<Variable name="iconhover.radius" description="Icon Hover Border Radius" type="length" max="15px" default="15px" value="15px"/>
<Variable name="thumbelem.radius" description="Thumbnail Elements Border Radius" type="length" max="15px" default="8px" value="5px"/>
<Variable name="moblsearch.radius" description="Blog Search Border Radius (Mobile)" type="length" max="20px" default="12px" value="20px"/>
<Variable name="desksearch.radius" description="Blog Search Border Radius (Desktop)" type="length" max="20px" default="8px" value="20px"/>
<Variable name="moblheader.radius" description="Header Style 2 Border Radius (Mobile)" type="length" max="30px" default="8px" value="30px"/>
</Group>

<Group description="(Do not edit) New Blogger comment required">
<Variable name="body.background" description="Background" color="#505050" type="background" default="$(color) none repeat scroll center center"  value="$(color) url() no-repeat scroll center center"/>
<Variable name="body.text.font" description="Font Blogger comment" type="font" default="&#39;Roboto&#39;, sans-serif" value="400 14px &#39;Roboto&#39;, sans-serif"/>
<Variable name="body.text.color" description="Color" type="color" default="#505050"  value="#505050"/>
<Variable name="body.link.color" description="Link color" type="color" default="#262d3d"  value="#989b9f"/>
<Variable name="posts.title.color" description="Post title color" type="color" default="#262d3d"  value="#989b9f"/>
<Variable name="posts.text.color" description="Post text color" type="color" default="#48525c"  value="#989b9f"/>
<Variable name="posts.icons.color" description="Post info color" type="color" default="#262d3d" value="#989b9f"/>
<Variable name="posts.background.color" description="Post background color" type="color" default="#f7f7fc"  value="transparent"/>
<Variable name="tabs.font" description="Font" type="font" default="&#39;Roboto&#39;, sans-serif" value="400 14px &#39;Roboto&#39;, sans-serif"/>
<Variable name="tabs.color" description="Text color" type="color" default="#4d4d4d"  value="#08102b"/>
<Variable name="tabs.selected.color" description="Selected color" type="color" default="#fff"  value="#fffdfc"/>
<Variable name="tabs.overflow.background.color" description="Popup background color" type="color" default="$(posts.background.color)"  value="#fffdfc"/>
<Variable name="tabs.overflow.color" description="Popup text color" type="color" default="#48525c"  value="#08102b"/>
<Variable name="tabs.overflow.selected.color" description="Popup selected color" type="color" default="#262d3d"  value="#989b9f"/>
<Variable name="labels.background.color" description="Labels background color" type="color" default="#fff" value="#fffdfc"/>
<Variable name="blog.title.font" description="Blog title font" type="font" default="&#39;Roboto&#39;, sans-serif" value="400 14px &#39;Roboto&#39;, sans-serif"/>
<Variable name="blog.title.color" description="Blog title color" type="color" default="#fff" value="#fffdfc"/>
</Group>
*/

/* Variable color */ :root, :root .lgT:not(.drK), :root .theme0:not(.drK){/* Theme */ --themeC: $(themeColor); /* Body */ --headC: $(head.color); --bodyC: $(body.color); --bodyCa: $(body.colorAlt); --bodyB: $(body.bgColor); /* Link */ --linkC: $(link.color); --linkB: $(link.bgColor); /* Wave */ --waveB: $(wave.bgColor); /* Icon */ --iconC: $(icon.color); --iconCa: $(icon.colorAlt); --iconCs: $(icon.colorSec); /* Header */ --headerC: $(header.text); --headerT: $(header.titleSize); --headerW: 400; /* write 400(normal) or 700(bold) */ --headerB: $(header.bgColor); --headerL: $(header.border); --headerI: $(header.icon); --headerH: $(header.height); --headerHi: -$(header.height); --headerHm: $(header.heightM); --headerR: $(moblheader.radius); /* Notif */ --notifH: $(notif.height); --notifU: $(notif.bg); --notifC: $(notif.color); /* Content */ --contentB: $(content.bg); --contentL: $(content.border); --contentW: $(content.maxWidth); --sideW: $(sidebar.maxWidth); --transB: rgba(0,0,0,.05); /* Page */ --pageW: $(page.maxContent); --pageW: $(post.maxContent); --postT: $(post.titleSize); --postF: $(post.fontSize); --postTm: $(post.titleSizeMobile); --postFm: $(post.fontSizeMobile); /* Widget */ --widgetT: $(widget.titleSize); --widgetTw: 400; /* write 400(normal) or 700(bold) */ --widgetTa: $(widget.titleAfter); --widgetTac: $(widget.titleAfterC); /* Nav */ --navW: $(nav.width); --navT: $(nav.text); --navI: $(nav.icon); --navB: $(nav.bg); --navL: $(nav.border); /* Search */ --srchI: $(search.icon); --srchB: $(search.bg); /* Mob Men */ --mobT: $(mob.text); --mobHv: $(mob.hovBg); --mobB: $(mob.bg); --mobL: $(mob.border); --mobBr: $(mob.borderR); /* Footer */ --fotT: $(foot.text); --fotB: $(foot.bg); --fotL: $(foot.border); /* Font family */ --fontH: Google Sans Text,Arial,Helvetica,sans-serif; --fontB: Google Sans Text,Arial,Helvetica,sans-serif; --fontBa: Google Sans Text,Arial,Helvetica,sans-serif; --fontC: Google Sans Mono,monospace; /* Trans */ --trans-1: all .1s ease; --trans-2: all .2s ease; --trans-3: all .3s ease; --trans-4: all .4s ease; /* Synx */ --synxBg: #f6f6f6; --synxC: #2f3337; --synxOrange: #b75501; --synxBlue: #015692; --synxGreen: #54790d; --synxRed: #f15a5a; --synxGray: #656e77; /* Border Radius */ --buttonR: $(button.radius); --greetR: $(greet.radius);  --iconHr: $(iconhover.radius);  --thumbEr: $(thumbelem.radius); --srchMr: $(moblsearch.radius); --srchDr : $(desksearch.radius); /* Dark */ --darkT: $(dark.text); --darkTa: $(dark.textAlt); --darkU: $(dark.link); --darkW: $(dark.wave); --darkB: $(dark.bg); --darkBa: $(dark.bgAlt); --darkBs: $(dark.bgSec);}

/* Dark Mode */ :root .drK{/* Theme */ --themeC: $(themeDark);}

/* Theme 1 - Red */ :root .theme1:not(.drK){/* Theme */ --themeC: #D32F2F; /* Body */ --bodyB: #FFFCFD; /* Link */ --linkC: #F44336; --linkB: #F44336; /* Header */ --headerB: #FFEBEE; /* Notif */ --notifU: #FFEBEE; --notifC: #B71C1C; /* Search */ --srchB: #FFEBEE; /* MobMn */ --mobB: #FFEBEE; /* Wave */ --waveB: #FFEBEE;}

/* Theme 2 - Green */ :root .theme2:not(.drK){/* Theme */ --themeC: #00796B; /* Body */ --bodyB: #FCFFFC; /* Link */ --linkC: #009688; --linkB: #009688; /* Header */ --headerB: #E0F2F1; /* Notif */ --notifU: #E0F2F1; --notifC: #00796B; /* Search */ --srchB: #E0F2F1; /* MobMn */ --mobB: #E0F2F1; /* Wave */ --waveB: #E0F2F1;}

/* Theme 3 - Blue */ :root .theme3:not(.drK){/* Theme */ --themeC: #1565C0; /* Body */ --bodyB: #FBFEFF; /* Link */ --linkC: #1976D2; --linkB: #1976D2; /* Header */ --headerB: #E3F2FD; /* Notif */ --notifU: #E3F2FD; --notifC: #1565C0; /* Search */ --srchB: #E3F2FD; /* MobMn */ --mobB: #E3F2FD; /* Wave */ --waveB: #E3F2FD;}

/* Theme 4 - Yellow */ :root .theme4:not(.drK){/* Theme */ --themeC: #FFC107; /* Body */ --bodyB: #FFFEFA; /* Link */ --linkC: #FF8F00; --linkB: #FF8F00; /* Header */ --headerB: #FFF8E1; /* Notif */ --notifU: #FFF8E1; --notifC: #FF8F00; /* Search */ --srchB: #FFF8E1; /* MobMn */ --mobB: #FFF8E1; /* Wave */ --waveB: #FFF8E1;}

/* Theme 5 - Pink */ :root .theme5:not(.drK){/* Theme */ --themeC: #C2185B; /* Body */ --bodyB: #FFFCFD; /* Link */ --linkC: #D81B60; --linkB: #D81B60; /* Header */ --headerB: #FCE4EC; /* Notif */ --notifU: #FCE4EC; --notifC: #C2185B; /* Search */ --srchB: #FCE4EC; /* MobMn */ --mobB: #FCE4EC; /* Wave */ --waveB: #FCE4EC;}

/* Theme 6 - Orange */ :root .theme6:not(.drK){/* Theme */ --themeC: #E64A19; /* Body */ --bodyB: #FBFEFF; /* Link */ --linkC: #F4511E; --linkB: #F4511E; /* Header */ --headerB: #FBE9E7; /* Notif */ --notifU: #FBE9E7; --notifC: #E64A19; /* Search */ --srchB: #FBE9E7; /* MobMn */ --mobB: #FBE9E7; /* Wave */ --waveB: #FBE9E7;}

/* Theme 7 - Blue Grey */ :root .theme7:not(.drK){/* Theme */ --themeC: #455A64; /* Body */ --bodyB: #FBFEFF; /* Link */ --linkC: #546E7A; --linkB: #546E7A; /* Header */ --headerB: #ECEFF1; /* Notif */ --notifU: #ECEFF1; --notifC: #455A64; /* Search */ --srchB: #ECEFF1; /* MobMn */ --mobB: #ECEFF1; /* Wave */ --waveB: #ECEFF1;}

/* Theme 8 - Brown */ :root .theme8:not(.drK){/* Theme */ --themeC: #5D4037; /* Body */ --bodyB: #FBFEFF; /* Link */ --linkC: #5D4037; --linkB: #5D4037; /* Header */ --headerB: #EFEBE9; /* Notif */ --notifU: #EFEBE9; --notifC: #5D4037; /* Search */ --srchB: #EFEBE9; /* MobMn */ --mobB: #EFEBE9; /* Wave */ --waveB: #EFEBE9;}

/* Theme 9 - Purple */ :root .theme9:not(.drK){/* Theme */ --themeC: #7B1FA2; /* Body */ --bodyB: #FBFEFF; /* Link */ --linkC: #8E24AA; --linkB: #8E24AA; /* Header */ --headerB: #F3E5F5; /* Notif */ --notifU: #F3E5F5; --notifC: #7B1FA2; /* Search */ --srchB: #F3E5F5; /* MobMn */ --mobB: #F3E5F5; /* Wave */ --waveB: #F3E5F5;}

/* Theme 10 - Indigo */ :root .theme10:not(.drK){/* Theme */ --themeC: #283593; /* Body */ --bodyB: #FBFEFF; /* Link */ --linkC: #3949AB; --linkB: #3949AB; /* Header */ --headerB: #E8EAF6; /* Notif */ --notifU: #E8EAF6; --notifC: #283593; /* Search */ --srchB: #E8EAF6; /* MobMn */ --mobB: #E8EAF6; /* Wave */ --waveB: #E8EAF6;}
]]></b:skin>

<style><b:if cond='!data:blog.isMobileRequest'>/*<![CDATA[*/
/* Desktop Fonts */
/* Font Body and Alternative */ @font-face{font-family:'Google Sans Text';font-style:normal;font-weight:400;font-display:swap;src:local('Google Sans Text'),local('Google-Sans-Text'),url(https://fonts.gstatic.com/s/googlesanstext/v16/5aUu9-KzpRiLCAt4Unrc-xIKmCU5qEp2iw.woff2) format('woff2')} 
/* Font Heading */ @font-face{font-family:'Google Sans Text';font-style:normal;font-weight:700;font-display:swap;src:local('Google Sans Text'),local('Google-Sans-Text'),url(https://fonts.gstatic.com/s/googlesanstext/v16/5aUp9-KzpRiLCAt4Unrc-xIKmCU5oPFTnmhjtg.woff2) format('woff2')}
/* Source Code Font */ @font-face{font-family:'Google Sans Mono';font-style:normal;font-weight:400;font-display:swap;src:local('Google Sans Mono'),local('Google-Sans-Mono'),url(https://fonts.gstatic.com/s/googlesansmono/v4/P5sUzYWFYtnZ_Cg-t0Uq_rfivrdYH4RE8-pZ5gQ1abT53wVQGrk.woff2) format('woff2')}
/*]]>*/
</b:if>/*<![CDATA[*/
/* Standar CSS */ ::selection{color:#fff;background:var(--linkB)} .drK ::selection{background:var(--darkU)} *, ::after, ::before{-webkit-box-sizing:border-box;box-sizing:border-box} *{-webkit-tap-highlight-color:transparent} h1, h2, h3, h4, h5, h6{margin:0;font-weight:700;font-family:var(--fontH);color:var(--headC)} h1{font-size:1.9rem} h2{font-size:1.7rem} h3{font-size:1.5rem} h4{font-size:1.4rem} h5{font-size:1.3rem} h6{font-size:1.2rem} a{color:var(--linkC);text-decoration:none} a:hover{opacity:.9;transition:opacity .1s} table{border-spacing:0} iframe{max-width:100%;border:0;margin-left:auto;margin-right:auto} input, button, select, textarea{font:inherit;font-size:100%;color:inherit;line-height:normal} input::placeholder{color:rgba(0,0,0,.5)} img{display:block;position:relative;max-width:100%;height:auto} svg{width:22px;height:22px;fill:var(--iconC)} svg.line, svg .line{fill:none!important;stroke:var(--iconC);stroke-linecap:round;stroke-linejoin:round; stroke-width:1} svg.c-1{fill:var(--iconCa)} svg.c-2{fill:var(--iconCs); opacity:.4} .hidden{display:none !important} .invisible{visibility:hidden !important} .clear{width:100%;display:block;margin:0;padding:0;float:none;clear:both} .fCls{display:block;position:fixed;top:-50%;left:-50%;right:-50%;bottom:-50%;z-index:1;transition:var(--trans-1);background:transparent;opacity:0;visibility:hidden} .free::after, .new::after{display:inline-block;content:'Free!';color:var(--linkC);font-size:12px;font-weight:400;margin:0 5px} .new::after{content:'New!'} svg .svgC{fill:var(--linkC)} svg.line .svgC{fill:none;stroke:var(--linkC)} .drK svg .svgC{fill:var(--darkU)} .drK svg.line .svgC{fill:none;stroke:var(--darkU)}  .blog-admin, .bD .bmPs, .pInf .pIc .bmPs, .bmPs>svg .d, .isBkm, .cBkPs, .ckW, .tocL, .headR .headM .themeBtn, .cusW{display:none} .lazy.loaded, .tocL, .pVws:not(.hidden){animation:opaCity .5s 0s;-webkit-animation:opaCity .5s 0s}
/* Unfilled Ads */ ins.adsbygoogle[data-ad-status="unfilled"]{display:none !important}
/* Main Element */ html{scroll-behavior:smooth;overflow-x:hidden} body{-webkit-font-smoothing:antialiased;position:relative;top:0!important;margin:0;padding:0!important;width:100%;font-family:var(--fontB);font-size:14px;color:var(--bodyC);background:var(--bodyB);-webkit-font-smoothing:antialiased;animation:opaCity 1s 0s;-webkit-animation:opaCity 1s 0s} .secIn{margin:0 auto;padding-left:20px;padding-right:20px;max-width:var(--contentW)}
/* Notif Section */ .ntfC{display:flex;align-items:center;position:relative;min-height:var(--notifH);background:var(--notifU);color:var(--notifC);padding:10px 15px; font-size:13px; transition:var(--trans-1);overflow:hidden;border-radius:10px;margin-bottom:20px} .ntfC::before{content:'';width:60px;height:60px;background:rgba(0,0,0,.15);display:block;border-radius:50%;position:absolute;top:-12px;left:-12px;opacity:.1} .Rtl .ntfC::before{left:unset;right:-12px} .ntfC .secIn{width:100%;position:relative} .ntfC .c{display:flex;align-items:center} .ntfT{width:100%; padding-right:15px; text-align:center} .ntfT a{color:var(--linkC);font-weight:700} .ntfI:checked ~ .ntfC{height:0;min-height:0;margin:0;padding:0;opacity:0;visibility:hidden} .ntfA{display:inline-flex;align-items:center;justify-content:center;text-align:initial} .ntfA >a{flex-shrink:0;white-space:nowrap;display:inline-block; margin-left:10px;padding:8px 12px;border-radius:var(--buttonR);background:#fffdfc;color:var(--notifC);font-size:12px;font-weight:400;box-shadow:0 10px 8px -8px rgb(0 0 0 / 12%);text-decoration:none} .drK .ntfA >a{background:var(--darkU);color:#fffdfc}
/* Fixed/Pop-up Element */ .fixL{display:flex;align-items:center;position:fixed;left:0;right:0;bottom:0;margin-bottom:-100%;z-index:20;transition:var(--trans-1);width:100%;height:100%;opacity:0;visibility:hidden} .fixLi, .fixL .cmBri{width:100%;max-width:680px;max-height:calc(100% - 60px);border-radius:12px;transition:inherit;z-index:3;display:flex;overflow:hidden;position:relative;margin:0 auto;box-shadow:0 5px 30px 0 rgba(0,0,0,.05)} .fixLs{padding:60px 20px 20px;overflow-y:scroll;overflow-x:hidden;width:100%;background:var(--contentB)} .fixH, .mnH{display:flex;background:inherit;position:absolute;top:0;left:0;right:0;padding:0 10px;z-index:2} .fixH .cl{padding:0 10px;display:flex;align-items:center;justify-content:flex-end;position:relative;flex-shrink:0;min-width:40px} .fixH .c::after, .ntfC .c::after, .mnH .c::before{content:'\2715';line-height:18px;font-size:14px} .fixT::before{content:attr(data-text);flex-grow:1;padding:16px 10px;font-size:90%;opacity:.7} .fixT .c::before, .mnH .c::after{content:attr(aria-label);font-size:11px;margin:0 8px;opacity:.6} .fixi:checked ~ .fixL, #comment:target .fixL{margin-bottom:0;opacity:1;visibility:visible} .fixi:checked ~ .fixL .fCls, #comment:target .fixL .fCls, .BlogSearch input:focus ~ .fCls{opacity:1;visibility:visible;background:rgba(0,0,0,.2); -webkit-backdrop-filter:saturate(180%) blur(10px); backdrop-filter:saturate(180%) blur(10px)} .shBri{max-width:520px} /* display:flex */ .headI, .bIc{display:flex;align-items:center}
/* Header Section */ header{width:100%;z-index:10; position:-webkit-sticky;position:sticky;top:0;box-shadow:0 0 15px rgba(0,0,0,.07)} header a{display:block;color:var(--bodyC)} header svg{width:20px;height:20px;fill:var(--headerI); opacity:.8} header svg.line{fill:none;stroke:var(--headerI)} .headIc .tNav svg >*{opacity:0;transition:var(--trans-1)} .hdMn .navI:not(:checked) ~ .mainWrp .headIc .tNav .h1, .hdMn .navI:checked ~ .mainWrp .headIc .tNav .h2, .bD:not(.hdMn) .navI:not(:checked) ~ .mainWrp .headIc .tNav .h2, .bD:not(.hdMn) .navI:checked ~ .mainWrp .headIc .tNav .h1{opacity:1} .headCn{position:relative;height:var(--headerH);color:var(--headerC);background:var(--headerB); display:flex} .headL{display:flex;align-items:center;width: var(--navW) ; /* change var(--navW) to increase header title width */ padding:0 0 0 20px; transition:var(--trans-1)} .headL .headIc{flex:0 0 30px} .headL .headN{width:calc(100% - 30px); padding:0 0 0 5px} .headR{padding:0 25px; flex-grow:1; transition:var(--trans-1)} .headI .headP{display:flex;justify-content:flex-end;position:relative} .headI .headS{} .headI{height:100%; justify-content:space-between; position:relative;width:calc(100% + 15px);left:-7.5px;right:-7.5px} .headI >*{margin:0 7.5px} .headIc{font-size:11px;display:flex;list-style:none;margin:0;padding:0} .headIc >*{position:relative} .headIc svg{z-index:1} .headIc .tNav .l{stroke:var(--linkC)} .drK .headIc .tNav .l{stroke:var(--darkU)} .headIc .isSrh{display:none} ul.headIc{position:relative;width:calc(100% + 14px);left:-7px;right:-7px;justify-content:flex-end} ul.headIc li{margin:0 2px} ul.headIc li >*{cursor:pointer} .Header{background-repeat:no-repeat;background-size:100%;background-position:center} .Header img{max-width:160px;max-height:45px} .Header .headH{display:block;color:inherit;font-size:var(--headerT); font-weight:var(--headerW)} .Header .headH.hasSub{display:flex;align-items:baseline} .Header .headTtl{overflow:hidden;white-space:nowrap;text-overflow:ellipsis; display:block} .Header .headSub{margin:0 5px;font:400 11px var(--fontB); white-space:nowrap;text-overflow:ellipsis;overflow:hidden;max-width:60px;opacity:.6} .Header .headSub::before{content:attr(data-text)} /* Icon */ .tIc{width:30px;height:30px;justify-content:center} .tIc::after{content:'';background:var(--transB);border-radius:var(--iconHr);position:absolute;left:0;right:0;top:0;bottom:0;transition:var(--trans-1);opacity:0;visibility:hidden} .tIc:hover::after{opacity:1;visibility:visible;transform:scale(1.3,1.3)} .tDL .d2, .drK .tDL .d1{display:none} ul.headIc li .fCls, .headR .headM, .isDrk .cusW{cursor:auto}
/* mainIn Section */ .mainWrp:after{content:'';display:block;position:absolute;top:0;right:0;width:170px;height:170px;border-radius:0 0 0 200px;background:rgba(0,0,0,.02);z-index:-1} .drK .mainWrp:after{background:rgba(0,0,0,.10)} .blogCont{flex-grow:1;padding:20px 0 0;position:relative;transition:var(--trans-1)} .blogCont .section:not(.no-items), .blogCont .widget:not(:first-child){margin-top:40px} .blogCont .section:first-child, .blogCont footer .section:not(:first-child), .blogCont footer .widget:not(:first-child), .blogCont .section.mobMn, #notif-widget .widget:not(:first-child){margin-top:0} .blogAd .section:not(.no-items){margin-bottom:40px} #notif-widget .widget:first-child{margin-bottom:20px} .blogM{flex-wrap:wrap;justify-content:center;padding-bottom:40px} .sidebar{max-width:500px;margin:50px auto 0} .sideSticky{position:-webkit-sticky;position:sticky;top:calc(var(--headerH) + 10px)} .onPs .blogM .mainbar{max-width:var(--pageW)} .onPg .blogM .mainbar{max-width:var(--pageW)}
/* mainNav */ .mnBrs{background:var(--contentB)} .mnBr a{color:inherit} .mnBr ul{list-style:none;margin:0;padding:0} .mnMob{align-self:flex-end;background:inherit;border-top:1px solid var(--contentL);bottom:0;left:0;padding:15px 20px 20px;position:absolute;right:0;text-align:center;z-index:1} .mnMob .mSoc{display:flex;justify-content:center;left:-7px;margin-top:5px;position:relative;right:-7px;width:calc(100% + 14px)} .mnMob:not(.no-items) + .mnMen{padding-bottom:100px} .mnMen{padding:20px 15px} .mMenu{margin-bottom:10px} .mMenu >*{display:inline} .mMenu >*:not(:last-child)::after{content:'\00B7';font-size:90%;opacity:.6} .mMenu a:hover{text-decoration:underline} .mSoc >*{position:relative} .mSoc svg{z-index:1} .mSoc svg, .mnMn svg{width:20px;height:20px;opacity:.8} .mSoc span, .mMenu span{opacity:.7} .mNav{display:none;position:relative;max-width:30px} .mNav svg{height:18px;opacity:.7;z-index:1} .mnMn >li{position:relative} .mnMn >li.br::after{content:'';display:block;border-bottom:1px solid var(--contentL);margin:12px 5px} .mnMn li:not(.mr) .a:hover, .mnMn ul li >*:hover{background:var(--transB)} .mnMn li:not(.mr) .a:hover, .mnMn ul li a:hover{color:var(--linkC)} .mnMn li:not(.mr) ul{padding-left:30px} .mnMn li ul{display:none;opacity:0;visibility:hidden} .mnMn ul li >*, .mnMn .a{display:flex;align-items:center;padding:10px 5px;position:relative;width:calc(100% + 10px);left:-5px;right:-5px;border-radius:8px;transition:var(--trans-1)} .mnMn ul li >*{padding:10px} .mnMn li li a >*{-webkit-box-orient:vertical;-webkit-line-clamp:1;display:flex;line-height:20px;overflow:hidden} .mnMn li li a >* svg{margin-right:5px} .mnMn .a >*{margin:0 5px} .mnMn .a:hover svg:not(.d){fill:var(--linkC)} .mnMn .a:hover svg.line:not(.d){fill:none;stroke:var(--linkC)} .mnMn .n, .mnMn ul li >*{display:block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;flex:1 0 calc(100% - 64px)} .mnMn svg{flex-shrink:0} .mnMn svg.d{width:14px;height:14px} .mnMn .drp.mr .a{font-size:13px;padding-bottom:0;opacity:.7} .mnMn .drp.mr svg.d{display:none} .mnMn .drpI:checked ~ .a svg.d{transform:rotate(180deg)} .mnMn .drpI:checked ~ ul{display:block;position:relative;opacity:1;visibility:visible} @media screen and (min-width:897px){.bD:not(.hdMn) .navI:not(:checked) ~ .mainWrp .blogMn .mnMn :not(.mr) ul li >*, .hdMn .navI:checked ~ .mainWrp .blogMn .mnMn :not(.mr) ul li >*{margin-left:5px;overflow:visible;width:calc(100% + 5px)} .bD:not(.hdMn) .navI:not(:checked) ~ .mainWrp .blogMn .drp:not(.mr) ul::before, .hdMn .navI:checked ~ .mainWrp .blogMn .drp:not(.mr) ul::before{border-left:1px solid;content:'';display:block;height:calc(100% - 20px);left:15px;opacity:.2;position:absolute;width:1px} .bD:not(.hdMn) .navI:not(:checked) ~ .mainWrp .blogMn .drp:not(.mr) ul li a::before, .hdMn .navI:checked ~ .mainWrp .blogMn .drp:not(.mr) ul li a::before{border-bottom:1px solid;content:'';display:block;height:1px;left:-15px;opacity:.2;position:absolute;top:17.5px;width:13px} .bD:not(.hdMn) .navI:not(:checked) ~ .mainWrp .blogMn .drp:not(.mr) ul.s li a::before, .hdMn .navI:checked ~ .mainWrp .blogMn .drp:not(.mr) ul.s li a::before{top:20px} .bD:not(.hdMn) .navI:not(:checked) ~ .mainWrp .blogMn .drp:not(.mr) ul:not(.s)::before, .hdMn .navI:checked ~ .mainWrp .blogMn .drp:not(.mr) ul:not(.s)::before{height:calc(100% - 18.5px)} .bD:not(.hdMn) .navI:not(:checked) ~ .mainWrp .blogMn .mnMn li li a:hover svg, .hdMn .navI:checked ~ .mainWrp .blogMn .mnMn li li a:hover svg{fill:var(--linkC)} .bD:not(.hdMn) .navI:not(:checked) ~ .mainWrp .blogMn .mnMn li li a:hover svg.line, .hdMn .navI:checked ~ .mainWrp .blogMn .mnMn li li a:hover svg.line{fill:none;stroke:var(--linkC)} .drK:not(.hdMn) .navI:not(:checked) ~ .mainWrp .blogMn .mnMn li li a:hover svg, .drK.hdMn .navI:checked ~ .mainWrp .blogMn .mnMn li li a:hover svg{fill:var(--darkU)} .drK:not(.hdMn) .navI:not(:checked) ~ .mainWrp .blogMn .mnMn li li a:hover svg.line, .drK.hdMn .navI:checked ~ .mainWrp .blogMn .mnMn li li a:hover svg.line{fill:none;stroke:var(--darkU)} .Rtl:not(.hdMn) .navI:not(:checked) ~ .mainWrp .blogMn .drp ul:before, .Rtl.hdMn .navI:checked ~ .mainWrp .blogMn .drp ul:before{left:unset;right:15px} .Rtl:not(.hdMn) .navI:not(:checked) ~ .mainWrp .blogMn .mnMn :not(.mr) ul li >*, .Rtl.hdMn .navI:checked ~ .mainWrp .blogMn .mnMn :not(.mr) ul li >*{margin-left:0;margin-right:5px} .Rtl:not(.hdMn) .navI:not(checked) ~ .mainWrp .blogMn .drp ul li a:before, .Rtl.hdMn .navI:checked ~ .mainWrp .blogMn .drp ul li a:before{left:unset;right:-15px}} @media screen and (max-width:896px){.bD .navI:checked ~ .mainWrp .blogMn .mnMn :not(.mr) ul li >*{margin-left:5px;overflow:visible;width:calc(100% + 5px)} .bD .navI:checked ~ .mainWrp .blogMn .drp:not(.mr) ul::before{border-left:1px solid;content:'';display:block;height:calc(100% - 20px);left:15px;opacity:.2;position:absolute;width:1px} .bD .navI:checked ~ .mainWrp .blogMn .drp:not(.mr) ul li a::before{border-bottom:1px solid;content:'';display:block;height:1px;left:-15px;opacity:.2;position:absolute;top:17.5px;width:13px} .bD .navI:checked ~ .mainWrp .blogMn .drp:not(.mr) ul.s li a::before{top:20px} .bD .navI:checked ~ .mainWrp .blogMn .mnMn li li a:hover svg{fill:var(--linkC)} .bD .navI:checked ~ .mainWrp .blogMn .mnMn li li a:hover svg.line{fill:none;stroke:var(--linkC)} .drK.bD .navI:checked ~ .mainWrp .blogMn .mnMn li li a:hover svg{fill:var(--darkU)} .drK.bD .navI:checked ~ .mainWrp .blogMn .mnMn li li a:hover svg.line{fill:none;stroke:var(--darkU)} .Rtl .navI:checked ~ .mainWrp .blogMn .drp ul:before{left:unset;right:15px} .Rtl .navI:checked ~ .mainWrp .blogMn .mnMn :not(.mr) ul li >*{margin-left:0;margin-right:5px} .Rtl .navI:checked ~ .mainWrp .blogMn .drp ul li a:before{left:unset;right:-15px}}
/* Mobile Menu */ .mobMn{position:fixed;left:0;right:0;bottom:0; border-top:1px solid var(--mobL);border-radius:var(--mobBr) var(--mobBr) 0 0;background:var(--mobB);color:var(--mobT);padding:0 20px;box-shadow:0 -10px 25px -5px rgba(0,0,0,.1);z-index:2;font-size:12px} .mobMn svg.line{stroke:var(--mobT);opacity:.8} .mobMn ul{height:55px;display:flex;align-items:center;justify-content:center;list-style:none;margin:0;padding:0} .mobMn li{display:flex;justify-content:center;flex:1 0 20%} .mobMn li >*{display:inline-flex;align-items:center;justify-content:center;min-width:35px;height:35px;border-radius:20px;padding:0 8px;transition:var(--trans-1);color:inherit} .mobMn li svg{margin:0 3px;flex-shrink:0} .mobMn li >*::after{content:attr(data-text);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;max-width:0;margin:0;transition:inherit;opacity:.7} .mobMn li >*:hover::after{max-width:70px;margin:0 3px} .mobMn .nmH{opacity:.7} .mobMn li >*:hover{background:var(--mobHv)} .mobMn li >*:hover svg.line{fill:var(--mobT) !important;opacity:.5} .mobMn li >*:hover svg.line .svgC{fill:var(--linkB) !important;stroke:var(--linkB)} /* Style 2 */ .MN-2 .mobMn{font-size:10px} .mobS .mobMn li >*{flex-direction:column;position:relative} .mobS .mobMn li >*:hover{background:transparent} .MN-2 .mobMn li >*::after{max-width:none} /* Style 3 */ .MN-3 .mobMn li >*::after, .MN-4 .mobMn li >*::after{content:'';width:4px;height:4px;border-radius:50%;position:absolute;bottom:-2px;opacity:0} .MN-3 .mobMn li >*:hover::after,  .MN-4 .mobMn li >*:hover::after{background:var(--linkB);opacity:.7} .MN-3 .mobMn li >*:hover svg.line, .MN-4 .mobMn li >*:hover svg.line{stroke:var(--linkB);fill:var(--linkB) !important;opacity:.7} /* Style 4 */ .MN-4 .mobMn{left:15px;right:15px;bottom:15px;padding:0 10px;border-radius:var(--headerR);box-shadow: 0 5px 35px rgba(0,0,0,.1);transition:bottom 1.2s ease;-webkit-transition:bottom 1.2s ease} .MN-4 .mobMn.slide{bottom:-150px;transition:bottom 1.5s ease;-webkit-transition:bottom 1.5s ease}
/* Back to Top */ .toTopB{display:flex;align-items:center;justify-content:center;position:fixed;right:20px;bottom:20px;width:45px;height:45px;border-radius:50%;cursor:pointer;visibility:hidden;opacity:0;z-index:5;transform:scale(0);transition:transform .3s ease, opacity .3s ease,visibility .3s ease,margin-bottom 1s ease} .toTopB.vsbl{visibility:visible;opacity:1;transform:scale(1)} .MN-4 .toTopB{bottom:20px} .toTopB:hover{opacity:.8} .toTopB svg{height:100%;width:100%;-webkit-transform:rotate(-90deg);-ms-transform:rotate(-90deg);transform:rotate(-90deg);stroke-width:1.5;cursor:pointer} .toTopB svg .b{fill:#fff;stroke:#e6e6e6;opacity:.9} .toTopB svg .c{fill:none;stroke:var(--linkC);stroke-dasharray:100 100;stroke-dashoffset:100;stroke-linecap:round} .toTopB svg .d{fill:none;stroke:var(--iconC)} .drK .toTopB svg .b{fill:var(--darkBa);stroke:#404045} .drK .toTopB svg .c{stroke:var(--darkU)} .drK .toTopB svg .d{stroke:var(--darkT)}
/* Footer */ footer{font-size:97%;line-height:1.8em;color:var(--fotT);padding:40px 0 20px} .footer{padding:20px;background:var(--fotB);box-shadow: 0 5px 35px rgba(0,0,0,.1);border-radius:10px} footer .LinkList a, footer .sL li >*{display:inline-block;color:inherit;line-height:20px} footer .LinkList a:hover{text-decoration:underline} footer .LinkList ul:not(.sL) li::before{content:'\2013'; opacity:.3} footer .sL{display:flex;flex-wrap:wrap;align-items:baseline;font-size:13px;opacity:.8} footer .sL li{display:inline-flex;align-items:baseline} footer .sL li:not(:first-child)::before{content:'/';margin:0 5px} footer .sL li >*::before{content:attr(data-text)} .fotIn ul{list-style:none;margin:0;padding:0} .fotIn{display:flex;flex-wrap:wrap;position:relative;width:calc(100% + 30px);left:-15px;right:-15px} .fotIn >*{width:calc(21.666% - 30px);margin:0 15px} .fotIn >*:first-child{width:calc(35% - 30px)} .fotIn .widget{margin-bottom:30px} .fotIn .widget .title{color:inherit;margin-bottom:12px;font-weight:700;font-size:14px} .abtU{max-width:calc(100% - 25px)} .abtU::before{content:attr(data-text);font-size:13px; opacity:.6;display:block;margin-bottom:3px} .abtU >*{align-items:center;display:flex;justify-content:space-between;margin:10px 0} .abtU .pu-views::before{content:'We have served the best performance ' attr(data-text) ' times.';opacity:.8} .abtL{flex:0 0 70px;width:70px;height:70px;background-repeat:no-repeat;background-size:100%;background-position:center;border-radius:10px} .abtT{flex:0 0 calc(100% - 82px)} .abtT .tl{color:inherit; font-size:1.3rem} .abtD{-webkit-box-orient:vertical;-webkit-line-clamp:2;display:-webkit-box;line-height:1.4em;margin:4px 0 0;overflow:hidden}
/* Footer Credit and Backtop */ footer .credit a{display:inline-flex;align-items:center} footer .credit a svg{width:13px;height:13px;margin:0 3px;fill:var(--linkC)} .cdtIn{display:flex;align-items:baseline;justify-content:space-between; position:relative;width:calc(100% + 20px);left:-10px;right:-10px} .cdtIn >*{margin:0 10px} .cdtIn .HTML{overflow:hidden;white-space:nowrap;text-overflow:ellipsis} .fotCd{display:inline-flex} .fotCd .creator{opacity:0} .tTop svg{width:20px;height:20px;stroke:var(--fotT)} .toTop{display:flex;align-items:center; white-space:nowrap} .toTop::before{content:attr(data-text); opacity:.7;margin:0 5px} .toTopF{display:flex;align-items:center;justify-content:center;width:45px;height:45px;border-radius:50%;background:var(--linkB);position:fixed;bottom:20px;right:20px} .toTopF svg{stroke:#fffdfc;stroke-width:2}
/* Wave Animation */ .wvC{position:absolute;bottom:0;width:100%;z-index:-1} .wvS{position:relative} .wvS .waves{position:absolute;bottom:0;width:100%;height:60px;min-height:100px;max-height:150px} .wvH{position:relative;height:0;background:var(--waveB)} .plx > use{fill:var(--waveB);animation:waveMove 25s cubic-bezier(.55,.5,.45,.5) infinite} .plx > use:nth-child(1){opacity:.7;animation-delay:-2s;animation-duration:7s} .plx > use:nth-child(2){opacity:.5;animation-delay:-3s;animation-duration:10s} .plx > use:nth-child(3){opacity:.3;animation-delay:-4s;animation-duration:13s} .plx > use:nth-child(4){opacity:1;animation-delay:-5s;animation-duration:20s} .drK .wvH{background:var(--darkW)} .drK .plx > use{fill:var(--darkW)} @keyframes waveMove{0%{transform: translate3d(-90px,0,0)}100%{transform: translate3d(85px,0,0)}}
/* Article Section */ .onIndx .blogPts, .itemFt .itm{display:flex;flex-wrap:wrap;align-items:center;position:relative; width:calc(100% + 20px);left:-10px;right:-10px} .onIndx .blogPts >*, .itemFt .itm >*{flex:0 0 calc(50% - 20px);width:calc(50% - 20px); margin-bottom:0;margin-left:10px;margin-right:10px} .onIndx .blogPts >*{background:var(--contentB);box-shadow:0 5px 35px rgba(0,0,0,.07);border-radius:10px;margin-bottom:20px;padding:10px 10px 45px;position:relative} .onIndx .blogPts .pTag{padding-bottom:0} .onIndx .pTag .pInf{display:none} .onIndx .blogPts .pInf{position:absolute;bottom:15px;left:15px;right:15px} .onIndx .blogPts{align-items:stretch} .onIndx .blogPts.mty{display:block;width:100%;left:0;right:0} .onIndx .blogPts.mty .noPosts{width:100%;margin:0} .onIndx .blogPts div.ntry{padding-bottom:0;flex:0 0 calc(100% - 20px)} .blogPts .ntry.noAd .widget/*, .Blog ~ .HTML*/{display:none} .cPst .pLbls >*{padding:16px 3px} .cPst .pLbls >*:not(:last-child){padding-right:0} .cPst .pLbls >*:not(:last-child)::after{padding-left:3px} .ctgry article{animation:ctgryFade 1.5s;-webkit-animation:ctgryFade 1.5s} @keyframes ctgryFade{from{opacity:0}to{opacity:1}} @-webkit-keyframes ctgryFade{from{opacity:0}to{opacity:1}}
/* Blog title */ .blogTtl{font-size:14px; margin:0 0 30px;width:calc(100% + 16px);display:flex;justify-content:space-between;position:relative;left:-8px;right:-8px} .blogTtl .t, .blogTtl.hm .title{margin:0 8px;flex-grow:1} .blogTtl .t span{font-weight:400;font-size:90%; opacity:.7} .blogTtl .t span::before{content:attr(data-text)} .blogTtl .t span::after{content:''; margin:0 4px} .blogTtl .t span.hm::after{content:'/'; margin:0 8px}
/* No Post */ .blogPts .noPosts{min-height:120px;display:flex;align-items:center;justify-content:center;padding-top:40px}
/* Thumbnail */ .pThmb{flex:0 0 calc(50% - 12.5px);overflow:hidden;position:relative;border-radius:5px; margin-bottom:20px;background:var(--transB)} .pThmb .thmb{display:block;position:relative;padding-top:52.335%;color:inherit;transition:var(--trans-4);-webkit-transition:var(--trans-4)} article:hover .thmb{transform:scale(1.03);-webkit-transform:scale(1.03)} .pThmb .thmb amp-img{position:absolute;top:50%;left:50%;min-width:100%;min-height:100%;max-height:108%;text-align:center;transform:translate(-50%, -50%)} .pThmb div.thmb span::before{content:attr(data-text); opacity:.7; white-space:nowrap} .pThmb:not(.nul)::before{position:absolute;top:0;right:0;bottom:0;left:0; transform:translateX(-100%); background-image:linear-gradient(90deg, rgba(255,255,255,0) 0, rgba(255,255,255,.3) 20%, rgba(255,255,255,.6) 60%, rgba(255,255,255, 0)); animation:shimmer 2s infinite;content:''} .pThmb.iyt:not(.nul) .thmb::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0; background:rgba(0,0,0,.4) url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'><path d='M4 11.9999V8.43989C4 4.01989 7.13 2.2099 10.96 4.4199L14.05 6.1999L17.14 7.9799C20.97 10.1899 20.97 13.8099 17.14 16.0199L14.05 17.7999L10.96 19.5799C7.13 21.7899 4 19.9799 4 15.5599V11.9999Z'/></svg>") center / 35px no-repeat; opacity:0;transition:var(--trans-1)} .pThmb.iyt:not(.nul):hover .thmb::after{opacity:1}
/* Sponsored */ .iFxd{display:flex;justify-content:flex-end;position:absolute;top:0;left:0;right:0;padding:10px 6px;font-size:13px;line-height:16px} .iFxd.l{right:auto} .Rtl .iFxd.l{right:0;left:auto} .iFxd >*{display:flex;align-items:center;margin:0 5px;padding:5px 2.5px;border-radius:var(--thumbEr);background:var(--contentB);color:inherit;box-shadow:0 8px 25px 0 rgba(0,0,0,.1)} .iFxd >* svg{width:16px;height:16px;stroke-width:1.5;margin:0 2.5px;opacity:.7} .iFxd .cmnt, .iFxd .pV{padding:5px;color:var(--bodyC)} .iFxd .bM{overflow:hidden;cursor:pointer} .iFxd .bM::after{content:attr(aria-label);white-space:nowrap;transition:all .4s cubic-bezier(.18,.89,.32,1.28);width:0;opacity:0;visibility:hidden} .iFxd .bM:hover::after{width:63px;opacity:1;visibility:visible} .iFxd .bM.a::after{content:attr(data-added)} .iFxd .bM.a:hover::after{width:50px} .iFxd .bM:hover{opacity:.8} .iFxd .pV.hidden{display:none} .iFxd .cmnt::after, .iFxd .pV::after{content:attr(data-text);margin:0 2.5px;opacity:.8} .drK .iFxd >* svg.line{stroke:var(--iconC)}
/* Label */ .pLbls::before, .pLbls >*::before{content:attr(data-text)} .pLbls::before{opacity:.7} .pLbls a:hover{text-decoration:underline} .pLbls >*{color:inherit;display:inline;padding:16px 0} .pLbls >*:not(:last-child)::after{content:'/'}
/* Profile Images and Name */ .im{width:35px;height:35px;border-radius:16px; background-color:var(--transB);background-size:100%;background-position:center;background-repeat:no-repeat;display:flex;align-items:center;justify-content:center} .im svg{width:18px;height:18px;opacity:.4} .nm::after{content:attr(data-text)}
/* Title and Entry */ .pTtl{font-size:1.1rem;line-height:1.5em} .pTtl.sml{font-size:1rem} .pTtl.itm{font-size:var(--postT);font-family:var(--fontBa);font-weight:700; line-height:1.3em} .pTtl.itm.nSpr{margin-bottom:30px} .aTtl a:hover{color:var(--linkC)} .aTtl a, .pSnpt{color:inherit; display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden} .pEnt{margin-top:40px; font-size:var(--postF);font-family:var(--fontBa); line-height:1.8em}
/* Snippet, Description, Headers and Info */ .onIndx .pCntn{padding:0 5px} .pHdr{margin-bottom:8px} .pHdr .pLbls{white-space:nowrap;overflow:hidden;text-overflow:ellipsis; opacity:.8} .pSml{font-size:93%} .pSnpt{-webkit-line-clamp:2;margin:12px 0 0;font-family:var(--fontB);font-size:14px;line-height:1.5em; opacity:.8} .pSnpt.nTag{color:var(--linkC);opacity:1;margin-bottom:10px} .pDesc{font-size:16px;line-height:1.5em;margin:8px 0 25px;opacity:.7} .pInf{display:flex;align-items:baseline;justify-content:space-between; margin-top:15px} .pInf.nTm{margin:0} .pInf.nSpr .pJmp{opacity:1} .pInf.nSpr .pJmp::before{content:attr(aria-label)} .pInf.ps{background:var(--contentB);box-shadow:0 0 25px rgba(0,0,0,.07);padding:15px;border-radius:10px;justify-content:flex-start;align-items:center;margin-top:25px;position:relative;width:100%} .drK .pInf.ps{background:var(--darkBa)} .pInf.ps .pTtmp{opacity:1} .pInf.ps .pTtmp::before{content:attr(data-date) ' '} .pInf.ps .pTtmp::after{display:inline} .pInf.ps.nul{display:none} .pInf .pIm{flex-shrink:0;margin-right:8px} .Rtl .pInf .pIm{margin-right:0;margin-left:8px} .pInf .pNm{flex-grow:1;width:calc(100% - 160px);display:inline-flex;flex-wrap:wrap;align-items:baseline} .pInf .pNm.l{display:none} .pInf .pCm{flex-shrink:0;max-width:58px;margin:0 2px} .pInf .pCm.l{max-width:95px} .pInf .pIc{display:inline-flex;justify-content:flex-end;position:relative;width:calc(100% + 10px);left:-5px;right:-5px} .pInf .pIc >*{display:flex;align-items:center;justify-content:center;width:30px;height:30px;position:relative;margin:0 2px;color:inherit} .pInf .pIc svg{width:20px;height:20px;opacity:.8;z-index:1} .pInf .pIc .cmnt::before{content:attr(data-text);font-size:11px;line-height:18px;padding:0 5px;border-radius:10px;background:#e6e6e6;color:var(--bodyC);position:absolute;top:-5px;right:0;z-index:2} .pInf .pDr{opacity:.7;display:inline-block;margin:0 4px;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;max-width:100%} .pInf .pDr >*:not(:first-child)::before{content:'\00B7';margin:0 5px} .pInf a.nm{color:var(--bodyC)} .pInf a.nm:hover::after{text-decoration:underline} .drK .pInf a.nm{color:var(--darkT)} .pInf .pIn{display:inline} .pInf .nm{margin:0 4px} /*.pInf .n .nm::before{content:attr(data-write) ' ';opacity:.7}*/ .pInf .im{width:28px;height:28px} .aTtmp{opacity:.8} .aTtmp, .pJmp{overflow:hidden} .pTtmp::after, .pJmp::before, .iTtmp::before{content:attr(data-text); display:block;line-height:18px; white-space:nowrap;text-overflow:ellipsis;overflow:hidden} .pJmp{display:inline-flex;align-items:center; opacity:0; transition:var(--trans-2)} .pJmp::before{content:attr(aria-label)} .pJmp svg{height:18px;width:18px;stroke:var(--linkC); flex-shrink:0} .ntry:hover .pJmp, .itm:hover .pJmp{opacity:1} .ntry:not(.noAd) .pJmp, .itemFt .itm .pJmp{animation:indicator 2s 3s infinite} .ntry:not(.noAd):hover .pJmp, .itemFt:hover .itm .pJmp{animation:none}
/* Product view */ .pTag .pPad{padding:10px 0} .pTag .pPric{font-size:20px;color:var(--linkC);padding-top:20px} .pTag .pPric::before, .pTag .pInfo small{content:attr(data-text);font-size:small;opacity:.8;display:block;line-height:1.5em;color:var(--bodyC)} .pTag .pInfo{font-size:14px;line-height:1.6em} .pTag .pInfo:not(.o){position:relative;width:calc(100% + 20px);left:-10px;right:-10px;display:flex} .pTag .pInfo:not(.o) >*{width:50%;padding:0 10px} .pTag .pMart{margin:10px 0 12px;display:flex;flex-wrap:wrap;line-height:1.6em; position:relative;width:calc(100% + 8px);left:-4px;right:-4px} .pTag .pMart >*{margin:0 4px} .pTag .pMart small{width:calc(100% - 8px);margin-bottom:10px} .pTag .pMart a{display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border:1px solid var(--contentL);border-radius:12px;margin-bottom:8px} .pTag .pMart img{width:20px;display:block}
/* Blog pager */ .blogPg{display:flex;flex-wrap:wrap;justify-content:center;font-size:90%;font-family:var(--fontB);line-height:20px;color:#fffdfc;margin:25px 0 50px;max-width:100%} .blogPg >*{display:flex;justify-content:center;align-items:center;min-width:40px;padding:10px 13px;margin:5px;color:inherit;background:var(--linkB);border-radius:var(--buttonR);box-shadow:rgba(100, 100, 111, 0.2) 0px 7px 29px 0px} .blogPg >* svg{width:18px;height:18px; stroke:var(--darkT); stroke-width:1.5} .blogPg >*::before{content:attr(data-text)} .blogPg .jsLd{margin-left:auto;margin-right:auto} .blogPg .nwLnk::before, .blogPg .jsLd::before{display:none} .blogPg .nwLnk::after, .blogPg .jsLd::after{content:attr(data-text); margin:0 8px} .blogPg .olLnk::before{margin:0 8px} .blogPg .nPst, .blogPg .current{background:var(--contentL); color:var(--bodyCa)} .blogPg .nPst.jsLd svg{fill:var(--darkTa);stroke:var(--darkTa)} .blogPg .nPst svg.line{stroke:var(--darkTa)}
/* Breadcrumb */ .brdCmb{margin-bottom:5px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap} .brdCmb a{color:var(--bodyC)} .brdCmb >*:not(:last-child)::after{content:'/'; margin:0 4px;font-size:90%;opacity:.6} .brdCmb >*{display:inline} .brdCmb .tl::before{content:attr(data-text)} .brdCmb .hm a{font-size:90%;opacity:.7}
/* Article Style */ .pS h1, .pS h2, .pS h3, .pS h4, .pS h5, .pS h6{margin:1.5em 0 18px; font-family:var(--fontBa);font-weight:700; line-height:1.5em} .pS h1:target, .pS h2:target, .pS h3:target, .pS h4:target, .pS h5:target, .pS h6:target{padding-top:var(--headerH);margin-top:0}
/* Paragraph */ .pS p{margin:1.7em 0} .pIndent{text-indent:2.5rem} .onItm:not(.Rtl) .dropCap{float:left;margin:4px 8px 0 0; font-size:55px;line-height:45px;opacity:.8} .pS hr{margin:3em 0; border:0} .pS hr::before{content:'\2027 \2027 \2027'; display:block;text-align:center; font-size:24px;letter-spacing:0.6em;text-indent:0.6em;opacity:.8;clear:both} .pRef{display:block;font-size:14px;line-height:1.5em; opacity:.7; word-break:break-word}
/* Img and Ad */ .pS img{display:inline-block;border-radius:5px;height:auto !important} .pS img.full{display:block !important; margin-bottom:10px; position:relative; width:100%;max-width:none} .pS .widget, .ps .pAd >*{margin:40px 0}
/* Note */ .note{position:relative;padding:16px 20px 16px 50px;background:var(--notifU);color:#3c4043; font-size:.85rem;font-family:var(--fontB);line-height:1.6em;border-radius:10px;overflow:hidden} .note::before{content:'';width:60px;height:60px;background:rgba(0,0,0,.4);display:block;border-radius:50%;position:absolute;top:-12px;left:-12px;opacity:.1} .note::after{content:'\002A';position:absolute;left:18px;top:16px; font-size:20px; min-width:15px;text-align:center} .note.wr{background:#ffdfdf;color:#48525c} .note.wr::after{content:'\0021'} .drK .note{background:var(--darkBs);color:rgba(255,255,255,.9)}
/* Ext link */ .extL{display:inline-flex;align-items:center} .extL::after{content:''; width:14px;height:14px; display:inline-block;margin:0 5px; background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23989b9f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><path d='M13 11L21.2 2.80005'/><path d='M22 6.8V2H17.2'/><path d='M11 2H9C4 2 2 4 2 9V15C2 20 4 22 9 22H15C20 22 22 20 22 15V13'/></svg>") center / 14px no-repeat} .extL.alt::after{background-image:url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23989b9f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><path d='M15 7h3a5 5 0 0 1 5 5 5 5 0 0 1-5 5h-3m-6 0H6a5 5 0 0 1-5-5 5 5 0 0 1 5-5h3'/><line x1='8' y1='12' x2='16' y2='12'/></svg>")}
/* Scroll img */ .psImg{display:flex;flex-wrap:wrap;align-items:flex-start;justify-content:center; margin:2em 0; position:relative;left:-7px;right:-7px; width:calc(100% + 14px)} .psImg >*{width:calc(50% - 14px); margin:0 7px 14px; position:relative} .psImg img{display:block} .scImg >*{width:calc(33.3% - 14px); margin:0 7px} .btImg label{position:absolute;top:0;left:0;right:0;bottom:0; border-radius:5px; display:flex;align-items:center;justify-content:center; background:rgba(0,0,0,.6); transition:var(--trans-1); -webkit-backdrop-filter:saturate(180%) blur(10px); backdrop-filter:saturate(180%) blur(10px); color:var(--darkT); font-size:13px;font-family:var(--fontB)} .hdImg .shImg{width:100%;margin:0; left:0;right:0; transition:var(--trans-1); max-height:0;opacity:0;visibility:hidden} .inImg:checked ~ .hdImg .shImg{max-height:1000vh;opacity:1;visibility:visible} .inImg:checked ~ .hdImg .btImg label{opacity:0;visibility:hidden}
/* Post related */ .pRelate{margin:40px 0;padding:20px 0; border:1px solid #989b9f;border-left:0;border-right:0; font-size:14px;line-height:1.8em} .pRelate a, .drK .pRelate a{color:inherit} .pRelate a:hover{text-decoration:underline} .pRelate b{font-weight:400; margin:0;opacity:.8} .pRelate ul, .pRelate ol{margin:8px 0 0;padding:0 20px}
/* Blockquote */ blockquote, .cmC i[rel=quote]{position:relative;font-size:.97rem; opacity:.8;line-height:1.6em;margin-left:0;margin-right:0;padding:5px 20px;border-left:2px solid var(--contentL)} blockquote.s-1, details.sp{font-size:.93rem; padding:25px 25px 25px 45px; border:1px solid #989b9f;border-left:0;border-right:0;line-height:1.7em} blockquote.s-1::before{content:'\201D';position:absolute;top:10px;left:0; font-size:60px;line-height:normal;opacity:.5}
/* Table */ .ps table{margin:0 auto; font-size:14px;font-family:var(--fontB)} .ps table:not(.tr-caption-container){min-width:90%;border:1px solid var(--contentL);border-radius:3px;overflow:hidden} .ps table:not(.tr-caption-container) td{padding:16px} .ps table:not(.tr-caption-container) tr:not(:last-child) td{border-bottom:1px solid var(--contentL)} .ps table:not(.tr-caption-container) tr:nth-child(2n+1) td{background:rgba(0,0,0,.01)} .ps table th{padding:16px; text-align:inherit; border-bottom:1px solid var(--contentL)} .ps .table{display:block; overflow-y:hidden;overflow-x:auto;scroll-behavior:smooth}
/* Img caption */ figure{margin-left:0;margin-right:0} .ps .tr-caption, .psCaption, figcaption{display:block; font-size:14px;line-height:1.6em; font-family:var(--fontB);opacity:.7}
/* Syntax */ .pre{background:var(--synxBg);color:var(--synxC); direction: ltr} .pre:not(.tb){position:relative;border-radius:3px;overflow:hidden;margin:1.7em auto;font-family:var(--fontC)} .pre pre{margin:0;color:inherit;background:inherit} .pre:not(.tb)::before, .cmC i[rel=pre]::before{content:'</>';display:flex;justify-content:flex-end;position:absolute;right:0;top:0;width:100%;background:inherit;color:var(--synxGray);font-size:10px;padding:0 10px;z-index:2;line-height:30px} .pre:not(.tb).html::before{content:'.html'} .pre:not(.tb).css::before{content:'.css'} .pre:not(.tb).js::before{content:'.js'} .pre:not(.tb):hover::before{content:'Double click to copy | </>'} .pre:not(.tb).html:hover::before{content:'Double click to copy | .html'} .pre:not(.tb).css:hover::before{content:'Double click to copy | .css'} .pre:not(.tb).js:hover::before{content:'Double click to copy | .js'} .pre[data-text]:not([data-text='']):not(.tb)::before{content:attr(data-text)} .pre[data-text]:not([data-text='']):not(.tb):hover::before{content:'Double Click to Copy | ' attr(data-text)} pre, .cmC i[rel=pre]{display:block;position:relative;font-family:var(--fontC);font-size:13px;line-height:1.6em;border-radius:3px;background:var(--synxBg);color:var(--synxC);padding:30px 20px 20px;margin:1.7em auto; -moz-tab-size:2;tab-size:2;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none; overflow:auto;direction:ltr;white-space:pre} pre i{font-style:normal} pre i.block{color:#fff;background:var(--synxBlue)} pre i.green{color:var(--synxGreen)} pre i.gray{color:var(--synxGray)} pre i.red{color:var(--synxOrange)} pre i.blue{color:var(--synxBlue)} code{display:inline;padding:5px;font-size:14px;border-radius:3px;line-height:inherit;color:var(--synxC);background:#f2f3f5;font-family:var(--fontC)}
/* Multi syntax */ .pre.tb{border-radius:5px} .pre.tb pre{margin:0;background:inherit} .pre.tb .preH{font-size:13px;border-color:rgba(0,0,0,.05);margin:0} .pre.tb .preH >*{padding:13px 20px} .pre.tb .preH::after{content:'</>';font-size:10px;font-family:var(--fontC);color:var(--synxGray);padding:15px;margin-left:auto} .pre.tb >:not(.preH){display:none} .pS input[id*="1"]:checked ~ div[class*="C-1"], .pS input[id*="2"]:checked ~ div[class*="C-2"], .pS input[id*="3"]:checked ~ div[class*="C-3"], .pS input[id*="4"]:checked ~ div[class*="C-4"]{display:block}
/* ToC */ .pS details summary{list-style:none;outline:none} .pS details summary::-webkit-details-marker{display:none} details.sp{padding:16px 20px;background:var(--contentB);box-shadow:0 5px 35px rgba(0,0,0,.07);border:0;border-radius:10px;} details.sp summary{display:flex;justify-content:space-between;align-items:baseline} details.sp summary::after{content:attr(data-show);padding:4px 10px;background:var(--linkB);color:#fffdfc;font-size:12px;border-radius:var(--buttonR);cursor:pointer} details.sp[open] summary::after{content:attr(data-hide)} details.toc a:hover{text-decoration:underline} details.toc a{display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden;color:inherit} details.toc ol, details.toc ul{padding:0 20px; list-style-type:decimal} details.toc li ol, details.toc li ul{margin:5px 0 10px; list-style-type:lower-alpha}
/* Accordion */ .showH{margin:1.7em 0;font-size:.93rem;font-family:var(--fontB);line-height:1.7em} details.ac{padding:18px 15px;background:var(--contentB);box-shadow:0 5px 35px rgba(0,0,0,.07);margin:20px 0;border-radius:10px} details.ac summary{font-weight:700;cursor:default; display:flex;align-items:baseline; transition:var(--trans-1);cursor:pointer} details.ac summary::before{content:'\203A'; flex:0 0 25px;display:flex;align-items:center;justify-content:flex-start;padding:0 5px; font-weight:400;font-size:1.33rem;color:inherit} details.ac[open] summary{color:var(--linkC)} details.ac:not(.alt)[open] summary::before{transform:rotate(90deg);padding:0 0 0 5px;justify-content:center} details.ac.alt summary::before{content:'\002B'; padding:0 2px} details.ac.alt[open] summary::before{content:'\2212'} details.ac .aC{padding:0 15px;opacity:.9} .drK details.sp, .drK details.ac{background:var(--darkBs)} .drK details.sp summary::after{background:var(--darkU)}
/* Tabs */ .tbHd{display:flex; border-bottom:1px solid var(--contentL);margin-bottom:30px;font-size:14px;font-family:var(--fontB);line-height:1.6em; overflow-x:scroll;overflow-y:hidden;scroll-behavior:smooth;scroll-snap-type:x mandatory; -ms-overflow-style:none;-webkit-overflow-scrolling:touch} .tbHd >*{padding:12px 15px; border-bottom:1px solid transparent; transition:var(--trans-1);opacity:.6;white-space:nowrap; scroll-snap-align:start} .tbHd >*::before{content:attr(data-text)} .tbCn >*{display:none;width:100%} .tbCn >* p:first-child{margin-top:0} .pS input[id*="1"]:checked ~ .tbHd label[for*="1"], .pS input[id*="2"]:checked ~ .tbHd label[for*="2"], .pS input[id*="3"]:checked ~ .tbHd label[for*="3"], .pS input[id*="4"]:checked ~ .tbHd label[for*="4"]{border-color:var(--linkB);opacity:1} .pS input[id*="1"]:checked ~ .tbCn div[class*="Text-1"], .pS input[id*="2"]:checked ~ .tbCn div[class*="Text-2"], .pS input[id*="3"]:checked ~ .tbCn div[class*="Text-3"], .pS input[id*="4"]:checked ~ .tbCn div[class*="Text-4"]{display:block} .tbHd.stick{position:-webkit-sticky;position:sticky;top:var(--headerH);background:var(--bodyB)}
/* Split */ .ps .blogPg{font-size:13px; justify-content:center; position:relative;width:calc(100% + 8px);left:-4px;right:-4px} .ps .blogPg >*{padding:8px 15px;margin:0 4px 8px}
/* Button */ .button{display:inline-flex;align-items:center; margin:10px 0;padding:12px 15px;outline:0;border:0; border-radius:var(--buttonR);line-height:20px; color:#fffdfc; background:var(--linkB); font-size:14px;font-family:var(--fontB); white-space:nowrap;overflow:hidden;max-width:320px} .button.ln{color:inherit;background:transparent; border:1px solid var(--bodyCa)} .button.ln:hover{border-color:var(--linkB);box-shadow:0 0 0 1px var(--linkB) inset} .btnF{display:flex;justify-content:center; margin:10px 0;width:calc(100% + 12px);left:-6px;right:-6px;position:relative} .btnF >*{margin:0 6px}
/* Download btn */ .dlBox{max-width:500px;background:#f1f1f0;border-radius:10px;padding:12px;margin:1.7em 0; display:flex;align-items:center; font-size:14px} .dlBox .fT{flex-shrink:0;display:flex;align-items:center;justify-content:center; width:45px;height:45px; padding:10px; background:rgba(0,0,0,.1);border-radius:var(--buttonR)} .dlBox .fT::before{content:attr(data-text);opacity:.7} .dlBox .fT.lazy{background-size:cover;background-position:center;background-repeat:no-repeat} .dlBox .fT.lazy::before{display:none} .dlBox a{flex-shrink:0;margin:0;padding:10px 12px;border-radius:var(--buttonR);font-size:13px} .dlBox a::after{content:attr(aria-label)} .dlBox .fN{flex-grow:1; width:calc(100% - 200px);padding:0 15px} .dlBox .fN >*{display:block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis} .dlBox .fS{line-height:16px;font-size:12px;opacity:.8} .dldCo{display:flex;align-items:center;justify-content:center;max-width:480px;background:rgba(0,0,0,.03);border-radius:10px;margin:30px 0;transition:margin .6s ease;-webkit-transition:margin .6s ease} .dldCo::before{content:'Generating Download Link...';position:absolute;z-index:1;font-size:13px;font-family:var(--fontB);opacity:.8} .dldBx{visibility:hidden;opacity:0;transition:opacity .3s ease;-webkit-transition:opacity .3s ease} .dldSl{visibility:hidden;opacity:0;transition:opacity .3s ease,bottom .6s ease;-webkit-transition:opacity .3s ease,bottom .6s ease} .drK .dldCo{background:var(--darkBa)}
/* Icon btn */ .icon{flex-shrink:0;display:inline-flex} .icon::before{content:'';width:18px;height:18px;background-size:18px;background-repeat:no-repeat;background-position:center} .icon::after{content:'';padding:0 6px} .icon.dl::before, .drK .button.ln .icon.dl::before{background-image:url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fefefe' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5'><polyline points='8 17 12 21 16 17'/><line x1='12' y1='12' x2='12' y2='21'/><path d='M20.88 18.09A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.29'/></svg>")} .icon.demo::before{background-image:url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fefefe' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5'><path d='M7.39999 6.32003L15.89 3.49003C19.7 2.22003 21.77 4.30003 20.51 8.11003L17.68 16.6C15.78 22.31 12.66 22.31 10.76 16.6L9.91999 14.08L7.39999 13.24C1.68999 11.34 1.68999 8.23003 7.39999 6.32003Z'/><path d='M10.11 13.6501L13.69 10.0601'/></svg>")} .button.ln .icon.dl::before{background-image:url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2308102b' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5'><polyline points='8 17 12 21 16 17'/><line x1='12' y1='12' x2='12' y2='21'/><path d='M20.88 18.09A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.29'/></svg>")}
/* Article Style Responsive */ @media screen and (max-width: 640px){.pS img.full{width:calc(100% + 40px);left:-20px;right:-20px; border-radius:0} .note{font-size:13px} .scImg{flex-wrap:nowrap;justify-content:flex-start;position:relative;width:calc(100% + 40px);left:-20px;right:-20px;padding:0 13px; overflow-y:hidden;overflow-x:scroll;scroll-behavior:smooth;scroll-snap-type:x mandatory; -ms-overflow-style:none;-webkit-overflow-scrolling:touch} .scImg >*{flex:0 0 80%;scroll-snap-align:center} .ps .table{position:relative; width:calc(100% + 40px);left:-20px;right:-20px;padding:0 20px; display:flex}} @media screen and (max-width:500px){.hdImg{width:100%;left:0;right:0} .hdImg >*, .shImg >*{width:100%;margin:0 0 16px} .ps .tr-caption, .psCaption, figcaption{font-size:13px} .btnF >*{flex-grow:1;justify-content:center}.btnF >*:first-child{flex:0 0 auto} .dlBox a{width:42px;height:42px;justify-content:center} .dlBox a::after, .dlBox .icon::after{display:none}}
/* Author profile */ .admAbt{padding-top:30px} .admPs{display:flex; max-width:480px;margin:30px 0; padding:12px 12px 15px; background:var(--contentB);border-radius:8px; box-shadow:0 10px 25px -3px rgba(0,0,0,.1)} .admIm{flex-shrink:0; padding:5px 0 0} .admIm .im{width:34px;height:34px} .admI{flex-grow:1; width:calc(100% - 34px);padding:0 12px} .admN::before{content:attr(data-write) ' '; opacity:.7;font-size:90%} .admN::after{content:attr(data-text)} .admA{margin:5px 0 0; font-size:90%; opacity:.9;line-height:1.5em; /*display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden*/}
/* Share btn */ .pSh{margin:15px 0;padding:18px 0;border-bottom:1px solid rgba(0,0,0,.05)} .pShc{display:flex;align-items:center;flex-wrap:wrap; position:relative;width:calc(100% + 18px);left:-9px;right:-9px;font-size:13px} .pShc::before{content:attr(data-text);margin:0 9px;flex-shrink:0} .pShc >*{margin:0 5px; display:flex;align-items:center; color:inherit;padding:12px;border-radius:var(--buttonR);background:#f1f1f0} .pShc .c{color:#fffdfc} .pShc .c svg{fill:#fffdfc} .pShc .c::after{content:attr(aria-label)} .pShc .fb{background:#1778F2} .pShc .wa{background:#128C7E} .pShc .tw{background:#1DA1F2} .pShc a::after{content:attr(data-text);margin:0 3px} .pShc svg, .cpL svg{width:18px;height:18px; margin:0 3px} .shL{position:relative;width:calc(100% + 20px);left:-10px;right:-10px;margin-bottom:20px;display:flex;flex-wrap:wrap;justify-content:center} .shL >*{margin:0 10px 20px;text-align:center} .shL >*::after{content:attr(data-text);font-size:90%;opacity:.7;display:block} .shL a{display:flex;align-items:center;justify-content:center;flex-wrap:wrap; width:65px;height:65px; color:inherit;margin:0 auto 5px;padding:8px;border-radius:26px;background:#f1f1f0} .shL svg{opacity:.8} .cpL{padding-bottom:15px} .cpL::before{content:attr(data-text);display:block;margin:0 0 15px;opacity:.8} .cpL svg{margin:0 4px;opacity:.7} .cpL input{border:0;outline:0; background:transparent;color:rgba(8,16,43,.4); padding:18px 8px;flex-grow:1} .cpL label{color:var(--linkC);display:flex;align-items:center;align-self:stretch; flex-shrink:0;padding:0 8px} .cpLb{display:flex;align-items:center;position:relative;background:#f1f1f0;border-radius:4px 4px 0 0;border-bottom:1px solid rgba(0,0,0,.25); padding:0 8px} .cpLb:hover{border-color:rgba(0,0,0,.42);background:#ececec} .cpLn span{display:block;padding:5px 14px 0;font-size:90%;color:#2e7b32; transition:var(--trans-1);animation:fadein 2s ease forwards; opacity:0;height:22px} /* Label Hashtags */ .lbHt{position:relative;display:block;width:calc(100% + 40px);right:-20px;left:-20px;padding:8px 17px 20px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis} .lbHt >*{color:var(--bodyC);padding:9px 12px;background:var(--contentB);font-family:var(--fontVa);font-size:13px;border-radius:var(--greetR);box-shadow:3px 6px 15px rgba(0,0,0,.07);display:inline-flex;margin:0 3px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;opacity:.8} .drK .lbHt >*{background:var(--darkBa);color:var(--darkT)} .Rtl .lbHt{text-align:right} .Rtl .lbHt{margin-right:0;margin-left:5px}
/* Widget Style */ .widget .imgThm{display:block;position:absolute;top:50%;left:50%;max-width:none;max-height:108%; font-size:12px;text-align:center; transform:translate(-50%, -50%)} .widget .title{margin:0 0 25px; font-size:var(--widgetT);font-weight:var(--widgetTw);position:relative} .widget .title.dt::before{position:absolute;top:0;right:0;content:'';width:20px;height:20px;display:inline-block;opacity:.3;background:url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 276.167 276.167' fill='%23989b9f'><path d='M33.144,2.471C15.336,2.471,0.85,16.958,0.85,34.765s14.48,32.293,32.294,32.293s32.294-14.486,32.294-32.293 S50.951,2.471,33.144,2.471z'/><path d='M137.663,2.471c-17.807,0-32.294,14.487-32.294,32.294s14.487,32.293,32.294,32.293c17.808,0,32.297-14.486,32.297-32.293 S155.477,2.471,137.663,2.471z'/><path d='M243.873,67.059c17.804,0,32.294-14.486,32.294-32.293S261.689,2.471,243.873,2.471s-32.294,14.487-32.294,32.294 S226.068,67.059,243.873,67.059z'/><path d='M243.038,170.539c17.811,0,32.294-14.483,32.294-32.293c0-17.811-14.483-32.297-32.294-32.297 s-32.306,14.486-32.306,32.297C210.732,156.056,225.222,170.539,243.038,170.539z'/><path d='M136.819,170.539c17.804,0,32.294-14.483,32.294-32.293c0-17.811-14.478-32.297-32.294-32.297 c-17.813,0-32.294,14.486-32.294,32.297C104.525,156.056,119.012,170.539,136.819,170.539z'/><path d='M243.771,209.108c-17.804,0-32.294,14.483-32.294,32.294c0,17.804,14.49,32.293,32.294,32.293 c17.811,0,32.294-14.482,32.294-32.293S261.575,209.108,243.771,209.108z'/></svg>") center / 20px no-repeat} .Rtl .widget .title::before{right:auto;left:0;transform:rotate(-90deg);-webkit-transform:rotate(-90deg)} .widget .title::after{content:'';display:inline-block;vertical-align:middle; width:var(--widgetTa); margin:0 10px;border-bottom:1px solid var(--widgetTac); opacity:.5} .widget input[type=text], .widget input[type=email], .widget textarea{display:block;width:100%;outline:0;border:0;border-bottom:1px solid rgba(0,0,0,.25);border-radius:4px 4px 0 0;background:#f3f3f4; padding:25px 16px 8px 16px; line-height:1.6em; transition:var(--trans-1)} .widget input[type=text]:hover, .widget input[type=email]:hover, .widget textarea:hover{border-color:rgba(0,0,0,.42);background:#ececec} .widget input[type=text]:focus, .widget input[type=email]:focus, .widget textarea:focus, .widget input[data-text=fl], .widget textarea[data-text=fl]{border-color:var(--linkB);background:#ececec} .widget input[type=button], .widget input[type=submit]{display:inline-flex;align-items:center; padding:12px 30px; outline:0;border:0;border-radius:4px; color:#fffdfc; background:var(--linkB); font-size:14px; white-space:nowrap;overflow:hidden;max-width:100%} .widget input[type=button]:hover, .widget input[type=submit]:hover{opacity:.7}
/* Widget BlogSearch */ .BlogSearch{position:fixed;top:0;left:0;right:0;z-index:12} .BlogSearch form{position:relative;min-width:320px} .BlogSearch input{position:relative;display:block;background:var(--srchB);border:0;outline:0;margin-top:-100%;padding:10px 55px;width:100%;height:72px;transition:var(--trans-1);z-index:2;border-radius:0 0 var(--srchMr) var(--srchMr)} .BlogSearch input:focus{margin-top:0;box-shadow:0 10px 40px rgba(0,0,0,.2)} .BlogSearch input:focus ~ button.sb{opacity:.9} .BlogSearch .sb{position:absolute;left:0;top:0;display:flex;align-items:center;padding:0 20px;z-index:3;opacity:.7;height:100%;background:transparent;border:0;outline:0} .BlogSearch .sb svg{width:18px;height:18px;stroke:var(--srchI)} .BlogSearch button.sb{left:auto;right:0;opacity:0;font-size:13px} .BlogSearch button.sb::before{content:'\2715'} @media screen and (min-width:897px){header .BlogSearch{position:static;z-index:1} header .BlogSearch input{margin-top:0;padding:12px 42px;height:auto;font-size:13px;border-radius:var(--srchDr);background:rgba(0,0,0,.03); width:calc(100% + 26px);left:-13px;right:-13px;transition:var(--trans-2)} header .BlogSearch input:hover{background:var(--transB)} header .BlogSearch input:focus{box-shadow:none;margin-top:0; background:var(--transB)} header .BlogSearch .sb{padding:0} header .BlogSearch .fCls{display:none}}
/* Widget Profile */ .prfI:checked ~ .mainWrp .wPrf{top:0;opacity:1;visibility:visible} .prfI:checked ~ .mainWrp .wPrf ~ .fCls{z-index:3;opacity:1;visibility:visible} .wPrf{display:flex;position:absolute;top:-5px;right:0;background:var(--contentB);border-radius:16px 5px 16px 16px;width:260px;max-height:400px;box-shadow:0 10px 25px -3px rgba(0,0,0,.1);transition:var(--trans-1);z-index:4;opacity:0;visibility:hidden;overflow:hidden} .wPrf .prfS{background:inherit} .wPrf.tm .im{width:39px;height:39px;flex-shrink:0} .wPrf.sl .im{width:60px;height:60px;border-radius:26px;margin:0 auto} .wPrf.sl .prfC{text-align:center} .prfH .c{display:none} .prfL{display:flex;align-items:center;position:relative;width:calc(100% + 16px);left:-8px;right:-8px;border-radius:8px;padding:8px 0;transition:var(--trans-1)} .prfL::after{content:attr(data-text);margin:0 2px} .prfL >*{margin:0 8px;flex-shrink:0} a.prfL:hover{background:var(--transB)} .sInf{margin-bottom:0} .sInf .sDt .l{display:inline-flex;align-items:center} .sInf .sTxt{margin:5px auto 0;max-width:320px;font-size:93%;opacity:.9;line-height:1.5em} .sInf .sTxt a{text-decoration:underline} .sInf .lc{display:flex;justify-content:center;margin:10px 0 0;opacity:.8;font-size:90%} .sInf .lc svg{width:16px;height:16px} .sInf .lc::after{content:attr(data-text);margin:0 4px}
/* Widget Scroll Menu */ .navS{background:var(--navB);overflow-y:hidden;overflow-x:scroll;scroll-behavior:smooth;scroll-snap-type:x mandatory; -ms-overflow-style:none;-webkit-overflow-scrolling:touch; display:flex;padding:0;border-radius:10px} .navS ul{display:flex;list-style:none;margin:0;padding:0;white-space:nowrap} .navS li{margin:0 15px;padding:10px 0;position:relative;scroll-snap-align:start} .navS li:first-child{margin-left:auto} .navS li:last-child{margin-right:auto} .navS .l{display:block;color:var(--bodyC);padding:8px 0} .navS .l::before{content:attr(data-text)} .navS .l::after{content:'';height:1px;border-radius:2px 2px 0 0;background:var(--linkC);position:absolute;bottom:0;left:0;right:0;opacity:0} .navS span.l{opacity:.7} .navS a.l:hover, .navS .l.a{color:var(--linkC)} @media screen and (max-width:896px){.navS .secIn{padding:0} .navS li{margin:0;padding:8px 0;display:flex} .navS li::before{content:'';padding:10px} .navS ul::after{content:'';display:block;padding:10px;scroll-snap-align:start} .navS .l{position:relative} .navS .l::after{bottom:-8px} .navS a.l:hover::after, .navS .l.a::after{opacity:1}} @media screen and (max-width:500px){.navS{font-size:13px}} .drK .navS{background:var(--darkBs)} .drK .navS .l{color:var(--darkC)} .drK .navS a.l:hover, .drK .navS .l.a{color:var(--darkU)} .drK .navS .l::after{background:var(--darkU)}
/* Widget FeaturedPost */ @media screen and (min-width:501px){.FeaturedPost .itemFt{position:relative;overflow:hidden;padding:10px;background:var(--contentB);box-shadow:0 5px 35px rgba(0,0,0,.07);border-radius:10px} .drK .FeaturedPost .itemFt{background:var(--darkBa)} .FeaturedPost .itemFt::after{content:'';position: absolute;right:0;top:0;width:40px;height:15px;background:var(--linkB);border-radius:0 0 0 20px;opacity:.2}} .itemFt .itm >*{flex:0 0 310px;width:310px} .itemFt .itm >*:last-child{flex:1 0 calc(100% - 310px - 40px);width:calc(100% - 310px - 40px)}
/* Widget PopularPosts */ .PopularPosts{padding:20px 20px 30px;background:var(--contentB);box-shadow:0 5px 35px rgba(0,0,0,.07);border-radius:10px} .itemPp{counter-reset:p-cnt} .itemPp .iCtnt{display:flex} .itemPp >*:not(:last-child){margin-bottom:25px} .itemPp .iCtnt::before{flex-shrink:0;content:'0' counter(p-cnt);counter-increment:p-cnt;width:25px;opacity:.6;font-size:85%;line-height:1.8em} .iInr{flex:1 0;width:calc(100% - 25px)} .iTtl{font-size:.95rem;font-weight:700;line-height:1.5em} .iTtmp{display:inline-flex} .iTtmp::after{content:'\2014';margin:0 5px; color:var(--widgetTac);opacity:.7} .iInf{margin:0 25px 8px; overflow:hidden;white-space:nowrap;text-overflow:ellipsis} .iInf .pLbls{display:inline;opacity:.8}
/* Widget Label */ /* List Label */ .Label{padding:20px;background:var(--contentB);box-shadow:0 5px 35px rgba(0,0,0,.07);border-radius:10px} .wL ul{display:flex;flex-wrap:wrap; list-style:none;margin:0;padding:0; position:relative;width:calc(100% + 30px);left:-15px;right:-15px; font-size:13px} .wL li{width:calc(50% - 10px); margin:0 5px} .wL li >*{display:flex;align-items:baseline;justify-content:space-between; color:var(--bodyC);width:100%; padding:8px 10px;border-radius:4px;line-height:20px} .wL li >* svg{width:18px;height:18px;opacity:.8} .wL li >*:hover svg, .wL li >div svg{/*fill:var(--linkC) !important;*/stroke:var(--linkC)} .wL li >*:hover .lbC, .wL li >div .lbC{color:var(--linkC)} .wL .lbR{display:inline-flex;align-items:center} .wL .lbR .lbC{margin:0 5px} .wL .lbAl{max-height:0; overflow:hidden; transition:var(--trans-4)} .wL .lbM{display:inline-block; margin-top:10px;line-height:20px; color:var(--linkC);cursor:pointer} .wL .lbM::before{content:attr(data-show)} .wL .lbM::after, .wL .lbC::after{content:attr(data-text)} .wL .lbM::after{margin:0 8px} .wL .lbT{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;opacity:.7} .wL .lbC, .wL .lbM::after{flex-shrink:0;font-size:12px;opacity:.7} .lbIn:checked ~ .lbAl{max-height:1000vh} .lbIn:checked ~ .lbM::before{content:attr(data-hide)} .lbIn:checked ~ .lbM::after{visibility:hidden} .wL.bg ul{width:calc(100% + 10px);left:-5px;right:-5px} .wL.bg li{margin-bottom:10px} .wL.bg li >*{background:#f6f6f6} /* Cloud Label */ .wL.cl{display:flex;flex-wrap:wrap} .wL.cl >*, .wL.cl .lbAl >*{display:block;max-width:100%} .wL.cl .lbAl{display:flex;flex-wrap:wrap} .wL.cl .lbC::before{content:'';margin:0 4px;flex:0 0} .wL.cl .lbN{display:flex;justify-content:space-between; margin:0 0 8px;padding:9px 13px; border:1px solid var(--contentL);border-radius:3px; color:inherit;line-height:20px} .wL.cl .lbN:hover .lbC, .wL.cl div.lbN .lbC{color:var(--linkB); opacity:1} .wL.cl .lbN:not(div):hover, .wL.cl div.lbN{border-color:var(--linkB)} .wL.cl .lbSz{display:flex} .wL.cl .lbSz::after{content:'';margin:0 4px;flex:0 0}
/* Widget ContactForm */ .ContactForm{max-width:500px; font-family:var(--fontB);font-size:14px} .cArea:not(:last-child){margin-bottom:25px} .cArea label{display:block;position:relative} .cArea label .n{display:block;position:absolute;left:0;right:0;top:0; color:rgba(8,16,43,.4);line-height:1.6em;padding:15px 16px 0;border-radius:4px 4px 0 0;transition:var(--trans-1)} .cArea label .n.req::after{content:'*';font-size:85%} .cArea textarea{height:100px} .cArea textarea:focus, .cArea textarea[data-text=fl]{height:200px} .cArea input:focus ~ .n, .cArea textarea:focus ~ .n, .cArea input[data-text=fl] ~ .n, .cArea textarea[data-text=fl] ~ .n{padding-top:5px;color:rgba(8,16,43,.7);font-size:90%;background:#ececec} .cArea .h{display:block;font-size:90%;padding:5px 16px 0;opacity:.7;line-height:normal} .nArea .contact-form-error-message-with-border{color:#d32f2f} .nArea .contact-form-success-message-with-border{color:#2e7b32} .tNtf img.contact-form-cross{display:none}
/* Widget Sliders */ .sldO{position:relative;display:flex;overflow-y:hidden;overflow-x:scroll; scroll-behavior:smooth;scroll-snap-type:x mandatory;list-style:none;margin:0;padding:0;border-radius:5px; -ms-overflow-style: none} .sldO.no-items{display:none} .sldO.no-items + .section{margin-top:0} .sldO .widget:not(:first-child){margin-top:0} .sldO .widget{position:relative;flex:0 0 100%;width:100%;background:transparent; outline:0;border:0} .sldC{position:relative} .sldS{position:absolute;top:0;left:0;width:100%;height:100%;scroll-snap-align:center;z-index:-1} .sldIm{background-repeat:no-repeat;background-size:cover;background-position:center;background-color:var(--transB);display:block;padding-top:40%;border-radius:5px;color:#fffdfc;font-size:13px} .sldT{position:absolute;bottom:0;left:0;right:0;display:block;padding:20px; background:linear-gradient(0deg, rgba(30,30,30,.1) 0%, rgba(30,30,30,.05) 60%, rgba(30,30,30,0) 100%); border-radius:0 0 3px 3px} .sldS{animation-name:tonext, snap;animation-timing-function:ease;animation-duration:4s;animation-iteration-count:infinite} .sldO .widget:last-child .sldS{animation-name:tostart, snap} .Rtl .sldS{animation-name:tonext-rev, snap} .Rtl .sldO .widget:last-child .sldS{animation-name:tostart-rev, snap} .sldO:hover .widget .sldS, .Rtl .sldO:hover .widget .sldS, .sldO:focus-within .widget .sldS, .Rtl .sldO:focus-within .widget .sldS{animation-name:none} @media (prefers-reduced-motion:reduce){.sldS, .Rtl .sldS{animation-name:none}} @media screen and (max-width:640px){.sldO{width:calc(100% + 40px);left:-20px;right:-20px;padding:0 12.5px 10px;border-radius:0} .sldO .widget{flex:0 0 90%;width:90%;margin:0 7.5px; box-shadow:0 10px 8px -8px rgb(0 0 0 / 12%)} .sldT{padding:10px 15px} .sldIm{font-size:12px}}
/* Google Transalte */ .goog-te-banner-frame.skiptranslate, .goog-te-gadget-simple img, img.goog-te-gadget-icon, .goog-te-menu-value span, #goog-gt-tt, .goog-tooltip, .goog-tooltip:hover, .goog-logo-link, .goog-te-balloon-frame{display:none!important} .goog-text-highlight{background-color: transparent !important;box-shadow:none !important;-webkit-box-shadow:none !important} .goog-te-menu-frame{box-shadow:none!important} .goog-te-gadget-simple{background-color:transparent!important;border:none!important;outline:0 !important;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-user-select:none !important;-moz-user-select:none !important;-ms-user-select:none !important;user-select:none !important} #google_translate_element{position:absolute !important;z-index:2 !important;overflow:hidden !important} #google_translate_element, .skiptranslate.goog-te-gadget, .goog-te-gadget-simple{width:40px !important;height:40px !important;padding:0 !important;margin:0 !important;border-radius:50% !important}
/* Widget Music Player */ #musicPlayer{position:relative;direction:ltr;width:100%;min-height:200px;max-width:480px;margin:20px 0;background:rgba(0,0,0,.03);border-radius:20px;display:flex;align-items:center;justify-content:center} #musicPlayer::before{content:'Loading Tracks...';position:absolute;color:inherit;font-size:13px;font-family:var(--fontB);opacity:.8} .drK #musicPlayer{background:var(--darkBs)}
/* Pop-up */ .fxPu{position:fixed;top:-50%;bottom:-50%;top:-50%;left:-50%;right:-50%;z-index:70;background:rgba(0,0,0,.4);visibility:hidden;opacity:0;transition:all .6s ease;-webkit-transition:all .6s ease} .fxPu.visible{visibility:visible;opacity:1} .fxPuW{position:fixed;top:0;bottom:0;top:0;left:0;right:0;z-index:71;padding:20px;display:flex;flex-direction:column;justify-content:center;align-items:center} .fxPuC{position:relative;background:#fffdfc;width:100%;max-width:500px;padding:20px 25px 25px;border-radius:20px} .fxPuCl::after{content:'\2715';line-height:16px;font-size:12px;color:#fffdfc;position:absolute;top:-15px;right:15px;background:var(--linkC);display:flex;align-items:center;justify-content:center;width:30px;height:30px;border-radius:50%;transition:var(--trans-1);-webkit-transition:var(--trans-1);cursor:pointer} .fxPuCl:hover::after{transform:scale(1.03);-webkit-transform:scale(1.03)} .fxPuS{display:block;text-align:center;margin-bottom:5px} .fxPuS svg.line{stroke-width:1;width:50px;height:50px} .fxPuH{font-size:1.2rem;font-weight:700;font-family:var(--fontBa);margin-bottom:10px;text-align:center} .fxPuD{line-height:1.7em;font-size:15px} .fxPuB{text-align:center;margin-top:20px} .fxPuB .btn{width:45px;height:45px;outline:none;border:none;display:inline-flex;align-items:center;justify-content:center;background:var(--linkC);border-radius:50%} .fxPuB .btn:hover{opacity:.8} .fxPuB .btn svg{stroke:#fffdfc} .drK .fxPuC{background:var(--darkBs)} .drK .fxPuCl::after, .drK .fxPuB .btn{background:var(--darkU)}
/* Toast Notif */ .tNtf span{position:fixed;left:24px;bottom:-70px;display:inline-flex;align-items:center;text-align:center;justify-content:center;z-index:997;background:#323232;color:rgba(255,255,255,.8);font-size:14px;font-family:var(--fontB);border-radius:3px;padding:13px 24px;box-shadow:0 5px 35px rgba(149,157,165,.3);opacity:0;transition:var(--trans-1);animation:slidein 5s ease forwards;-webkit-animation:slidein 5s ease forwards} .tNtf span i{content:''; width:14px;height:14px; display:inline-block;margin:0 5px;opacity:.8} .tNtf span i.check{background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><path d='M22 11.08V12a10 10 0 1 1-5.93-9.14'/><polyline points='22 4 12 14.01 9 11.01'/></svg>") center / 14px no-repeat} .tNtf span i.warn{background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><g transform='translate(2.500000, 3.000000)'><path d='M9.5,18 C3.00557739,18 0.456662548,17.5386801 0.0435259337,15.2033146 C-0.36961068,12.8679491 2.27382642,8.47741935 3.08841712,7.02846996 C5.81256986,2.18407813 7.66371927,0 9.5,0 C11.3362807,0 13.1874301,2.18407813 15.9115829,7.02846996 C16.7261736,8.47741935 19.3696107,12.8679491 18.9564741,15.2033146 C18.5443995,17.5386801 15.9944226,18 9.5,18 Z'/><line x1='9.5' y1='5.5' x2='9.5' y2='9.395'/><line x1='9.4957' y1='12.895' x2='9.5047' y2='12.895'/></g></svg>") center / 14px no-repeat} .tNtf span i.copy{background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><rect x='9' y='9' width='13' height='13' rx='2' ry='2'/><path d='M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1'/></svg>") center / 14px no-repeat} .tNtf span i.clipboard{background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><path d='M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2'/><rect x='8' y='2' width='8' height='4' rx='1' ry='1'/></svg>") center / 14px no-repeat} .tNtf span i.onCloud{background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><path d='M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z'/></svg>") center / 14px no-repeat} .tNtf span i.offCloud{background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><path d='M22.61 16.95A5 5 0 0 0 18 10h-1.26a8 8 0 0 0-7.05-6M5 5a8 8 0 0 0 4 15h9a5 5 0 0 0 1.7-.3'/><line x1='1' y1='1' x2='23' y2='23'/></svg>") center / 14px no-repeat} .tNtf span i.onWifi{background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><path d='M5 12.55a11 11 0 0 1 14.08 0'/><path d='M1.42 9a16 16 0 0 1 21.16 0'/><path d='M8.53 16.11a6 6 0 0 1 6.95 0'/><line x1='12' y1='20' x2='12.01' y2='20'/></svg>") center / 14px no-repeat} .tNtf span i.offWifi{background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><line x1='1' y1='1' x2='23' y2='23'/><path d='M16.72 11.06A10.94 10.94 0 0 1 19 12.55'/><path d='M5 12.55a10.94 10.94 0 0 1 5.17-2.39'/><path d='M10.71 5.05A16 16 0 0 1 22.58 9'/><path d='M1.42 9a15.91 15.91 0 0 1 4.7-2.88'/><path d='M8.53 16.11a6 6 0 0 1 6.95 0'/><line x1='12' y1='20' x2='12.01' y2='20'/></svg>") center / 14px no-repeat} .tNtf span i.del{background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><g transform='translate(3.500000, 2.000000)'><path d='M15.3891429,7.55409524 C15.3891429,15.5731429 16.5434286,19.1979048 8.77961905,19.1979048 C1.01485714,19.1979048 2.19295238,15.5731429 2.19295238,7.55409524'/><line x1='16.8651429' y1='4.47980952' x2='0.714666667' y2='4.47980952'/><path d='M12.2148571,4.47980952 C12.2148571,4.47980952 12.7434286,0.714095238 8.78914286,0.714095238 C4.83580952,0.714095238 5.36438095,4.47980952 5.36438095,4.47980952'/></g></svg>") center / 14px no-repeat} .Rtl .tNtf span{left:auto;right:24px} @media screen and (max-width:896px){.tNtf span{margin-bottom:50px}} @media screen and (max-width:500px){.tNtf span{left:20px;right:20px;font-size:13px}  .tNtf.alt span, .Rtl .tNtf.alt span{left:0;right:0;margin-bottom:-25px;justify-content:left;text-align:left;font-size:15px;padding:15px 24px;border-radius:0} .Rtl .tNtf.alt span{justify-content:right;text-align:right} .Rtl .tNtf span{left:20px;right:20px}} .drK .tNtf span{box-shadow:0 10px 40px rgba(0,0,0,.2)} .MN-4 .tNtf span{margin-bottom:0;animation-duration:2s;-webkit-animation-duration:2s}
/* Neon Lighting */ .nLght::before, .nLght::after{content:'';position:fixed;width:100%;bottom:0;left:0;right:0;height:3px;z-index:55;background:linear-gradient(-45deg, #4086F4, #31A952, #FBBE01, #EB4132,#4086F4, #31A952, #FBBE01, #EB4132);background-size:200%;-webkit-animation:animeNeon 5s linear infinite;animation:animeNeon 5s linear infinite;visibility:none;opacity:0;transition:all 1s ease} .nLght::after{height:10px;filter:blur(10px)} .nLght.vsbl::before, .nLght.vsbl::after{visibility:visible;opacity:.7} @-webkit-keyframes animeNeon{0%{background-position:0% 50%}50%{background-position:100% 50%}100%{background-position:0% 50%}} @keyframes animeNeon{0%{background-position:0% 50%}50%{background-position:100% 50%}100%{background-position:0% 50%}}
/* Maintenance Mode */ .mtm{position:fixed;left:0;right:0;top:-35%;bottom:-35%;display:flex;align-items:center;justify-content:center;background:var(--bodyB);transition:all .5s ease;-webkit-transition:all .5s ease;visibility:visible;opacity:1;z-index:499} .mtm.hdn{visibility:hidden;opacity:0} .mtmC{max-width:450px;padding:20px;text-align:center} .mtmH{font-weight:700;font-size:1.5rem;font-family:var(--fontBa);margin-bottom:10px} .mtmD{margin-bottom:10px} .mtm .clock{width:100%;font-size:25px;text-align:center;font-family:var(--fontBa);font-weight:400;position:relative;left:0;right:0} .mtm .tBox{width:65px;height:65px;margin:3px;padding-top:8px;background-color:#f4efff;border-radius:20px;display:inline-block} .mtm .unit{display:block;font-size:10px;margin:auto;font-family:var(--fontB);opacity:0.8} .drK .mtm{background:var(--darkB)} .drK .mtm .tBox{background:var(--darkBa)}
/* Sticky Ad */ .ancrA{position:fixed;bottom:0;left:0;right:0;max-height:200px;padding:5px;box-shadow:0 -6px 18px 0 rgba(9,32,76,.1); transition:var(--trans-1);display:flex;align-items:center;justify-content:center;background:#fffdfc;z-index:50;border-top:1px solid var(--contentL)} .ancrC{width:40px;height:30px;display:flex;align-items:center;justify-content:center;border-radius:12px 0 0;border:1px solid var(--contentL);border-bottom:0;border-right:0;position:absolute;right:0;top:-30px;background:inherit} .ancrC svg{width:18px;height:18px;fill:none!important;stroke:var(--iconC);stroke-linecap:round;stroke-linejoin:round;stroke-width:1.4} .ancrCn{flex-grow:1;overflow:hidden;display:block;position:relative} .ancrI:checked ~ .ancrA{padding:0;min-height:0} .ancrI:checked ~ .ancrA .ancrCn{display:none} .ancrI:checked ~ .ancrA .ancrC svg{-webkit-transform:rotate(180deg);transform:rotate(180deg)}
/* Error Page */ .erroP{display:flex;align-items:center;justify-content:center;height:100vh;text-align:center;padding:0} .erroC{width:calc(100% - 40px);max-width:450px;margin:auto;font-family:var(--fontBa)} .erroC h3{font-size:1.414rem;font-family:inherit} .erroC h3 span:not(.e){position:relative} .erroC h3 span.e{display:block;font-size:140px;line-height:.8;margin-bottom:-1rem;color:#ebebf0} .erroC h3 span.e{animation:glitch 1s linear infinite} .erroC h3 span.e::before, .erroC h3 span.e::after{content:attr(title);position:absolute;left:0;right:0} .erroC h3 span.e::before{animation:glitchTop 1s linear infinite;clip-path:polygon(0 0, 100% 0, 100% 33%, 0 33%);-webkit-clip-path:polygon(0 0, 100% 0, 100% 33%, 0 33%)} .erroC h3 span.e::after{animation:glitchBotom 1.5s linear infinite;clip-path:polygon(0 67%, 100% 67%, 100% 100%, 0 100%);-webkit-clip-path:polygon(0 67%, 100% 67%, 100% 100%, 0 100%)} .erroC p{margin:30px 5%;line-height:1.6em;opacity:.7} .erroC .button{margin:0;padding-left:2em;padding-right:2em;font-size:14px}
/* Extra */ .iFxd.bl{top:auto;bottom:0;right:auto;left:0} .aNm.t{background:rgba(255,255,255,.8);padding:6px} .aNm.t .im{width:16px;height:16px;margin-right:4px} .aNm.t .nm{font-size:13px;color:var(--bodyC)} .aNm.t svg{width:13px;height:13px;fill:#1DA1F2;margin-left:4px} .Rtl .iFxd.bl{top:auto;bottom:0;right:0;left:auto} .Rtl .aNm.t .im{margin-left:4px;margin-right:0} .Rtl .aNm.t svg{margin-left:0;margin-right:4px} .onIndx .blogPts .ntry, .PopularPosts, .widget.Label, .footer{position:relative;overflow:hidden} .onIndx .blogPts .ntry::before, .itemFt .iCtnt::before, .PopularPosts::before, .widget.Label::before, .footer::before{content:'';width:70px;height:70px;position:absolute;z-index:0;background:rgba(0,0,0,.04);bottom:0;left:0;border-radius:0 70px 0 0} .onIndx.Rtl .blogPts .ntry::before, .Rtl .itemFt .iCtnt::before, .Rtl .PopularPosts::before, .Rtl .widget.Label::before, .Rtl .footer::before{border-radius:70px 0 0 0;bottom:0;left:unset;right:0} @media screen and (min-width:501px){.itemFt .iCtnt::before{display:none}} .pTtmp[data-type]{display:inline-flex;align-items:center} .pTtmp[data-type]::before{content:attr(data-type);display:inline;background:var(--linkB);color:#fff;font-size:10px;line-height:.6rem;padding:3px;border-radius:2px;margin-right:4px} .drK .pTtmp[data-type]::before{background:var(--darkU)} .pTtmp[data-type]::after{display:inline} .Rtl .pTtmp[data-type]::before{margin-left:4px;margin-right:0} .Rtl .FeaturedPost .itemFt::after{left:0;right:unset;border-radius:0 0 20px 0} .Rtl .toTopB{right:unset;left:20px} .Rtl .mainWrp .headIc .tNav svg{transform:rotate(180deg);-webkit-transform:rotate(180deg)}
/* Ads Loading Animation */ .adsbygoogle{background:transparent url("data:image/svg+xml, <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' preserveAspectRatio='xMidYMid meet'><path fill='rgba(221,221,221,1)' opacity='.5' d='M12 2A10 10 0 1 0 22 12A10 10 0 0 0 12 2Zm0 18a8 8 0 1 1 8-8A8 8 0 0 1 12 20Z'></path><path fill='rgba(0,0,255,1)' d='M20 12h2A10 10 0 0 0 12 2V4A8 8 0 0 1 20 12Z'><animateTransform attributeName='transform' dur='.9s' from='0 12 12' repeatCount='indefinite' to='360 12 12' type='rotate'/></path></svg>") center / 26px no-repeat;background-size:45px 45px}
/* Responsive */
@media screen and (min-width:897px){
/* mainIn */ .mainIn, .blogM{display:flex} .blogMn{width:var(--navW);flex-shrink:0;position:relative;transition:var(--trans-1);z-index:1} .blogCont{padding-top:30px} .blogCont::before{content:'';position:absolute;top:var(--headerHi);left:0;height:calc(100% + var(--headerH))} .blogCont{width:calc(100% - var(--navW))} .mnBrs{box-shadow:0 0 15px rgba(0,0,0,.07)} .blogCont .secIn{padding-left:25px;padding-right:25px} .mainbar{flex:1 0 calc(100% - var(--sideW) - 25px);width:calc(100% - var(--sideW) - 25px)} .sidebar{display:flex;flex:0 0 calc(var(--sideW) + 25px);width:calc(var(--sideW) + 25px); margin:0} .sidebar::before{content:'';flex:0 0 25px} .sidebar .sideIn{width:calc(100% - 25px)}
/* mainNav */ .mnBr{position:sticky;position:-webkit-sticky;top:var(--headerH)} .mnBrs{display:flex;height:calc(100vh - var(--headerH));font-size:13px;position:relative} .mnBrs >*:not(.mnMob){width:100%} .mnMen{padding:20px;overflow-y:hidden;overflow-x:hidden} .mnMen:hover{overflow-y:scroll} .mnMob{position:fixed;width:var(--navW)} .mnH, .mobMn{display:none} .bD:not(.hdMn) .navI:checked ~ .mainWrp .blogMn, .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMob, .hdMn .navI:not(:checked) ~ .mainWrp .blogMn, .hdMn .navI:not(:checked) ~ .mainWrp .mnMob{width:75px} .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMn a:hover, .hdMn .navI:not(:checked) ~ .mainWrp .mnMn a:hover{opacity:1;color:inherit} .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMn .a, .hdMn .navI:not(:checked) ~ .mainWrp .mnMn .a{max-width:40px; border-radius:15px} .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMn .drp.mr, .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMn svg.d, .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMob .PageList, .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMob .mSoc, .hdMn .navI:not(:checked) ~ .mainWrp .mnMn .drp.mr, .hdMn .navI:not(:checked) ~ .mainWrp .mnMn svg.d, .hdMn .navI:not(:checked) ~ .mainWrp .mnMob .PageList, .hdMn .navI:not(:checked) ~ .mainWrp .mnMob .mSoc{display:none} .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMob .mNav, .hdMn .navI:not(:checked) ~ .mainWrp .mnMob .mNav{display:flex} .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMn >li.br::after, .hdMn .navI:not(:checked) ~ .mainWrp .mnMn >li.br::after{max-width:20px} .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMen, .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMen:hover, .hdMn .navI:not(:checked) ~ .mainWrp .mnMen, .hdMn .navI:not(:checked) ~ .mainWrp .mnMen:hover{overflow-y:visible;overflow-x:visible} .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMn .n, .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMn li:not(.mr) ul, .hdMn .navI:not(:checked) ~ .mainWrp .mnMn .n, .hdMn .navI:not(:checked) ~ .mainWrp .mnMn li:not(.mr) ul{position:absolute;left:35px;top:3px;margin:0 5px;padding:8px 10px;border-radius:5px 16px 16px 16px;max-width:160px;background:var(--contentB);color:var(--bodyC);opacity:0;visibility:hidden;box-shadow:0 5px 20px 0 rgba(0,0,0,.1);z-index:1} .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMn li:not(.mr) ul, .hdMn .navI:not(:checked) ~ .mainWrp .mnMn li:not(.mr) ul{padding:0 5px;margin:0;overflow:hidden;display:block} .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMn li:not(.mr):last-child ul, .hdMn .navI:not(:checked) ~ .mainWrp .mnMn li:not(.mr):last-child ul{top:auto;bottom:3px;border-radius:16px 16px 16px 5px} .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMn li:not(.drp) .a:hover .n, .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMn li:not(.mr):hover ul, .hdMn .navI:not(:checked) ~ .mainWrp .mnMn li:not(.drp) .a:hover .n, .hdMn .navI:not(:checked) ~ .mainWrp .mnMn li:not(.mr):hover ul{opacity:1;visibility:visible} .bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMn ul li >*, .hdMn .navI:not(:checked) ~ .mainWrp .mnMn ul li >*{border-radius:0}
/* Article */ .onIndx.onHm .blogPts >*{flex:0 0 calc(33.33% - 20px);width:calc(33.33% - 20px)} .onIndx.onMlt .blogPts >*{flex:0 0 calc(25% - 20px);width:calc(25% - 20px)}
}
@media screen and (min-width:768px){
::-webkit-scrollbar{-webkit-appearance:none;width:8px;height:5px}::-webkit-scrollbar-track{background:transparent}::-webkit-scrollbar-thumb{background:rgba(0,0,0,.15);border-radius:10px}::-webkit-scrollbar-thumb:hover{background:rgba(0,0,0,.35)}::-webkit-scrollbar-thumb:active{background:rgba(0,0,0,.35)}
}
@media screen and (max-width:1600px){
/* Quick Edit */ .nBtm.qEdit .qeBtn{margin-bottom:85px}
/* Back to Top */ .nBtm.toTopB{margin-bottom:80px}
}
@media screen and (max-width:1100px){
/* Article */ .onIndx.onHm .blogPts >*{flex:0 0 calc(50% - 20px);width:calc(50% - 20px)} .onIndx.onMlt .blogPts >*{flex:0 0 calc(33.33% - 20px);width:calc(33.33% - 20px)}
/* Widget */ .itemFt .itm >*, .itemFt .itm >*:last-child{flex:0 0 calc(50% - 20px);width:calc(50% - 20px)} .itemFt .itm >*:last-child{flex-grow:1} .itemFt .pSnpt{display:none}
/* Footer */  .fotIn >*, .fotIn >*:first-child{width:calc(50% - 30px)}
}
@media screen and (max-width:896px){
/* Header */ .ntfC{padding-left:20px;padding-right:20px} header, .drK header{box-shadow:none;border:none} header.stick{box-shadow:0 2px 8px 0 rgba(0,0,0,.07)} .drK:not(.HD-2) header.stick{border-bottom:1px solid rgba(255,255,255,.15);box-shadow:none} .HD-2 header{position: relative;padding:15px 15px 0} .HD-2 header.stick{box-shadow:none} .HD-2 .headCn{border-radius:var(--headerR);box-shadow: 0 5px 15px rgba(0,0,0,.07)} .drK.HD-2 .headCn{background:var(--darkBa)} .headL{padding:0 0 0 15px;flex-grow:1;width:50%} .headR{padding:0 20px 0 0;flex-grow:0} .headIc .isSrh{display:block} .headI .headS{margin:0} .bD:not(.hdMn) .navI:not(:checked) ~ .mainWrp .headIc .tNav .h1, .bD:not(.hdMn) .navI:checked ~ .mainWrp .headIc .tNav .h3 {opacity:1} .bD:not(.hdMn) .navI:not(:checked) ~ .mainWrp .headIc .tNav .h2, .bD:not(.hdMn) .navI:checked ~ .mainWrp .headIc .tNav .h1{opacity:0}
/* mainNav */ .blogMn{display:flex;justify-content:flex-start;position:fixed;left:0;top:0;bottom:0;margin-left:-100%;z-index:20;transition:var(--trans-1);width:100%;height:100%} .flT .blogMn{left:15px;top:15px;bottom:15px;height:auto} .flT .mnBr{border-radius:20px;width:80%} .Rtl.flT .blogMn{left:auto;right:15px} .mnBr{width:85%;max-width:480px;height:100%;border-radius:0 12px 12px 0;transition:inherit;z-index:3;overflow:hidden;position:relative;box-shadow:0 5px 30px 0 rgba(0,0,0,.05)} .mnBrs{padding:60px 0 0;overflow-y:scroll;overflow-x:hidden;width:100%;height:100%} .mnH{padding:0 15px} .mnH label{padding:15px 10px} .mnH .c::after{margin:0 13px} .mnMen{padding-top:0} .navI:checked ~ .mainWrp .blogMn{margin-left:0} .navI:checked ~ .mainWrp .blogMn .fCls{opacity:1;visibility:visible;background:rgba(0,0,0,.2); -webkit-backdrop-filter:saturate(180%) blur(10px); backdrop-filter:saturate(180%) blur(10px)}
/* Article */ .onIndx.onHm .blogPts >*{flex:0 0 calc(33.33% - 20px);width:calc(33.33% - 20px)}
/* Widget */ .itemFt .pSnpt{display:-webkit-box} .bD:not(.MN-4) .mobMn:not(.no-items) + footer{padding-bottom:calc(55px + 20px)} .bD:not(.MN-4) .wvH{height:calc(100px + 60px)}
/* Widget Scroll Menu */ .HD-2 .navS{box-shadow:0 0 25px rgba(0,0,0,.07)}
/* Back To Top */ .bD:not(.MN-4) .toTopB{margin-bottom:55px} .bD:not(.MN-4) .nBtm.toTopB{margin-bottom:145px} .MN-4 .nBtm.toTopB{margin-bottom:80px}
}
@media screen and (max-width:768px){
/* Article */ .onIndx.onHm .blogPts >*, .onIndx.onMlt .blogPts >*{flex:0 0 calc(50% - 20px);width:calc(50% - 20px)}
}
@media screen and (max-width:640px){
/* Header */ .headCn{height:var(--headerHm)}
/* Pop-up */ .fixL{align-items:flex-end} .fixL .fixLi, .fixL .cmBri{border-radius:12px 12px 0 0; max-width:680px} .fixL .cmBri:not(.mty){border-radius:0;height:100%;max-height:100%}
/* Footer */ footer{padding-top:30px}
}
@media screen and (max-width:500px){
/* Font and Blog */ .iFxd, .crdtIn{font-size:12px} .brdCmb{font-size:13px} .pDesc{font-size:14px} .pEnt{font-size:var(--postFm)} .pTtl.itm{font-size:var(--postTm)} .pInf.ps .pTtmp::after{content:attr(data-time)} .pInf.ps .pDr{font-size:12px} .pInf .pNm, .pInf .pDr{display:flex;flex-direction:column} .pInf .pDr >*:not(:first-child)::before{display:none}
/* Article */ .onIndx:not(.oneGrd) .blogPts{width:calc(100% + 10px);left:-5px;right:-5px} .onIndx:not(.oneGrd) .blogPts >*{padding:8px 8px 40px;flex:0 0 calc(50% - 10px);width:calc(50% - 10px);margin-left:5px;margin-right:5px;margin-bottom:10px} .onIndx:not(.oneGrd) .blogPts .pTag{padding-bottom:5px} .onIndx:not(.oneGrd) .blogPts div.ntry{flex:0 0 calc(100% - 10px)} .onIndx:not(.oneGrd) .pCntn{padding:0} .onIndx:not(.oneGrd) .ntry .pSml{font-size:12px} .onIndx:not(.oneGrd) .blogPts .pInf{bottom:10px;left:8px;right:8px} .onIndx:not(.oneGrd) .ntry .iFxd{font-size:10px} .onIndx:not(.oneGrd) .ntry .iFxd:not(.l) >*{margin-left:0} .Rtl.onIndx:not(.oneGrd) .ntry .iFxd:not(.l) >*{margin-left:5px;margin-right:0} .onIndx:not(.oneGrd) .ntry .iFxd:not(.l) >* svg{width:14px;height:14px;stroke-width:1.7} .onIndx:not(.oneGrd) .ntry .iFxd:not(.l) >* svg{margin:0 1.5px} .onIndx:not(.oneGrd) .ntry .iFxd.l .edit{padding:5px} .onIndx:not(.oneGrd) .ntry .iFxd.l >* svg{margin:0px} .onIndx:not(.oneGrd) .ntry .pTtl{font-size:.8rem;font-weight:600} .onIndx:not(.oneGrd) .ntry:not(.pTag) .pSnpt, .onIndx:not(.oneGrd) .ntry .pInf:not(.nSpr) .pJmp, .onIndx:not(.oneGrd) .ntry .iFxd .spnr, .onIndx:not(.oneGrd) .iFxd .bM{display:none} .onIndx:not(.oneGrd) .ntry .iFxd{padding:8px 3px} .onIndx:not(.oneGrd) .ntry .iFxd.l{top:auto;bottom:0;right:0} .Rtl.onIndx:not(.oneGrd) .ntry .iFxd.l{right:auto;left:0} .onIndx:not(.oneGrd) .ntry .iFxd .cmnt, .onIndx:not(.oneGrd) .ntry .iFxd .pV{padding:3.5px} .onIndx:not(.oneGrd) .ntry .iFxd >* svg{padding:1px} .onIndx.oneGrd .blogPts >*{flex:0 0 calc(100% - 20px);width:calc(100% - 20px)}
/* Share */ .pShc{width:calc(100% + 10px);left:-5px;right:-5px} .pShc::before{width:calc(100% - 10px);margin:0 5px 12px} .pShc .wa::after, .pShc .tw::after{display:none}
/* Widget */ .prfI:checked ~ .mainWrp .wPrf{top:auto;bottom:0} .prfI:checked ~ .mainWrp .Profile .fCls{background:rgba(0,0,0,.2); -webkit-backdrop-filter:saturate(180%) blur(10px); backdrop-filter:saturate(180%) blur(10px)} .prfH .c{display:flex} .wPrf{position:fixed;top:auto;left:0;right:0;bottom:-100%;width:100%;max-height:calc(100% - var(--headerH));border-radius:12px 12px 0 0} .itemFt .itm{padding-bottom:80px} .itemFt .itm >*{flex:0 0 calc(100% - 20px);width:calc(100% - 20px)} .itemFt .itm .iCtnt{flex:0 0 calc(100% - 42px);width:calc(100% - 42px);margin:0 auto;position:absolute;left:0;right:0;bottom:0;padding:13px;background:rgba(255,253,252,.92);border-radius:10px;box-shadow:0 10px 20px -5px rgba(0,0,0,.1); -webkit-backdrop-filter:saturate(180%) blur(10px); backdrop-filter:saturate(180%) blur(10px)} .itemFt .pTtl{font-size:1rem} .itemFt .pSnpt{font-size:93%}
/* Footer */ .fotIn >*, .fotIn >*:first-child{width:calc(100% - 30px)}
}
/* Keyframes Animation */ @keyframes shimmer{100%{transform:translateX(100%)}} @keyframes slidein{0%{opacity:0}10%{opacity:1;bottom:24px}50%{opacity:1;bottom:24px}90%{opacity:1;bottom:24px}100%{opacity:0;bottom:-70px;visibility:hidden}} @keyframes fadein{50%{opacity:1}80%{opacity:1;padding-top:5px;height:22px}100%{opacity:0;padding-top:0;height:0}} @keyframes nudge{0%{transform:translateX(0)}30%{transform:translateX(-5px)}50%{transform:translateX(5px)}70%{transform:translateX(-2px)}100%{transform:translateX(0)}} @keyframes tonext{ 75%{left:0} 95%{left:100%} 98%{left:100%} 99%{left:0}} @keyframes tostart{ 75%{left:0} 95%{left:-300%} 98%{left:-300%} 99%{left:0}} @keyframes tonext-rev{ 75%{right:0} 95%{right:100%} 98%{right:100%} 99%{right:0}} @keyframes tostart-rev{ 75%{right:0} 95%{right:-300%} 98%{right:-300%} 99%{right:0}} @keyframes snap{ 96%{scroll-snap-align:center} 97%{scroll-snap-align:none} 99%{scroll-snap-align:none} 100%{scroll-snap-align:center}} @keyframes indicator{0%{opacity:0}50%{opacity:1}100%{opacity:0}} @keyframes opaCity{0%{opacity:0}100%{opacity:1}} @-webkit-keyframes fadein{50%{opacity:1}80%{opacity:1;padding-top:5px;height:22px}100%{opacity:0;padding-top:0;height:0}} @-webkit-keyframes slidein{0%{opacity:0}10%{opacity:1;bottom:24px}50%{opacity:1;bottom:24px}90%{opacity:1;bottom:24px}100%{opacity:0;bottom:-70px;visibility:hidden}} @-webkit-keyframes nudge{0%{transform:translateX(0)}30%{transform:translateX(-5px)}50%{transform:translateX(5px)}70%{transform:translateX(-2px)}100%{transform:translateX(0)}} @-webkit-keyframes tonext{ 75%{left:0} 95%{left:100%} 98%{left:100%} 99%{left:0}} @-webkit-keyframes tostart{ 75%{left:0} 95%{left:-300%} 98%{left:-300%} 99%{left:0}} @-webkit-keyframes tonext-rev{ 75%{right:0} 95%{right:100%} 98%{right:100%} 99%{right:0}} @-webkit-keyframes tostart-rev{ 75%{right:0} 95%{right:-300%} 98%{right:-300%} 99%{right:0}} @-webkit-keyframes snap{ 96%{scroll-snap-align:center} 97%{scroll-snap-align:none} 99%{scroll-snap-align:none} 100%{scroll-snap-align:center}} @-webkit-keyframes indicator{0%{opacity:0}50%{opacity:1}100%{opacity:0}} @-moz-keyframes indicator{0%{opacity:0}50%{opacity:1}100%{opacity:0}} @keyframes glitch{2%,64%{transform:translate(2px,0) skew(0deg)} 4%,60%{transform:translate(-2px,0) skew(0deg)} 62%{transform:translate(0,0) skew(5deg)}} @keyframes glitchTop{2%,64%{transform:translate(2px,-2px)} 4%,60%{transform:translate(-2px,2px)} 62%{transform:translate(13px,-1px) skew(-13deg)}} @keyframes glitchBotom{2%,64%{transform:translate(-2px,0)} 4%,60%{transform:translate(-2px,0)} 62%{transform:translate(-22px,5px) skew(21deg)}} @-webkit-keyframes opaCity{0%{opacity:0}100%{opacity:1}}
/* Noscript Option */ .lazy:not([lazied]){display:none} .bD .pS img.lazy:not([lazied]){display:none !important} .noJs{display:flex;justify-content:flex-end;align-items:center;position:fixed;top:20px;left:20px;right:20px;z-index:99;max-width:640px;border-radius:12px;margin:auto;padding:10px 5px;background:#ffdfdf;font-size:13px;box-shadow:0 10px 20px -10px rgba(0,0,0,.1);color:#48525c} .noJs::before{content:attr(data-text);padding:0 10px;flex-grow:1} .noJs label{flex-shrink:0;padding:10px} .noJs label::after{content:'\2715';line-height:18px;font-size:14px} .nJs:checked ~ .noJs{display:none}
/* Hide Scroll */ .scrlH::-webkit-scrollbar{width:0;height:0} .scrlH::-webkit-scrollbar-track{background:transparent} .scrlH::-webkit-scrollbar-thumb{background:transparent;border:none}
/* --- Remove to reduce CSS size or if you aren't using RTL --- */
.adB{min-height:70px;display:flex;align-items:center;justify-content:center;font-size:13px;color:var(--darkTa);border:1px solid var(--contentL);border-radius:3px} .adB.h240{min-height:240px} .adB.h360{min-height:360px} .adB.h480{min-height:480px} .h240, .h360, .h480{border-radius:5px 15px 5px 5px} .adB::before{content:attr(data-text)}
/* Article Style RTL */ .Rtl .note{padding:20px 50px 20px 20px} .Rtl .note::before{left:auto;right:-12px} .Rtl .note::after{left:auto;right:18px} .Rtl .pS hr::before{text-indent:-0.6em} .Rtl .dropCap{margin:0;line-height:inherit;font-size:inherit;float:none} .Rtl blockquote, .Rtl .cmC i[rel=quote]{border-left:0;border-right:2px solid var(--contentL)} .Rtl blockquote.s-1{padding:25px 45px 25px 25px;border-right:0} .Rtl blockquote.s-1::before{left:auto;right:0}
/* RTL Mode */ .Rtl .mnMn li li a >* svg{margin-right:0;margin-left:5px} .Rtl .ntfT{padding:0 0 0 15px} .Rtl .headL{padding:0 20px 0 0} .Rtl .headL .headN{padding:0 5px 0 0} .Rtl .BlogSearch .sb{left:auto;right:0} .Rtl .BlogSearch button.sb{left:0;right:auto} .Rtl .wPrf{right:auto;left:0;border-radius:5px 16px 16px 16px} .Rtl .toTopF{right:auto;left:18px} .Rtl .cmAc a::before{content:'\2935'} .Rtl .cmHl >li{padding:0 17.5px 0 0} .Rtl .cmHl >li >.cmAv{left:auto;right:0} .Rtl .cmHl >li >.cmIn{padding:12px 28px 12px 15px} .Rtl .mnMn li:not(.mr) ul{padding-left:0;padding-right:30px} .Rtl .tocL{right:auto;left:-280px} .Rtl .tocLi{border-radius:0 12px 12px 0} .Rtl .tocLi::before{left:auto;right:0} .Rtl .tocC{left:auto;right:-45px} .Rtl .tocC span{border-radius:0 20px 20px 0} .Rtl .tocL svg.rad{right:auto;left:-2px;transform:rotate(176deg)} .Rtl .tocL svg.rad.in{transform:rotate(-86deg)} .Rtl .tocI:checked ~ .tocL{right:auto;left:0} .Rtl .mainWrp:after{right:unset;left:0;border-radius:0 0 200px 0} @media screen and (min-width:897px){.Rtl .blogCont::before{left:auto;right:0} .Rtl:not(.hdMn) .navI:checked ~ .mainWrp .mnMn .n, .Rtl:not(.hdMn) .navI:checked ~ .mainWrp .mnMn li:not(.mr) ul, .Rtl.hdMn .navI:not(:checked) ~ .mainWrp .mnMn .n, .Rtl.hdMn .navI:not(:checked) ~ .mainWrp .mnMn li:not(.mr) ul{left:auto;right:35px;border-radius:16px 5px 16px 16px} .Rtl:not(.hdMn) .navI:checked ~ .mainWrp .mnMn li:not(.mr):last-child ul, .Rtl.hdMn .navI:not(:checked) ~ .mainWrp .mnMn li:not(.mr):last-child ul{border-radius:16px 16px 5px 16px}} @media screen and (max-width:896px){.Rtl .headR{padding:0 0 0 20px} .Rtl .headL{padding:0 15px 0 0} .Rtl .blogMn{left:auto;right:0;margin-right:-100%} .Rtl .mnBr{border-radius:12px 0 0 12px} .Rtl.flT .mnBr{border-radius:20px} .Rtl .navI:checked ~ .mainWrp .blogMn{margin-left:auto;margin-right:0}} @media screen and (max-width:500px){.Rtl .wPrf{right:0;border-radius:12px 12px 0 0} .Rtl .ftMn{right:auto;left:-4px;border-radius:16px 16px 16px 5px} .Rtl .cdtIn .PageList{margin-left:9px;margin-right:auto}}
/* Basic Layout */ @media screen and (min-width:897px){.LS-2 header, .LS-2 .tocLi::before, .LS-3 header{border:0} .LS-2, .LS-2 .headCn, .LS-2 .mnBrs, .LS-3 .mnBrs, .LS-3 .headL{background:#fafafc} .LS-2 .blogCont::before{border:0;top:0;right:0;bottom:0;height:100%;z-index:-1;background:var(--contentB);border-radius:15px 0 0 0} .LS-3 .headCn{background:transparent} .LS-3 .headR{background:var(--headerB)} .LS-3 .blogCont::before{z-index:10} .LS-3 .blogMn{z-index:11} .LS-3 .tocL{top:var(--headerHi);z-index:10} .LS-3 .tocI:checked ~ .tocL{z-index:10} .LS-3:not(.hdMn) .navI:checked ~ .mainWrp .headN, .LS-3.hdMn .navI:not(:checked) ~ .mainWrp .headN{display:none} .LS-3:not(.hdMn) .navI:checked ~ .mainWrp .headL, .LS-3 .navI:not(:checked) ~ .mainWrp .headL{width:75px}}

/* --- Your Custom CSS goes here --- */

/*]]>*/</style>
  
  <style>/*<![CDATA[*/
/* Change Mode */ .headR .headM{display:block;position:absolute;top:0;right:0;padding:10px 0;width:150px;background:var(--contentB);font-size:13px;border-radius:10px 5px 10px 10px;box-shadow:0 0 15px rgba(0,0,0,.07);-webkit-transition:var(--trans-2);transition:var(--trans-2);overflow:hidden;z-index:18;opacity:0;visibility:hidden} .headR .headM:before{content:attr(data-text);display:block;padding:0 15px 10px;width:100%;font-size:11px;opacity:.7} .headR .headM >*{display:block;padding:9px 15px;width:100%;cursor:pointer} .headR .headM >*:before{content:attr(aria-label);opacity:.9} .headR .headM >*:hover{background:rgba(0,0,0,.05)} .navM:checked ~ .mainWrp .headM{visibility:visible;opacity:1} .navM:checked ~ .mainWrp .headM + .fCls{visibility:visible;opacity:1;z-index:17} .headR .headM .sydB{display:none} .bD:not(.drK):not(.syD) .headR .headM .lgtB{background-color:rgba(0,0,0,.1)} .drK:not(.syD) .headR .headM .drkB{background-color:rgba(0,0,0,.1)} .syD .headR .headM .sydB{background-color:rgba(0,0,0,.1)} .drK .headR .headM{background:var(--darkBs)} .Rtl .headR .headM{right:auto;left:0;border-radius:5px 10px 10px 10px}
/* Author No Image */ .aFl{position:relative;width:28px;height:28px;display:flex;align-items:center;justify-content:center;border-radius:50%;overflow:hidden} .admPs .aFl{width:34px;height:34px} .aFl::before{content:'';position:absolute;z-index:0;top:0;right:0;bottom:0;left:0;background:var(--linkB);opacity:.8} .aFl >span{position:absolute;z-index:1;font-size:0;color:transparent;padding-top:1.5px;} .admPs .aFl >span{padding-top:2px} .aFl >span:first-letter{color:#fffdfc;font-size:13px} .admPs .aFl >span:first-letter{font-size:16px} .drK .aFl::before{background:var(--darkU);opacity:1}
/* Dark Mode */ .drK.onIndx .blogPts >*, .drK .rPst, .drK .PopularPosts, .drK .Label{background:var(--darkBa)} .drK .tDL .d2{display:block} .drK .tDL::after{content:attr(data-light)} .drK .tDL svg .f{stroke:none;fill:var(--darkT)}  .drK .pThmb:not(.nul)::before{background-image:linear-gradient(90deg, rgba(0,0,0,0) 0, rgba(0,0,0,.07) 20%, rgba(0,0,0,.1) 60%, rgba(0,0,0,0))} .drK input::placeholder, .drK .cpL input, .drK .cArea label .n{color:rgba(255,255,255,.25)} .drK .erroC h3 span.e{color:var(--darkBa)} .drK .nArea .contact-form-error-message-with-border{color:#f94f4f} .drK .cmC i[rel=image]::before, .drK .widget input[type=text], .drK .widget input[type=email], .drK .widget textarea{background:var(--darkBs);border-color:rgba(255,255,255,.15)} .drK svg, .drK svg.c-1{fill:var(--darkT)} .drK svg.line, .drK .ancrC svg{fill:none;stroke:var(--darkT)} .drK svg.c-2{fill:var(--darkTa); opacity:.4} .drK, .drK .headCn, .drK .mnBrs{background:var(--darkB);color:var(--darkT)} .drK .ntfC, .drK .mobMn{background:var(--darkBa);color:var(--darkTa)} .drK .ntfC{color:rgba(255,255,255,.9)} .drK header, .drK .mnMn >li.br::after, .drK .blogCont::before, .drK .tbHd, .drK .cmHl >li >.cmIn, .drK .pTag .pMart a, .drK .pRelate, .drK blockquote, .drK .cmC i[rel=quote], .drK blockquote.s-1, .drK details.sp, .drK .ps table:not(.tr-caption-container), .drK .ps table th, .drK .ps table:not(.tr-caption-container) tr:not(:last-child) td, .drK .pre.tb .preH, .drK details.ac, .drK .ancrA, .drK .ancrC, .drK .mnMob{border-color:rgba(255,255,255,.15)} .drK .pre{background:var(--darkBs);color:var(--darkT)} .drK .footer{background:var(--darkBs)} .drK .mnMn li:not(.mr) .a:hover, .drK .mnMn ul li >*:hover, .drK .wL.bg li >*, .drK .mobMn li >*:hover, .drK .shL a, .drK .cpLb{background:rgba(0,0,0,.15)} .drK .tIc::after{background:var(--darkBs)} .drK .wPrf{background:var(--darkBa);box-shadow:0 10px 40px rgba(0,0,0,.2)} .drK header a, .drK h1, .drK h2, .drK h3, .drK h4, .drK h5, .drK h6, .drK footer, .drK .button{color:var(--darkT)} .drK .dlBox, .drK .fixLs, .drK .cArea input:focus ~ .n, .drK .cArea textarea:focus ~ .n, .drK .cArea input[data-text=fl] ~ .n, .drK .cArea textarea[data-text=fl] ~ .n{background:var(--darkBs)} .drK .tocLi{background:var(--darkB)} .drK .admPs, .drK .tocC span, .drK .pShc >*:not(.c), .drK .ancrA, .drK .BlogSearch input, .drK .cmHl >li >.cmIn{background:var(--darkBa)} .drK .tocC span::before{background:var(--darkBa)} .drK .tocC span::after{background:var(--darkU)} .drK .tocL svg.rad{fill:var(--darkBa)} .drK .mobMn li >*:hover svg.line{fill:var(--darkT) !important} .drK .mobMn li >*:hover svg.line .svgC{fill:var(--darkU) !important;stroke:var(--darkU)} .drK.mobS .mobMn li >*:hover, .drK .button.ln{background:transparent} .drK .pTag .pPric::before, .drK .pTag .pInfo small{color:var(--darkTa)} .drK::selection, .drK a, .drK .free::after, .drK .new::after, .drK .mnMn li:not(.mr) .a:hover, .drK .mnMn ul li a:hover, .drK .aTtl a:hover, .drK .pSnpt.nTag, .drK .pTag .pPric, .drK details.ac[open] summary, .drK .cpL label, .drK .wL li >*:hover .lbC, .drK .wL li >div .lbC, .drK .wL .lbM, .drK .cmBtn.ln:hover, .drK .wL.cl .lbN:hover .lbC, .drK .wL.cl div.lbN .lbC{color:var(--darkU)} .drK .mnMn .a:hover svg:not(.d){fill:var(--darkU)} .drK .mnMn .a:hover svg.line:not(.d), .drK .pJmp svg{fill:none;stroke:var(--darkU)} .drK .wL li >*:hover svg, .drK .wL li >div svg{stroke:var(--darkU)} .drK.MN-3 .mobMn li >*:hover::after, .drK.MN-4 .mobMn li >*:hover::after, .drK .toTopF, .drK .blogPg >*, .drK .button, .drK .zmImg::after, .drK .widget input[type=button], .drK .widget input[type=submit]{background:var(--darkU)} .drK.MN-3 .mobMn li >*:hover svg.line, .drK.MN-4 .mobMn li >*:hover svg.line{stroke:var(--darkU);fill:var(--darkU) !important} .drK.MN-3 .mobMn li >*:hover svg .f, .drK.MN-4 .mobMn li >*:hover svg .f, .drK footer .credit a svg{fill:var(--darkU)} .drK .pS input[id*="1"]:checked ~ .tbHd label[for*="1"], .drK .pS input[id*="2"]:checked ~ .tbHd label[for*="2"], .drK .pS input[id*="3"]:checked ~ .tbHd label[for*="3"], .drK .pS input[id*="4"]:checked ~ .tbHd label[for*="4"], .drK .widget input[type=text]:focus, .drK .widget input[type=email]:focus, .drK .widget textarea:focus, .drK .widget input[data-text=fl], .drK .widget textarea[data-text=fl], .drK .wL.cl .lbN:not(div):hover, .drK .wL.cl div.lbN{border-color:var(--darkU)} .drK .button.ln:hover{border-color:var(--darkU);box-shadow:0 0 0 1px var(--darkU) inset} .drK .mnBr a, .drK .pLbls >*, .drK .aTtl a, .drK .blogPg >*, .drK .brdCmb a, .drK .wL li >*, .drK .mobMn li >*, .drK .cmAc a{color:var(--darkT)} .drK .blogPg .nPst, .drK .blogPg .current{background:var(--contentL);color:var(--bodyCa)} .drK .FeaturedPost .itemFt::after{background:var(--darkTa)} @media screen and (min-width:897px){.drK header .BlogSearch input{background:var(--darkBs)} .drK header .BlogSearch input:focus, .drK header .BlogSearch input:hover{background:var(--darkBa)} .drK.bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMn .n, .drK.bD:not(.hdMn) .navI:checked ~ .mainWrp .mnMn li:not(.mr) ul, .drK.hdMn .navI:not(:checked) ~ .mainWrp .mnMn .n, .drK.hdMn .navI:not(:checked) ~ .mainWrp .mnMn li:not(.mr) ul{background:var(--darkBa);box-shadow:0 10px 40px rgba(0,0,0,.2);color:var(--darkTa)} .drK.LS-2 .blogCont::before{background:var(--darkBs)} .drK.LS-3 .headCn{background:transparent} .drK.LS-3 .headL, .drK.LS-3 .mnBrs{background:var(--darkBs)} .drK.LS-3 .headR{background:var(--darkB)} .drK .tocLi::before{border-color:rgba(255,255,255,.15)}} @media screen and (min-width:897px){.drK header{border-bottom:var(--headerL) solid rgba(255,255,255,.15);box-shadow:none} .drK .blogCont::before{border-right:1px solid rgba(255,255,255,.15)} .drK .mnBrs{box-shadow:none}} @media screen and (min-width:641px){.drK .ftMn a:hover{color:var(--darkU)}} @media screen and (max-width:640px){.drK .ftMn{background:var(--darkBa)}} @media screen and (max-width:500px){.drK .itemFt .itm .iCtnt{background:var(--darkBa)}}
/*]]>*/</style>
  
  <b:if cond='data:view.isSingleItem'>
    <style>/*<![CDATA[*/
/* Greetings */ .pGV{display:flex;justify-content:space-between;position:relative;font-size:13px;padding-top:6px;padding-bottom:20px;} .pGV >*{padding:10px;background:var(--contentB);box-shadow:0 5px 35px rgba(0,0,0,.07);border-radius:var(--greetR);display:flex;align-items:center;justify-content:center} .pGV >*:first-child{margin-right:5px} .pGV >*:last-child{margin-left:5px} .Rtl .pGV >*:first-child{margin-right:0;margin-left:5px} .Rtl .pGV >*:last-child{margin-left:0;margin-right:5px} .pGV .pVws.hidden{display:none} .pGV .pGrt::after{content:attr(data-text)} .pGV .pVws::after{content:attr(data-text) ' views'} .pGV svg{width:15px;height:15px;margin-right:6px} .pGV svg.line{stroke-width:1.5} .Rtl .pGV svg{margin-right:0;margin-left:6px} .drK .pGV >*{background:var(--darkBa)}
/* Comments */ .pCmnts{margin-top:50px} .cmDis{text-align:center;margin-top:20px;opacity:.7} .cmMs{margin-bottom:20px} .drK .cmMs{background:var(--darkBa)} .cm iframe{width:100%} .cm:not(.cmBr) .cmBrs{background:transparent;position:relative;padding:80px 20px 0;width:calc(100% + 40px);left:-20px;right:-20px} .fixLs.cmBrs{padding-top:80px} .cmH h3.title{margin:0;flex-grow:1;padding:16px 10px} .cmH .s{margin:0 14px} .cmH .s::before{content:attr(data-text);margin:0 6px;opacity:.7;font-size:90%} .cmH .s::after{content:'\296E';line-height:18px;font-size:17px} .cmAv .im{width:35px;height:35px;border-radius:50%;position:relative} .cmBd.del .cmCo{font-style:italic;font-size:90%;line-height:normal;border:1px dashed rgba(0,0,0,.2);border-radius:3px;margin:.5em 0;padding:15px;opacity:.7; overflow:hidden;text-overflow:ellipsis;white-space:nowrap} .cmHr{line-height:24px; overflow:hidden;text-overflow:ellipsis;white-space:nowrap} .cmHr .d{font-size:90%;opacity:.7} .cmHr .d::before{content:'\00B7';margin:0 7px} .cmHr.a .n{display:inline-flex;align-items:center} .cmHr.a .n::after{content:'\2714';display:flex;align-items:center;justify-content:center;width:14px;height:14px;font-size:8px;background:#519bd6;color:#fefefe;border-radius:50%;margin:0 3px} .cmCo{line-height:1.6em;opacity:.9} .cmC i[rel=image]{font-size:90%; display:block;position:relative; min-height:50px; overflow:hidden;text-overflow:ellipsis;white-space:nowrap; margin:1em auto} .cmC i[rel=image]::before{content:'This feature isn\0027t available!';border:1px dashed rgba(0,0,0,.2);border-radius:3px;padding:10px;display:flex;align-items:center;justify-content:center;position:absolute;top:0;left:0;bottom:0;right:0;background:var(--contentB)} .cmC i[rel=pre], .cmC i[rel=quote]{margin-top:1em;margin-bottom:1em; font-style:normal;line-height:inherit;padding:20px} .cmC i[rel=pre]::before{display:block;width:auto} .cmC i[rel=quote]{display:block;font-style:italic;font-size:inherit;padding:5px 15px} .cmCo img{margin-top:1em;margin-bottom:1em} .cmAc{margin-top:10px} .cmAc a{font-size:90%;color:inherit;opacity:.7;display:inline-flex} .cmAc a::before{content:'\2934';line-height:18px;font-size:16px;transform:rotate(90deg)} .cmAc a::after{content:attr(data-text);margin:0 6px} .cmR{margin:10px 40px 0} .cmRp ~ .cmAc, .cmBd.del ~ .cmAc, .onItm:not(.Rtl) .cmHr .date{display:none} .cmRi:checked ~ .cmRp .thTg{margin-bottom:0} .cmRi:checked ~ .cmRp .thTg::after{content:attr(aria-label)} .cmRi:checked ~ .cmRp .thCh, .cmRi:checked ~ .cmRp .cmR{display:none} .cmAl:checked ~ .cm .cmH .s::before{content:attr(data-new)} .cmAl:checked ~ .cm .cmCn >ol{flex-direction:column-reverse} .thTg{display:inline-flex;align-items:center;margin:15px 0 18px;font-size:90%} .thTg::before{content:'';width:28px;border-bottom:1px solid var(--widgetTac);opacity:.5} .thTg::after{content:attr(data-text);margin:0 12px;opacity:.7} .cmCn ol{list-style:none;margin:0;padding:0;display:flex;flex-direction:column} .cmCn li{margin-bottom:18px;position:relative} .cmCn li .cmRbox{margin-top:20px} .cmCn li li{display:flex;flex-wrap:wrap;width:calc(100% + 12px);left:-6px;right:-6px} .cmCn li li:last-child{margin-bottom:0} .cmCn li li .cmAv{flex:0 0 28px;margin:0 6px} .cmCn li li .cmAv .im{width:28px;height:28px} .cmCn li li .cmIn{width:calc(100% - 52px);margin:0 6px} .cmHl >li{padding-left:17.5px} .cmHl >li >.cmAv{position:absolute;left:0;top:12px} .cmHl >li >.cmIn{background:var(--contentB);padding:12px 15px 12px 28px;border-radius:12px;box-shadow:0 0 30px rgba(0,0,0,.07)} .pCmnts .cmIn img{border-radius:3px} /* Comments Show/Hide */ #comment:target{margin:0;padding-top:60px} .cmSh:checked ~ .cmShw, .cmShw ~ .cm:not(.cmBr), #comment:target .cmShw, #comment:target .cmSh:checked ~ .cm:not(.cmBr){display:none} .cmSh:checked ~ .cm:not(.cmBr), #comment:target .cm:not(.cmBr), #comment:target .cmSh:checked ~ .cmShw{display:block} .cmBtn{display:block;padding:20px;text-align:center;max-width:100%} .cmBtn.ln:hover{color:var(--linkB)} /* Comments Pop-up */ #comment:target .cmSh:checked ~ .cm.cmBr{bottom:-100%;opacity:0;visibility:hidden} #comment:target .cmSh:checked ~ .cm.cmBr .fCls{opacity:0;visibility:hidden} .cmDl{position: absolute;top:15px;right:20px;width:20px;height:20px;background:#ffcdd2;border-radius:50%;transition:var(--trans-2)} .cmDl.rp{top:2.5px;right:10px} .cmDl:hover{opacity:.8;transform:scale(1.08)} .cmDl a{width:20px;height:20px;display:flex;justify-content:center;align-items:center} .cmDl a svg{width:12px;height:12px;stroke-width:1.2} .Rtl .cmDl{right:unset;left:20px} .Rtl .cmDl.rp{left:10px} .drK .cmDl{background:var(--darkB)} .iCmn{cursor:pointer;outline:none;border:none;display:block;font-weight:700;margin:0 auto;max-width:none;position:absolute;top:0;right:0;bottom:0;left:0;z-index:9;background:var(--contentB)} .iCmn::before{content:attr(data-text)} .iCmn:hover::before{opacity:.7} .drK .iCmn{background:var(--darkBs)}
/* Youtube fullpage */ .videoYt{position:relative;padding-bottom:56.25%; overflow:hidden;border-radius:5px} .videoYt iframe{position:absolute;width:100%;height:100%;left:0;right:0}
/* Lazy Youtube */ .lazyYt{background:var(--synxBg);position:relative;overflow:hidden;padding-top:56.25%;border-radius:5px} .lazyYt img{width:100%;top:-16.84%;left:0;opacity:.95} .lazyYt img, .lazyYt iframe, .lazyYt .play{position:absolute} .lazyYt iframe{width:100%;height:100%;bottom:0;right:0} .lazyYt .play{top:50%;left:50%; transform:translate3d(-50%,-50%,0); transition:all .5s ease;display:block;width:70px;height:70px;z-index:1;cursor:pointer} .lazyYt .play svg{width:inherit;height:inherit; fill:none;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;stroke-width:8} .lazyYt .play .c{stroke:rgba(255,255,255,.85);stroke-dasharray:650;stroke-dashoffset:650; transition:all .4s ease-in-out; opacity:.3} .lazyYt .play .t{stroke:rgba(255,255,255,.75);stroke-dasharray:240;stroke-dashoffset:480; transition:all .6s ease-in-out; transform:translateY(0)} .lazyYt .play:hover .t{animation:nudge .6s ease-in-out;-webkit-animation:nudge .6s ease-in-out} .lazyYt .play:hover .t, .lazyYt .play:hover .c{stroke-dashoffset:0; opacity:.7;stroke:#FF0000} .nAmp .lazyYt{display:none}
/* Quick Edit */ .qeMn, .qeBtn .svg-2{display:none} .qeBtn{position:fixed;bottom:20px;right:20px;background:#fff;box-shadow:0 0 15px rgba(0,0,0,.1);border-radius:50px;z-index:5;overflow:hidden;display:flex;align-items:center;justify-content:center;width:35px;height:35px;-webkit-transition:all .2s ease,margin 1s ease;transition:all .2s ease,margin 1s ease;cursor:pointer} .qeBtns .qeBtn.a{bottom:22.5px;right:22.5px;width:30px;height:30px;z-index:4;box-shadow:none;opacity:0} .qeBtns .qeBtn.a:hover{-webkit-transform:scale(1.05);transform:scale(1.05);opacity:1} .qeBtn svg{width:18px;height:18px;margin:auto} .qeBtn svg.line{stroke-width:1.3} .qeBtn.a svg{width:16px;height:16px} .qeMn:checked + .qeBtn{-webkit-transform:rotate(360deg);transform:rotate(360deg)} .qeMn:checked + .qeBtn .svg-1{display:none} .qeMn:checked + .qeBtn .svg-2{display:block} .qeMn:checked ~ .qeBtns .qeBtn{box-shadow:0 0 15px rgba(0,0,0,.1)} .qeMn:checked ~ .qeBtns .qeBtn.a{opacity:1} .qeMn:checked ~ .qeBtns .qeBtn.a:nth-child(1){bottom:60px} .qeMn:checked ~ .qeBtns .qeBtn.a:nth-child(2){bottom:95px} .qeMn:checked ~ .qeBtns .qeBtn.a:nth-child(3){bottom:130px} .qeMn:checked ~ .qeBtns .qeBtn.a:nth-child(4){right:60px} .qeMn:checked ~ .qeBtns .qeBtn.a:nth-child(5){right:95px} .qeMn:checked ~ .qeBtns .qeBtn.a:nth-child(6){right:130px} .qeMn:checked ~ .qeBtns + .fCls{visibility:visible;opacity:1;z-inex:4} @media screen and (max-width:896px){.qeBtn{margin-bottom:60px;margin-right:55px} .qEdit.nBtm .qeBtn{margin-bottom:150px;margin-right:55px} .MN-4 .qeBtn{margin-bottom:60px;margin-right:5px} .MN-4 .qEdit.nBtm .qeBtn{margin-right:55px;margin-bottom:85px}} @media screen and (min-width:897px){.qeBtn{margin-bottom:5px;margin-right:55px}} .drK .qeBtn{background:var(--darkBa)}
/* Preloader */ .pldW{direction:ltr;position:fixed;top:0;right:0;bottom:0;left:0;display:none;background:var(--bodyB);align-items:center;justify-content:center;z-index:9999;visibility:visible;opacity:1;transition:all .8s ease} .pldW.e{display:flex} .pldW.h{opacity:0;visibility:hidden} .pldS {position:relative;transform:rotate(165deg)} .pldS:before, .pldS:after{content:'';position:absolute;display:block;width:.5em;height:.5em;border-radius:.25em;transform:translate(-50%,-50%)} .pldS:before{animation:pldBefore 2s infinite} .pldS:after{animation:pldAfter 2s infinite} @keyframes pldBefore{0%{width:.5em;box-shadow:1em -.5em rgba(225,20,98,.75),-1em .5em rgba(111,202,220,.75)}35%{width:2.5em;box-shadow:0 -.5em rgba(225,20,98,.75),0 .5em rgba(111,202,220,.75)}70%{width:.5em;box-shadow:-1em -.5em rgba(225,20,98,.75),1em .5em rgba(111,202,220,.75)}100%{box-shadow:1em -.5em rgba(225,20,98,.75),-1em .5em rgba(111,202,220,.75)}} @keyframes pldAfter{0%{height:.5em;box-shadow:.5em 1em rgba(61,184,143,.75),-.5em -1em rgba(233,169,32,.75)}35%{height:2.5em;box-shadow:.5em 0 rgba(61,184,143,.75),-.5em 0 rgba(233,169,32,.75)}70%{height:.5em;box-shadow:.5em -1em rgba(61,184,143,.75),-.5em 1em rgba(233,169,32,.75)}100%{box-shadow:.5em 1em rgba(61,184,143,.75),-.5em -1em rgba(233,169,32,.75)}} .drK .pldW{background:var(--darkB)}
/* Lightbox image */ .zmImg:not(.s){position:relative} .zmImg:not(.s)::before{content:'';position:absolute;top:10px;right:5px;width:30px;height:30px;display:flex;margin:0 5px;background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23363637' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><path d='M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3'/></svg>") center / 14px no-repeat;background-color:rgba(0,0,0,.2);border-radius:50%;z-index:2;opacity:0;transition:var(--trans-2);-webkit-transition:var(--trans-2);cursor:pointer} .zmImg:not(.s):hover::before{opacity:1} .zmImg.s{position:fixed;top:0;left:0;bottom:0;right:0;width:100%;margin:0;background:rgba(0,0,0,.75); display:flex;align-items:center;justify-content:center;z-index:999; -webkit-backdrop-filter:saturate(180%) blur(15px); backdrop-filter:saturate(180%) blur(15px)} .zmImg.s a, .zmImg.s a:hover{opacity:1} .zmImg.s img{display:block;max-width:92%;max-height:92%;width:auto;margin:auto;border-radius:10px;box-shadow:0 5px 30px 0 rgba(0,0,0,.05)} .zmImg.s img.full{left:auto;right:auto;border-radius:10px;width:auto} .zmImg::after{content:'\2715';line-height:16px;font-size:14px;color:#fffdfc;background:var(--linkB); position:fixed;bottom:-20px;right:-20px; display:flex;align-items:center;justify-content:center;width:45px;height:45px;border-radius:50%; transition:var(--trans-1);opacity:0;visibility:hidden} .zmImg.s::after{bottom:20px;right:20px;opacity:1;visibility:visible;cursor:pointer}
/* Highlight */ .pre .pre-subst{color:var(--synxC)} .pre .pre-comment{color:#656e77} .pre .pre-attr, .pre .pre-doctag, .pre .pre-meta, .pre .pre-meta-keyword, .pre .pre-section, .pre .pre-selector-class{color:#015692} .pre .pre-selector-pseudo, .pre .pre-keyword{color:#0a8356} .pre .pre-selector-tag{color:#167406} .pre .pre-attribute{color:#803378} .pre .pre-built_in, .pre .pre-literal, .pre .pre-name, .pre .pre-number, .drK .pre .pre-number, .pre .pre-quote, .pre .pre-selector-id, .pre .pre-template-tag, .pre .pre-title, .pre .pre-type{color:#b75501} .pre .pre-link, .pre .pre-meta-string, .pre .pre-regexp, .pre .pre-selector-attr, .pre .pre-string, .pre .pre-symbol, .pre .pre-template-variable, .pre .pre-variable{color:#54790d} .pre .pre-bullet, .pre .pre-code{color:#535a60} .pre .pre-deletion{color:#c02d2e} .pre .pre-addition{color:#2f6f44} .pre .pre-emphasis{font-style:italic} .pre .pre-strong{font-weight:700} .drK .pre{color:var(--darkT)} .drK .pre .pre-subst, .drK .pre .pre-property, .drK .pre .pre-function{color:#fefefe} .drK .pre .pre-strong{font-weight:bold} .drK .pre .pre-code, .drK .pre .pre-emphasis{font-style:italic} .drK .pre .pre-tag{color:#62c8f3} .drK .pre .pre-name{color:#ff7086} .drK .pre .pre-attr{color:#ffb244} .drK .pre .pre-variable, .drK .pre .pre-template-variable, .drK .pre .pre-selector-id, .drK .pre .pre-selector-class{color:#ade5fc} .drK .pre .pre-string, .drK .pre .pre-bullet{color:#a2fca2} .drK .pre .pre-type, .drK .pre .pre-title, .pre .pre-section, .drK .pre .pre-attribute, .drK .pre .pre-quote, .drK .pre .pre-built_in, .drK .pre .pre-builtin-name{color:#ffa} .pre .pre-number, .drK .pre .pre-symbol, .drK .pre .pre-bullet{color:#d36363} .drK .pre .pre-keyword, .drK .pre .pre-literal{color:#fcc28c} .drK .pre .pre-selector-pseudo{color:#fbc796} .drK .pre .pre-selector-tag{color:#fbb46e} .drK .pre .pre-comment, .drK .pre .pre-deletion, .drK .pre .pre-code{color:#888} .pre .pre-regexp, .drK .pre .pre-regexp, .drK .pre .pre-link{color:#c6b4f0} .drK .pre .pre-meta{color:#fc9b9b} .drK .pre .pre-deletion{background-color:#fc9b9b;color:#333} .drK .pre .pre-addition{background-color:#a2fca2;color:#333}
/* Post Next or Prev */ .chpN{margin:4em 0 0;display:flex;justify-content:space-between;flex-wrap:wrap;position:relative;width:calc(100% + 20px);left:-10px;right:-10px; font-size:14px;font-family:var(--fontB);line-height:1.6em} .chpN >*{margin:0 10px;flex:0 0 calc(50% - 20px);display:inline-flex;flex-direction:column;color:inherit!important;cursor:default} .chpN >*::before{content:'Previous Chapter'} .chpN >*:not(:first-child)::before{content:'Next Chapter'} .chpN >*::after{content:attr(data-text);display:block; /*display:-webkit-box;*/-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden; opacity:.6; font-size:12px;font-family:var(--fontBa);line-height:1.5em;margin-top:4px;cursor:pointer} .chpN >*:hover::after{text-decoration:underline} .chpN >*:not(:first-child){align-items:flex-end; text-align:right} .chpN.n{justify-content:flex-end} .chpN.p{justify-content:flex-start} /*]]>*/</style>
  </b:if>
  <b:defaultmarkups>
    <!--[ Blogger defaultmarkups ]-->
    <b:defaultmarkup type='Common'>
      <!--[By Dr-FarFar.CoM]-->
      <!--[ Template settings object ]-->
      <b:includable id='settings-object'>/*<![CDATA[*/
const PuSet = {
  license: {
    key: licenseKey
  },
  realViews: {
    databaseUrl: "",
    abbreviation: "true"
  },
  noInternet: {
    enablePopup: "true",
    enableToast: "true",
    offlineMes: "You are Offline!",
    onlineMes: "You are Online!"
  },
  preCopy: {
    copiedMes: "<i class='clipboard'></i>Copied to Clipboard!"
  },
  cookieCon: {
    consentMaxAge: "2592000",
    cookieError: "Error: Cookie can&#039;t be set! Please unblock this site from the cookie setting of your browser."
  },
  gTranslate: {
    pageLang: "en",
    includedLangs: "en,hi,gu,bn,ta,te,mr,ne,id",
    autoDisplay: "true",
    multiLangPage: "true"
  },
  bookmark: {
    bmTitle: "Bookmark Posts",
    closeText: "Close",
    noBmIcon: "<svg class='line' viewBox='0 0 24 24'><g transform='translate(3.650100, 2.749900)'><path d='M16.51,5.55 L10.84,0.15 C10.11,0.05 9.29,0 8.39,0 C2.1,0 -1.95399252e-14,2.32 -1.95399252e-14,9.25 C-1.95399252e-14,16.19 2.1,18.5 8.39,18.5 C14.69,18.5 16.79,16.19 16.79,9.25 C16.79,7.83 16.7,6.6 16.51,5.55 Z'/><path d='M10.2839,0.0827 L10.2839,2.7437 C10.2839,4.6017 11.7899,6.1067 13.6479,6.1067 L16.5989,6.1067'/><line class='svgC' x1='10.6623' y1='10.2306' x2='5.7623' y2='10.2306'/><line class='svgC' x1='8.2131' y1='12.6808' x2='8.2131' y2='7.7808'/></g></svg>",
    noBmMes: "The list of favorite articles does not exist yet...",
    noBmAll: "View all articles",
    noBmLink: "/search",
    delIcon: "<svg class='line' viewBox='0 0 24 24'><g transform='translate(3.500000, 2.000000)'><path d='M15.3891429,7.55409524 C15.3891429,15.5731429 16.5434286,19.1979048 8.77961905,19.1979048 C1.01485714,19.1979048 2.19295238,15.5731429 2.19295238,7.55409524'/><line x1='16.8651429' y1='4.47980952' x2='0.714666667' y2='4.47980952'/><path d='M12.2148571,4.47980952 C12.2148571,4.47980952 12.7434286,0.714095238 8.78914286,0.714095238 C4.83580952,0.714095238 5.36438095,4.47980952 5.36438095,4.47980952'/></g></svg>",
    addedNtf: "<i class='check'></i>Added to Bookmarks!",
    removedNtf: "<i class='del'></i>Removed from Bookmarks!",
  },
  adsenseAds: {
    publisherId: caPubAdsense.replace('ca-pub-',''), /* Auto or replace with Publisher ID i.e. "000000000000", */
    antiAdBClose: "true", /* "true", if you want to add a Close Button in Anti Ad Block popup */
    ignoreMaxAge: "86400",
    loadType: "defer" /* lazy, defer, scroll */
  },
  analytics: {
    propertyID: analyticsID /* Auto or replace with Property ID i.e. "UA-0000000", */
  },
  fontFamily: {
    mobileFonts: "@font-face{font-family:'Google Sans Text';font-style:normal;font-weight:400;font-display:swap;src:local('Google Sans Text'),local('Google-Sans-Text'),url(https://fonts.gstatic.com/s/googlesanstext/v16/5aUu9-KzpRiLCAt4Unrc-xIKmCU5qEp2iw.woff2) format('woff2')} @font-face{font-family:'Google Sans Text';font-style:normal;font-weight:700;font-display:swap;src:local('Google Sans Text'),local('Google-Sans-Text'),url(https://fonts.gstatic.com/s/googlesanstext/v16/5aUp9-KzpRiLCAt4Unrc-xIKmCU5oPFTnmhjtg.woff2) format('woff2')} @font-face{font-family:'Google Sans Mono';font-style:normal;font-weight:400;font-display:swap;src:local('Google Sans Mono'),local('Google-Sans-Mono'),url(https://fonts.gstatic.com/s/googlesansmono/v4/P5sUzYWFYtnZ_Cg-t0Uq_rfivrdYH4RE8-pZ5gQ1abT53wVQGrk.woff2) format('woff2')}"
  }
};
/*]]>*/</b:includable>
      <!--[By Dr-FarFar.CoM]-->
      <!--[ In-feed ad ]-->
      <b:includable id='post-adIn'>
        <div class='ntry pAdin'>
          <!--[ InFeed Ad ]-->
          
          <!--<ins class='adsbygoogle'/>
          <script>...</script>-->
          
        </div>
      </b:includable>
      
      <!--[ Top article ad ]-->
      <b:includable id='post-adTop'>
        <div class='pAd'>
          <!--[ Top article Ad ]-->
          
          <!--<ins class='adsbygoogle'/>
          <script>...</script>-->
          
        </div>
      </b:includable>
      
      <!--[ Bottom article ad ]-->
      <b:includable id='post-adBot'>
        <div class='pAd'>
          <!--[ Bottom article Ad ]-->
          
          <!--<ins class='adsbygoogle'/>
          <script>...</script>-->
          
        </div>
      </b:includable>
      
      <!--[ Matched content Ad ]-->
      <b:includable id='post-relatedAd'>
        <div class='relatedPosts'>
          <!--[ Matched content Ad ]-->
          
          <!--<ins class='adsbygoogle'/>
          <script>...</script>-->
          
        </div>
      </b:includable>
      
      <!--[ Index title ]-->
      <b:includable id='titlePost'>
        <div class='blogTtl'>
          <b:class cond='data:view.isHomepage' name='hm'/>
          <b:if cond='data:view.isHomepage'>
            
            <!--[ Change <data:messages.latestPosts/> to replace 'Latest Posts' with your special text ]-->
            <h2 class='title dt'><data:messages.latestPosts/></h2>

            <b:else/>
            <div class='t'>
              <b:class cond='data:view.search or data:view.isArchive and data:view.url != data:blog.homepageUrl.canonical path &quot;search&quot;' name='srch'/>
              <b:if cond='data:view.search.label'>
                <span class='hm' expr:data-text='data:messages.home'/><data:blog.pageName/>
                
                <b:elseif cond='data:view.search.query'/>
                <span expr:data-text='data:messages.search + &quot;:&quot;'/><data:view.search.query/>
                
                <b:elseif cond='data:view.isArchive'/>
                <span expr:data-text='data:messages.blogArchive + &quot;:&quot;'/><data:blog.pageName/>
                
                <b:else/>

                <!--[ Change all <data:messages.posts/> to replace 'Posts' with your special text ]-->
                <span class='hm' expr:data-text='data:messages.home'/><data:messages.posts/>
             
              </b:if>
            </div>
          </b:if>
        </div>
      </b:includable>
      <!--[By Dr-FarFar.CoM]-->
      <!--[ Theme Customisation ]-->
      <b:includable id='theme-custom'>
        <input class='cusI hidden' id='forCusThm' type='checkbox'/>
        <div class='cusW'>
          <div class='cusH'>
            <span class='cusHi' data-text='Theme Color'/>
            <label class='cusCl' for='forCusThm'/>
          </div>
          <div class='cusP'>
            <!--[ Replace color hex code with custom color hex code ]-->
            <span class='tPkr thB0' onclick='webTheme(&quot;theme0&quot;);modeL()' style='--pkrC:#eceff1'/><span class='tPkr thB1' onclick='webTheme(&quot;theme1&quot;);modeL()' style='--pkrC:#F44336'/><span class='tPkr thB2' onclick='webTheme(&quot;theme2&quot;);modeL()' style='--pkrC:#00BFA5'/><span class='tPkr thB3' onclick='webTheme(&quot;theme3&quot;);modeL()' style='--pkrC:#2196F3'/><span class='tPkr thB4' onclick='webTheme(&quot;theme4&quot;);modeL()' style='--pkrC:#FBC02D'/><span class='tPkr thB5' onclick='webTheme(&quot;theme5&quot;);modeL()' style='--pkrC:#E91E63'/><span class='tPkr thB6' onclick='webTheme(&quot;theme6&quot;);modeL()' style='--pkrC:#FF5722'/><span class='tPkr thB7' onclick='webTheme(&quot;theme7&quot;);modeL()' style='--pkrC:#607D8B'/><span class='tPkr thB8' onclick='webTheme(&quot;theme8&quot;);modeL()' style='--pkrC:#5D4037'/><span class='tPkr thB9' onclick='webTheme(&quot;theme9&quot;);modeL()' style='--pkrC:#744D97'/><span class='tPkr thB10' onclick='webTheme(&quot;theme10&quot;);modeL()' style='--pkrC:#3949AB'/>
          </div>
        </div>
        <label class='fCls' for='forCusThm'/>
      </b:includable>
      
      <!--[ Related post ]-->
      <b:includable id='post-related'>
        <div id='rPst'/>
        <script>
          var labelArray = [<b:if cond='data:post.labels'><b:loop values='data:post.labels' var='label'>&quot;<data:label.name/>&quot;<b:if cond='data:label.isLast != &quot;true&quot;'>,</b:if></b:loop></b:if>];
          
          // Auto Related Posts (Bottom)
          var relatedPostConfig = {
            homePage: &quot;<data:blog.homepageUrl.canonical/>&quot;,
            
            // Replace <data:messages.youMayLikeThesePosts/> to change Related Posts title
            widgetTitle: &quot;<h2 class='title dt'><b:if cond='data:post.labels none (label =&gt; label.name in [ &quot;Product&quot; ])'><data:messages.youMayLikeThesePosts/><b:else/>Related products!</b:if></h2>&quot;,

            numPosts: 6,
            summaryLength: 180,
            titleLength:&quot;auto&quot;,
            thumbnailSize: 300,
            noImage: &quot;data:image/png;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=&quot;,
            containerId: &quot;rPst&quot;,
            newTabLink: false,
            moreText: &quot;Read more&quot;,
            
            // Change the related post style, there are 4 styles available
            widgetStyle: 3,
            callBack:function(){}
          }

          // Auto Related Posts (In-Article)
          var autoRelatedConfig = {
            homePage: &quot;<data:blog.homepageUrl.canonical/>&quot;,
            numPosts: 10,
            titleLength:&quot;auto&quot;,
            newTabLink: false,
            callBack:function(){}
          }
          
          // Auto Chapter
          var autoChapterConfig = {
            homePage: &quot;<data:blog.homepageUrl.canonical/>&quot;,
            titleLength: &quot;60&quot;,
            newTabLink: false,
            callBack:function(){}
          }
        </script>
      </b:includable>
      <!--[By Dr-FarFar.CoM]-->
      <!--[ Preloader ]-->
      <b:includable id='preloader'>
        <!--[ Disable all the codes to remove preloader ]-->
        <!--<div class='pldW' id='preloader'>
          <div class='pldS'/>
        </div>
        <script>/*<![CDATA[*/ var preloader=document.querySelector('#preloader'); preloader.classList.add('e'); document.addEventListener('DOMContentLoaded', function(){setTimeout(function(){preloader.classList.add('h')},2000); neonLight(5000);}); /*]]>*/</script>-->
      </b:includable>
      <!--[By Dr-FarFar.CoM]-->
      <!--[ In-Post Quick Edit ]-->
      <b:includable id='quick-edit'>
        <div class='blog-admin qEdit' id='qEdit'><input class='qeMn hidden' id='offqeMn' type='checkbox'/><label class='qeBtn' for='offqeMn'><b:include name='settings-icon'/><b:include name='close-icon'/></label><div class='qeBtns'><a class='qeBtn a' expr:href='&quot;https://www.blogger.com/blog/settings/&quot; + data:blog.blogId' rel='nofollow noopener noreferrer' target='_blank' title='Settings'><b:include name='settings-icon'/></a><a class='qeBtn a' expr:href='&quot;https://www.blogger.com/blog/comments/&quot; + data:blog.blogId' rel='nofollow noopener noreferrer' target='_blank' title='Comments'><b:include name='comments-icon'/></a><a class='qeBtn a' expr:href='&quot;https://www.blogger.com/feeds/&quot; + data:blog.blogId + &quot;/archive&quot;' rel='nofollow noopener noreferrer' title='Backup content'><b:include name='download-alt-icon'/></a><a class='qeBtn a' expr:href='&quot;https://www.blogger.com/blog/stats/week/&quot; + data:blog.blogId' rel='nofollow noopener noreferrer' target='_blank' title='7-days blog stats'><b:include name='stats-icon'/></a><b:if cond='data:view.isPost'><a class='qeBtn a' expr:href='&quot;https://www.blogger.com/blog/posts/&quot; + data:blog.blogId' rel='nofollow noopener noreferrer' target='_blank' title='Dashboard'><b:include name='list-icon'/></a><b:else/><a class='qeBtn a' expr:href='&quot;https://www.blogger.com/blog/pages/&quot; + data:blog.blogId' rel='nofollow noopener noreferrer' target='_blank' title='Dashboard'><b:include name='list-icon'/></a></b:if><b:if cond='data:view.isPost'><a class='qeBtn a' expr:href='&quot;https://www.blogger.com/blog/post/edit/&quot; + data:blog.blogId + &quot;/&quot; + data:post.id' rel='nofollow noopener noreferrer' target='_blank' title='Edit post'><b:include name='edit-icon'/></a><b:else/><a class='qeBtn a' expr:href='&quot;https://www.blogger.com/blog/page/edit/&quot; + data:blog.blogId + &quot;/&quot; + data:post.id' rel='nofollow noopener noreferrer' target='_blank' title='Edit page'><b:include name='edit-icon'/></a></b:if></div><label class='fCls' for='offqeMn'/></div>
      </b:includable>
      <!--[By Dr-FarFar.CoM]-->
      <!--[ Quick Edit on Thumbnails ]-->
      <b:includable id='thmb-quickedit'>
        <div class='blog-admin'>
          <div class='iFxd l'>
            <a aria-label='Edit this post' class='edit' data-text='Edit' expr:href='&quot;https://www.blogger.com/blog/post/edit/&quot; + data:blog.blogId + &quot;/&quot; + data:post.id' role='button' target='_blank'><b:include name='edit-alt-icon'/></a>
          </div>
        </div>
      </b:includable>
      
      <!--[ Post Bookmark on Thumbnails ]-->
      <b:includable id='thmb-bookmark'>
        <span aria-label='Read later' class='bM bmPs' data-added='Remove' expr:bm-id='data:post.id' expr:bm-img='resizeImage(data:post.featuredImage, 240, &quot;1:1&quot;)' expr:bm-ttl='data:post.title' expr:bm-url='data:post.url' role='button'><b:include name='bookmark-alt-icon'/></span>
      </b:includable>
      
      <!--[ Post authors ]-->
      <b:includable id='post-authorProfile'>
        <div class='admAbt'>
          <h2 class='title dt'>About the Author</h2>
          <div class='admPs'>
            <div class='admIm'>
              <b:include name='post-authorImage'/>
            </div>
            <div class='admI'>
              <bdi class='admN' expr:data-text='data:post.author.name' expr:data-write='data:messages.postedBy'/>
              <div class='admA'>
                <b:eval expr='data:post.author.aboutMe snippet {length: 250, links: true, linebreaks: true}'/>
                <!--<data:post.author.aboutMe/>-->
              </div>
            </div>
          </div>
        </div>
      </b:includable>
      
       <!--[ Thumbnail Post Authors ]-->
      <b:includable id='thmb-athr'>
        <div class='aNm t'>
          <b:if cond='data:post.author.authorPhoto.image'>
            <div class='im lazy' expr:data-style='&quot;background-image: url(&quot; + resizeImage(data:post.author.authorPhoto.image,20,&quot;1:1&quot;) + &quot;)&quot;'>
            </div>
          </b:if>
          <bdi class='nm' expr:data-text='data:post.author.name'/>
          <svg viewBox='0 0 24 24'><path d='M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z'/></svg>
        </div>
      </b:includable>
      
      <b:includable id='post-authorImage'>
        <b:if cond='!data:view.isPost'>
          <b:if cond='data:post.author.authorPhoto.image'>
            <div class='im' expr:data-style='&quot;background-image: url(&quot; + resizeImage(data:post.author.authorPhoto.image,20,&quot;1:1&quot;) + &quot;)&quot;'>
              <b:class cond='data:view.url != data:view.url params { amp: &quot;1&quot; }' name='lazy'/>
              <b:attr cond='data:view.url == data:view.url params { amp: &quot;1&quot; }' expr:value='&quot;background-image: url(&quot; + resizeImage(data:post.author.authorPhoto.image,20,&quot;1:1&quot;) + &quot;)&quot;' name='style'/>
            </div>
            <noscript><div class='im' expr:style='&quot;background-image: url(&quot; + resizeImage(data:post.author.authorPhoto.image,20,&quot;1:1&quot;) + &quot;)&quot;'/></noscript>
            <b:elseif cond='!data:view.isMultipleItems'/>
            <div class='im'><b:include name='profile-icon'/></div>
          </b:if>
          <b:else/>
          <b:if cond='data:post.author.authorPhoto.image'>
            <div class='im lazy' expr:data-style='&quot;background-image: url(&quot; + resizeImage(data:post.author.authorPhoto.image,40,&quot;1:1&quot;) + &quot;)&quot;'>
              <b:class cond='data:view.url != data:view.url params { amp: &quot;1&quot; }' name='lazy'/>
              <b:attr cond='data:view.url == data:view.url params { amp: &quot;1&quot; }' expr:value='&quot;background-image: url(&quot; + resizeImage(data:post.author.authorPhoto.image,20,&quot;1:1&quot;) + &quot;)&quot;' name='style'/>
            </div>
            <noscript><div class='im' expr:style='&quot;background-image: url(&quot; + resizeImage(data:post.author.authorPhoto.image,40,&quot;1:1&quot;) + &quot;)&quot;'/></noscript>
            <b:else/>
            <div class='aFl'>
              <span><data:post.author.name/></span>
            </div>
          </b:if>
        </b:if>
      </b:includable>
      
      <b:includable id='post-authorName'>
        <!--[ Post Author without Link ]-->
        <!--<bdi class='nm' expr:data-text='data:post.author.name' expr:data-write='data:widgets.Blog.first.allBylineItems.author.label'/>-->
        
        <!--[ Post Author with Link ]-->
        <b:tag class='nm' expr:data-text='data:post.author.name' expr:data-write='data:widgets.Blog.first.allBylineItems.author.label' expr:name='data:post.author.profileUrl ? &quot;a&quot; : &quot;span&quot;'>
          <b:attr cond='data:post.author.profileUrl' expr:value='data:post.author.name' name='aria-label'/>
          <b:attr cond='data:post.author.profileUrl' expr:value='data:post.author.profileUrl' name='href'/>
          <b:attr cond='data:post.author.profileUrl' name='rel' value='author noreferrer'/>
          <b:attr cond='data:post.author.profileUrl' name='target' value='_blank'/>
        </b:tag>
      </b:includable>
      
      <!--[ Post article ]-->
      <b:includable id='postHeaders'>
        <div class='pHdr pSml'>
          <b:include name='postLabelorSponsor'/>
        </div>
      </b:includable>
      
      <b:includable id='postCommentsLinks'>
        <b:if cond='data:post.allowComments and data:post.numberOfComments &gt; 0'>
          <a class='cmnt' expr:aria-label='data:messages.comments' expr:data-text='data:post.numberOfComments' expr:href='data:post.url.canonical fragment &quot;comment&quot;' role='button'>
            <b:include name='chat-icon'/>
          </a>
        </b:if>
      </b:includable>
      
      <b:includable id='postCommentsUrl'>
        <b:if cond='data:post.allowComments and data:post.numberOfComments &gt; 0'>
          <a class='cmnt' expr:aria-label='data:messages.comments' expr:data-text='data:post.numberOfComments' expr:href='data:post.url.canonical fragment &quot;comment&quot;' role='button'>
            <b:class cond='data:view.isSingleItem' name='tIc'/>
            <b:include name='chat-icon'/>
          </a>
        </b:if>
      </b:includable>
      
      <b:includable id='postCommentsLabel'>
        <b:if cond='data:post.allowComments and data:post.numberOfComments &gt; 0'>
          <label class='cmnt' expr:data-text='data:post.numberOfComments' for='forComments'>
            <b:class cond='data:view.isSingleItem' name='tIc'/>
            <b:include name='chat-icon'/>
          </label>
        </b:if>
      </b:includable>
      
      <b:includable id='postSponsored'>
        <div class='spnr'>
          <b:include name='link-icon'/>
        </div>
      </b:includable>
      
      <b:includable id='postProduct'>
        <div class='spnr'>
          <b:include name='tag-icon'/>
        </div>
      </b:includable>
      
      <b:includable id='postLabelSponsored'>
        <div class='pLbls nSpr'>
          <b:loop index='s' values='data:post.labels' var='label'>
            <b:if cond='data:s == 0'>
              <b:tag expr:data-text='data:label.name' name='span'/>
            </b:if>
          </b:loop>
        </div>
      </b:includable>
      
      <b:includable id='postLabel'>
        <b:if cond='data:widgets.Blog.first.allBylineItems.labels'>
          <div class='pLbls'>
            <b:attr cond='data:post.labels' expr:value='data:widgets.Blog.first.allBylineItems.labels.label' name='data-text'/>
            <b:loop index='i' values='data:post.labels' var='label'>
              <b:if cond='data:i &lt;= 1'>
                <b:tag expr:data-text='data:label.name' expr:name='data:blog.url != data:label.url ? &quot;a&quot; : &quot;span&quot;'>
                  <b:attr cond='data:blog.url != data:label.url' expr:value='data:label.url.canonical' name='href'/>
                  <b:attr cond='data:blog.url != data:label.url' name='rel' value='tag'/>
                  <b:attr cond='data:blog.url != data:label.url' expr:value='data:label.name' name='aria-label'/>
                </b:tag>
              </b:if>
            </b:loop>
          </div>
        </b:if>
      </b:includable>
      
      <b:includable id='postLabelorSponsor'>
        <b:if cond='data:post.labels none (label =&gt; label.name in [ &quot;Sponsored&quot; ])'>
          <!--[ Post labels ]-->
          <b:include cond='data:post.labels' name='postLabel'/>
          <b:else/>
          <b:include name='postLabelSponsored'/>
        </b:if>
      </b:includable>
      
      <b:includable id='postJumpLinks'>
        <a class='pJmp' expr:aria-label='data:blog.jumpLinkMessage' expr:data-text='data:messages.keepReading' expr:href='data:post.url.canonical'/>
      </b:includable>
      
      <b:includable id='postTimestampPublish'>
        <time class='aTtmp pTtmp pbl' data-type='Published' expr:data-text='format(data:post.date, &quot;MMM d, YYYY&quot;)' expr:datetime='data:post.date.iso8601' expr:title='&quot;Published: &quot; + data:post.date format &quot;MMMM d, YYYY&quot;'/>
      </b:includable>
      
      <b:includable id='postTimestamps'>
        <b:if cond='data:post.lastUpdated != data:post.date'>
          <time class='aTtmp pTtmp upd' data-date='Updated:' expr:data-text='format(data:post.lastUpdated, &quot;MMMM d, YYYY&quot;)' expr:data-time='format(data:post.lastUpdated, &quot;MMM d, YYYY&quot;)' expr:datetime='data:post.lastUpdated.iso8601' expr:title='&quot;Last updated: &quot; + data:post.lastUpdated format &quot;MMMM d, YYYY&quot;'/>
          <b:else/>
          <time class='aTtmp pTtmp pbl' expr:data-text='format(data:post.date, &quot;MMMM d, YYYY&quot;)' expr:data-time='format(data:post.lastUpdated, &quot;MMM d, YYYY&quot;)' expr:datetime='data:post.date.iso8601' expr:title='&quot;Published: &quot; + data:post.date format &quot;MMMM d, YYYY&quot;'/>
        </b:if>
      </b:includable>
      
      <b:includable id='postEntrySnippet'>
        <b:eval expr='snippet(data:post.snippets.long, {length: 150, links: false, linebreaks: false})'/>
        <!--<b:eval expr='snippet(data:post.body, {length: 90, links: false, linebreaks: false})'/>-->
      </b:includable>
      
      <b:includable id='postEntryThumbnail'>
        <b:if cond='data:post.featuredImage.isYoutube'>
          <img class='imgThm lazy' expr:alt='data:post.title ? data:post.title : data:messages.image' expr:data-src='data:post.featuredImage.youtubeMaxResDefaultUrl.isResizable ? resizeImage(data:post.featuredImage.youtubeMaxResDefaultUrl, 600, &quot;18:9&quot;) : data:post.featuredImage.youtubeMaxResDefaultUrl' src='data:image/png;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs='/>
          <noscript><img class='imgThm' expr:alt='data:post.title ? data:post.title : data:messages.image' expr:src='data:post.featuredImage.youtubeMaxResDefaultUrl.isResizable ? resizeImage(data:post.featuredImage.youtubeMaxResDefaultUrl, 600, &quot;18:9&quot;) : data:post.featuredImage.youtubeMaxResDefaultUrl'/></noscript>
          <b:else/>          
          <img class='imgThm lazy' expr:alt='data:post.title ? data:post.title : data:messages.image' expr:data-src='data:post.featuredImage.isResizable ? resizeImage(data:post.featuredImage, 600, &quot;18:9&quot;) : data:post.featuredImage' src='data:image/png;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs='/>
          <noscript><img class='imgThm' expr:alt='data:post.title ? data:post.title : data:messages.image' expr:src='data:post.featuredImage.isResizable ? resizeImage(data:post.featuredImage, 600, &quot;18:9&quot;) : data:post.featuredImage'/></noscript>
        </b:if>
      </b:includable>
      <!--[By Dr-FarFar.CoM]-->
      <!--[ Comment button ]-->
      <b:includable id='post-commentButton'>
        <!--[ Show/hide Comment ]-->
        <div class='cmShw'>
          <label class='cmBtn button' for='forComments'>
            <!--[ Delete tag bellow to change button style ]-->
            <b:class name='ln'/>
          
            <b:if cond='data:post.numberOfComments &gt; 0'>
              <span><data:messages.joinTheConversation/> (<data:post.numberOfComments/>)</span>
              <b:else/>
              <data:messages.postAComment/>
            </b:if>
          </label>
        </div>
      </b:includable>
      
      <!--[ Comment disqus ]-->
      <b:includable id='post-commentDisqus'>
        <div class='cmDisqus' id='disqus_thread'>
          <div class='cmBtn button' id='disqusshow' onclick='load_Comments()'>
            <!--[ Delete tag bellow to change button style ]-->
            <b:class name='ln'/>
            
            <span><data:messages.joinTheConversation/></span>
          </div>
        </div>
        <script>/*<![CDATA[*/ var disqus_shortname = "nldblog"; !function(){var e=document.createElement("script");e.defer=!0,e.src="//"+disqus_shortname+".disqus.com/blogger_item.js",(document.getElementsByTagName("head")[0]||document.getElementsByTagName("body")[0]).appendChild(e)}(); function load_Comments(){var e=document.getElementById("disqusshow");e.style.display="none";var t="nldblog";!function(){var e=document.createElement("script");e.defer=!0,e.src="https://"+t+".disqus.com/embed.js",(document.getElementsByTagName("head")[0]||document.getElementsByTagName("body")[0]).appendChild(e)}()}; var uri = window.location.toString(); if (uri.indexOf("?m=1","?m=1") > 0) {var clean_uri = uri.substring(0, uri.indexOf("?m=1"));window.history.replaceState({}, document.title, clean_uri); }; /*]]>*/</script>
      </b:includable>
      
      <!--[ Comment disqus on-scroll ]-->
      <b:includable id='post-commentDisqusScroll'>
        <div class='cmDisqus' id='disqus_thread'>
          <div id='disqus_empty'/>
        </div>
        
        <!--[ Disqus script by bungfrangki.com, change 'nldblog' with your disqus_shortname ]-->
        <script>var disqus_blogger_current_url = &quot;<data:blog.canonicalUrl/>&quot;; if (!disqus_blogger_current_url.length) {disqus_blogger_current_url = &quot;<data:blog.url/>&quot;;} var disqus_blogger_homepage_url = &quot;<data:blog.canonicalHomepageUrl/>&quot;; var disqus_blogger_canonical_homepage_url = &quot;<data:blog.canonicalHomepageUrl/>&quot;;</script>
        <script>/*<![CDATA[*/ function load_disqus( disqus_shortname ) {var y = document.getElementById('disqus_empty'), t = document.getElementById('disqus_thread'), e = document.createElement('script'), d = document.createElement('script'), h = (document.getElementsByTagName('head')[0] || document.getElementsByTagName('body')[0]); if( t && y ) {e.async = true; e.src = '//' + disqus_shortname + '.disqus.com/embed.js'; h.appendChild(e); d.async = !0; d.src = '//' + disqus_shortname + '.disqus.com/blogger_item.js'; h.appendChild(d); y.remove(); } }; window.addEventListener('scroll', function(e) {var currentScroll = document.scrollingElement.scrollTop; var t = document.getElementById('disqus_thread'); if( t && (currentScroll > t.getBoundingClientRect().top - 150) ) {load_disqus('nldblog'); console.log('Disqus loaded.'); }}, false); var uri = window.location.toString(); if (uri.indexOf("?m=1","?m=1") > 0) {var clean_uri = uri.substring(0, uri.indexOf("?m=1"));window.history.replaceState({}, document.title, clean_uri); }; /*]]>*/</script>
      </b:includable>
      
      <!--[ Comment FB ]-->
      <b:includable id='post-commentFB'>        
        <div id='fb-root'/>
        <script src='http://connect.facebook.net/en_US/all.js#xfbml=1' type='deferjs'/>
        <div id='commentFB'><fb:comments expr:href='data:post.url' num_posts='10' width='650'/></div>
        <script>/*<![CDATA[*/ var uri = window.location.toString(); if (uri.indexOf("?m=1","?m=1") > 0) {var clean_uri = uri.substring(0, uri.indexOf("?m=1"));window.history.replaceState({}, document.title, clean_uri); }; /*]]>*/</script>
      </b:includable>
      
      <!--[ Fixed Back to Top ]-->
      <b:includable id='button-backtoTop'>
        <div class='toTopB' id='backTop' onclick='window.scrollTo({top: 0});'>
          <svg viewBox='0 0 34 34'><circle class='b' cx='17' cy='17' r='15.92'/><circle class='c scrollProgress' cx='17' cy='17' r='15.92'/><path class='line d' d='M15.07,21.06,19.16,17l-4.09-4.06'/></svg>
        </div>
      </b:includable>
      
      <!--[ Loadmore pagination ]-->
      <b:includable id='post-pagination1'>
        <script> /*! Simple AJAX infinite scroll by Taufik Nurrohman dte.web.id */ !function(t,e){t.InfiniteScroll=function(n){function r(t,n){return n=n||e,n.querySelectorAll(t)}function o(t){return void 0!==t}function a(t){return&quot;function&quot;==typeof t}function i(t,e){t=t||{};for(var n in e)t[n]=&quot;object&quot;==typeof e[n]?i(t[n],e[n]):e[n];return t}function s(t,e,n){return o(t)?o(e)?void(o(n)?g[t][n]=e:g[t].push(e)):g[t]:g}function d(t,e){o(e)?delete g[t][e]:g[t]=[]}function l(t,e){if(o(g[t]))for(var n in g[t])g[t][n](e)}function c(){return L.innerHTML=p.text.loading,v=!0,M?(y.classList.add(p.state.loading),l(&quot;loading&quot;,[p]),void u(M,function(t,n){y.className=x+&quot; &quot;+p.state.load,h=e.createElement(&quot;div&quot;),h.innerHTML=t;var o=r(&quot;title&quot;,h),a=r(p.target.post,h),i=r(p.target.anchors+&quot; &quot;+p.target.anchor,h),s=r(p.target.post,H);if(o=o&amp;&amp;o[0]?o[0].innerHTML:&quot;&quot;,a.length&amp;&amp;s.length){var d=s[s.length-1];e.title=o,d.insertAdjacentHTML(&quot;afterend&quot;,&quot; &quot;),h=e.createElement(&quot;div&quot;);for(var c=0,u=a.length;u&gt;c;++c)h.appendChild(a[c]);d.insertAdjacentHTML(&quot;afterend&quot;,h.innerHTML),f(),M=i.length?i[0].href:!1,v=!1,q++,l(&quot;load&quot;,[p,t,n])}},function(t,e){y.classList.add(p.state.error),v=!1,f(1),l(&quot;error&quot;,[p,t,e])})):(y.classList.add(p.state.loaded),L.innerHTML=p.text.loaded,l(&quot;loaded&quot;,[p]))}function f(t){if(L.innerHTML=&quot;&quot;,T){h.innerHTML=p.text[t?&quot;error&quot;:&quot;load&quot;];var e=h.firstChild;e.onclick=function(){return 2===p.type&amp;&amp;(T=!1),c(),!1},L.appendChild(e)}}var u=&quot;infinite-scroll-state-&quot;,p={target:{posts:&quot;.posts&quot;,post:&quot;.post&quot;,anchors:&quot;.anchors&quot;,anchor:&quot;.anchor&quot;},text:{load:&quot;%s&quot;,loading:&quot;%s&quot;,loaded:&quot;%s&quot;,error:&quot;%s&quot;},state:{load:u+&quot;load&quot;,loading:u+&quot;loading&quot;,loaded:u+&quot;loaded&quot;,error:u+&quot;error&quot;}},g={load:[],loading:[],loaded:[],error:[]};p=i(p,n||{}),p.on=s,p.off=d;var h=null,u=function(e,n,r){if(t.XMLHttpRequest){var o=new XMLHttpRequest;o.onreadystatechange=function(){if(4===o.readyState){if(200!==o.status)return void(r&amp;&amp;a(r)&amp;&amp;r(o.responseText,o));n&amp;&amp;a(n)&amp;&amp;n(o.responseText,o)}},o.open(&quot;GET&quot;,e),o.send()}},T=1!==p.type,v=!1,H=r(p.target.posts)[0],L=r(p.target.anchors)[0],M=r(p.target.anchor,L),m=e.body,y=e.documentElement,x=y.className||&quot;&quot;,E=H.offsetTop+H.offsetHeight,j=t.innerHeight,A=0,b=null,q=1;if(M.length){M=M[0].href,H.insertAdjacentHTML(&quot;afterbegin&quot;,&quot; &quot;),h=e.createElement(&quot;div&quot;),f();var w=function(){E=H.offsetTop+H.offsetHeight,j=t.innerHeight,A=m.scrollTop||y.scrollTop,v||E&gt;A+j||c()};w(),0!==p.type&amp;&amp;t.addEventListener(&quot;scroll&quot;,function(){T||(b&amp;&amp;t.clearTimeout(b),b=t.setTimeout(w,200))},!1)}return p}}(window,document); if(typeof InfiniteScroll !== &quot;undefined&quot;) { var infinite_scroll = new InfiniteScroll ({ type: 0, target: { posts: &quot;.blogPts&quot;, post: &quot;.ntry&quot;, anchors: &quot;.blogPg&quot;, anchor: &quot;.olLnk&quot;}, text: {load: &quot;<a class='jsLd' expr:aria-label='data:messages.loadMorePosts' expr:data-text='data:messages.loadMorePosts' href='javascript:;'/>&quot;, loading: &quot;<div class='jsLd wait nPst' expr:data-text='data:messages.loading'><svg viewBox='0 0 50 50' x='0px' y='0px'><path d='M25.251,6.461c-10.318,0-18.683,8.365-18.683,18.683h4.068c0-8.071,6.543-14.615,14.615-14.615V6.461z'><animateTransform attributeName='transform' attributeType='xml' dur='0.6s' from='0 25 25' repeatCount='indefinite' to='360 25 25' type='rotate'/></path></svg></div>&quot;, loaded: &quot;<div class='jsLd nPst' expr:data-text='data:messages.noResultsFound'/>&quot;, error: &quot;<a class='jsLd error' expr:aria-label='data:messages.loadMorePosts' expr:data-text='data:messages.moreEllipsis' href='javascript:;'/>&quot;} }); } function puImgPs(){for(var e=qSell(&quot;.ntry img.imgThm&quot;),t=0;t&lt;e.length;t++)if(e[t].getAttribute(&quot;data-src&quot;)){var n=e[t].getAttribute(&quot;data-src&quot;);1!=n.includes(&quot;blogspot&quot;)&amp;&amp;1!=n.includes(&quot;googleusercontent&quot;)||1!=n.includes(&quot;-pd&quot;)&amp;&amp;1!=n.includes(&quot;-p-k-no-nu&quot;)||0!=n.includes(&quot;-rw&quot;)||e[t].setAttribute(&quot;data-src&quot;,n.replace(&quot;-nu&quot;,&quot;-nu-rw-e30&quot;).replace(&quot;-pd&quot;,&quot;-pd-rw-e30&quot;))}} &quot;undefined&quot;!=typeof infinite_scroll&amp;&amp;infinite_scroll.on(&quot;load&quot;,() =&gt; { puImgPs();typeof puViews==&quot;function&quot;&amp;&amp;(puViews());typeof bkMrk==&quot;function&quot;&amp;&amp;(bkMrk());typeof pushAds==&quot;function&quot;&amp;&amp;(pushAds()); });</script>
      </b:includable>
      
      <!--[ Numbered pagination (Feeds must be enabled to work) ]-->
      <b:includable id='post-pagination2'>
        <script async='async' defer='defer'>/*<![CDATA[*/ var perPage = 7, numPages = 3, firstText = "First", lastText = "Last", prevText = "Previous", nextText = "Next", urlactivepage = location.href, home_page = "/"; var noPage,currentPage,currentPageNo,postLabel;function loadScript(e,a,r){var t=document.createElement("script");t.type="text/javascript",t.src=e,t.async=a,r&&(t.onload=r),document.getElementsByTagName("head")[0].appendChild(t)}function looppagecurrentg(e){var a="";pageNumber=parseInt(numPages/2),pageNumber==numPages-pageNumber&&(numPages=2*pageNumber+1),pageStart=currentPageNo-pageNumber,pageStart<1&&(pageStart=1),lastPageNo=parseInt(e/perPage)+1,lastPageNo-1==e/perPage&&--lastPageNo,pageEnd=pageStart+numPages-1,pageEnd>lastPageNo&&(pageEnd=lastPageNo),a+="<span class='pgOf'>Page "+currentPageNo+" of "+lastPageNo+"</span>";e=parseInt(currentPageNo)-1;1<currentPageNo&&(a+="page"==currentPage?'<a class="pgFirst" href="'+home_page+'">'+firstText+"</a>":'<a class="pgFirst" href="/search/label/'+postLabel+"?&max-results="+perPage+'">'+firstText+"</a>"),2<currentPageNo&&(a+=3==currentPageNo?"page"==currentPage?'<a class="pgPrev" href="'+home_page+'">'+prevText+"</a>":'<a class="pgPrev" href="/search/label/'+postLabel+"?&max-results="+perPage+'">'+prevText+"</a>":"page"==currentPage?'<a class="pgPrev" href="#" onclick="redirectpage('+e+');return false">'+prevText+"</a>":'<a class="pgPrev" href="#" onclick="redirectlabel('+e+');return false">'+prevText+"</a>"),1<pageStart&&(a+="page"==currentPage?'<a class="pgNmbr" href="'+home_page+'">1</a>':'<a class="pgNmbr" href="/search/label/'+postLabel+"?&max-results="+perPage+'">1</a>'),2<pageStart&&(a+='<span class="pgHlip">&hellip;</span>');for(var r=pageStart;r<=pageEnd;r++)a+=currentPageNo==r?'<span class="pgCrnt nPst">'+r+"</span>":1==r?"page"==currentPage?'<a class="pgNmbr" href="'+home_page+'">1</a>':'<a class="pgNmbr" href="/search/label/'+postLabel+"?&max-results="+perPage+'">1</a>':"page"==currentPage?'<a class="pgNmbr" href="#" onclick="redirectpage('+r+');return false">'+r+"</a>":'<a class="pgNmbr" href="#" onclick="redirectlabel('+r+');return false">'+r+"</a>";pageEnd<lastPageNo-1&&(a+='<span class="pgHlip">&hellip;</span>'),pageEnd<lastPageNo&&(a+="page"==currentPage?'<a class="pgNmbr" href="#" onclick="redirectpage('+lastPageNo+');return false">'+lastPageNo+"</a>":'<a class="pgNmbr" href="#" onclick="redirectlabel('+lastPageNo+');return false">'+lastPageNo+"</a>");e=parseInt(currentPageNo)+1;currentPageNo<lastPageNo-1&&(a+="page"==currentPage?'<a class="pgNext" href="#" onclick="redirectpage('+e+');return false">'+nextText+"</a>":'<a class="pgNext" href="#" onclick="redirectlabel('+e+');return false">'+nextText+"</a>"),currentPageNo<lastPageNo&&(a+="page"==currentPage?'<a class="pgLast" href="#" onclick="redirectpage('+lastPageNo+');return false">'+lastText+"</a>":'<a class="pgLast" href="#" onclick="redirectlabel('+lastPageNo+');return false">'+lastText+"</a>");for(var t=document.getElementsByName("pageArea"),e=getid("blogPager"),s=0;s<t.length;s++)t[s].innerHTML=a;t&&0<t.length&&(a=""),e&&(e.innerHTML=a)}function totalcountdata(e){e=e.feed;looppagecurrentg(parseInt(e.openSearch$totalResults.$t,10))}function pagecurrentg(){var e=urlactivepage;-1!=e.indexOf("/search/label/")&&(postLabel=-1!=e.indexOf("?updated-max")?e.substring(e.indexOf("/search/label/")+14,e.indexOf("?updated-max")):e.substring(e.indexOf("/search/label/")+14,e.indexOf("?&max"))),-1==e.indexOf("?q=")&&-1==e.indexOf(".html")&&(-1==e.indexOf("/search/label/")?(currentPage="page",currentPageNo=-1!=urlactivepage.indexOf("#PageNo=")?urlactivepage.substring(urlactivepage.indexOf("#PageNo=")+8,urlactivepage.length):1,loadScript(home_page+"feeds/posts/summary?max-results=1&alt=json-in-script&callback=totalcountdata",!0)):(currentPage="label",-1==e.indexOf("&max-results=")&&(perPage=20),currentPageNo=-1!=urlactivepage.indexOf("#PageNo=")?urlactivepage.substring(urlactivepage.indexOf("#PageNo=")+8,urlactivepage.length):1,loadScript(home_page+"feeds/posts/summary/-/"+postLabel+"?alt=json-in-script&callback=totalcountdata&max-results=1",!0)))}function redirectpage(e){jsonstart=(e-1)*perPage,noPage=e,loadScript(home_page+"feeds/posts/summary?start-index="+jsonstart+"&max-results=1&alt=json-in-script&callback=finddatepost",!0)}function redirectlabel(e){jsonstart=(e-1)*perPage,noPage=e,loadScript(home_page+"feeds/posts/summary/-/"+postLabel+"?start-index="+jsonstart+"&max-results=1&alt=json-in-script&callback=finddatepost",!0)}function finddatepost(e){post=e.feed.entry[0];e=post.published.$t.substring(0,19)+post.published.$t.substring(23,29),e=encodeURIComponent(e),e="page"==currentPage?"/search?updated-max="+e+"&max-results="+perPage+"#PageNo="+noPage:"/search/label/"+postLabel+"?updated-max="+e+"&max-results="+perPage+"#PageNo="+noPage;location.href=e}void 0===firstText&&(firstText="First"),void 0===lastText&&(lastText="Last"),pagecurrentg(); /*]]>*/</script>
      </b:includable>
        
      <!--[ Single page condition ]-->
      <b:includable id='post-singlePage'>
        <style>/*<![CDATA[*/ .blogM .mainbar{flex-basis:100%} .blogM .sidebar{display:none; flex:0 0 530px;margin:50px auto 0} .blogM .sidebar::before{display:none} .sideIn{width:100%} /*]]>*/</style>
      </b:includable>
        
      <!--[ SVG Icon ]-->      
      <b:includable id='back-icon'>
        <!--[ Back icon ]-->
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(12.000000, 12.000000) rotate(-270.000000) translate(-12.000000, -12.000000) translate(5.000000, 8.500000)'><path d='M14,0 C14,0 9.856,7 7,7 C4.145,7 0,0 0,0'/></g></svg>
      </b:includable>
      
      <b:includable id='forward-icon'>
        <!--[ Forward icon ]-->
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(12.000000, 12.000000) rotate(-90.000000) translate(-12.000000, -12.000000) translate(5.000000, 8.500000)'><path d='M14,0 C14,0 9.856,7 7,7 C4.145,7 0,0 0,0'/></g></svg>
      </b:includable>
      
      <b:includable id='arrow-right-icon'>
        <!--[ Arrow right icon ]-->
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(12.000000, 12.000000) rotate(-90.000000) translate(-12.000000, -12.000000) translate(5.500000, 4.000000)'><line x1='6.7743' x2='6.7743' y1='15.7501' y2='0.7501'/><path d='M12.7988,9.6998 C12.7988,9.6998 9.5378,15.7498 6.7758,15.7498 C4.0118,15.7498 0.7498,9.6998 0.7498,9.6998'/></g></svg>
      </b:includable>
      
      <b:includable id='arrow-left-icon'>
        <!--[ Arrow left icon ]-->
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(12.000000, 12.000000) rotate(-270.000000) translate(-12.000000, -12.000000) translate(5.500000, 4.000000)'><line x1='6.7743' x2='6.7743' y1='15.7501' y2='0.7501'/><path d='M12.7988,9.6998 C12.7988,9.6998 9.5378,15.7498 6.7758,15.7498 C4.0118,15.7498 0.7498,9.6998 0.7498,9.6998'/></g></svg>
      </b:includable>
      
      <b:includable id='arow-down-icon'>
        <!--[ Arrow down icon ]-->
        <svg class='line d' viewBox='0 0 24 24'><g transform='translate(5.000000, 8.500000)'><path d='M14,0 C14,0 9.856,7 7,7 C4.145,7 0,0 0,0'/></g></svg>
      </b:includable>
      
      <b:includable id='arow-up-icon'>
        <!--[ Arrow up icon ]-->
        <svg class='line u' viewBox='0 0 24 24'><g transform='translate(12.000000, 12.000000) rotate(-180.000000) translate(-12.000000, -12.000000) translate(5.000000, 8.500000)'><path d='M14,0 C14,0 9.856,7 7,7 C4.145,7 0,0 0,0'/></g></svg>
      </b:includable>
      
      <b:includable id='arow-up-circle-icon'>
        <!--[ Arrow up circle icon ]-->
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(2.500000, 3.000000)'><path class='fill' d='M9.5,18 C3.00557739,18 0.456662548,17.5386801 0.0435259337,15.2033146 C-0.36961068,12.8679491 2.27382642,8.47741935 3.08841712,7.02846996 C5.81256986,2.18407813 7.66371927,0 9.5,0 C11.3362807,0 13.1874301,2.18407813 15.9115829,7.02846996 C16.7261736,8.47741935 19.3696107,12.8679491 18.9564741,15.2033146 C18.5443995,17.5386801 15.9944226,18 9.5,18 Z'/></g></svg>
      </b:includable>
      
      <!--[ Eye icon ]-->
      <b:includable id='eye-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(2.000000, 4.000000)'><path class='svgC' d='M13.1643,8.0521 C13.1643,9.7981 11.7483,11.2141 10.0023,11.2141 C8.2563,11.2141 6.8403,9.7981 6.8403,8.0521 C6.8403,6.3051 8.2563,4.8901 10.0023,4.8901 C11.7483,4.8901 13.1643,6.3051 13.1643,8.0521 Z'/><path d='M0.7503,8.0521 C0.7503,11.3321 4.8923,15.3541 10.0023,15.3541 C15.1113,15.3541 19.2543,11.3351 19.2543,8.0521 C19.2543,4.7691 15.1113,0.7501 10.0023,0.7501 C4.8923,0.7501 0.7503,4.7721 0.7503,8.0521 Z'/></g></svg>
      </b:includable>
      
      <!--[ Profile icon ]-->
      <b:includable id='profile-icon'>
        <svg class='line' viewBox='0 0 24 24'><path class='svgC' d='M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z'/><path d='M20.5899 22C20.5899 18.13 16.7399 15 11.9999 15C7.25991 15 3.40991 18.13 3.40991 22'/></svg>
      </b:includable>
      
      <!--[ Profile Circle icon ]-->
      <b:includable id='profile-circle-icon'>
        <svg class='line' viewBox='0 0 24 24'><path class='svgC' d='M12.12 12.78C12.05 12.77 11.96 12.77 11.88 12.78C10.12 12.72 8.71997 11.28 8.71997 9.50998C8.71997 7.69998 10.18 6.22998 12 6.22998C13.81 6.22998 15.28 7.69998 15.28 9.50998C15.27 11.28 13.88 12.72 12.12 12.78Z'/><path d='M18.74 19.3801C16.96 21.0101 14.6 22.0001 12 22.0001C9.40001 22.0001 7.04001 21.0101 5.26001 19.3801C5.36001 18.4401 5.96001 17.5201 7.03001 16.8001C9.77001 14.9801 14.25 14.9801 16.97 16.8001C18.04 17.5201 18.64 18.4401 18.74 19.3801Z'/><path d='M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z'/></svg>
      </b:includable>
      
      <!--[ Profiles icon ]-->
      <b:includable id='profiles-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(2.749500, 2.549500)'><path d='M6.809,18.9067 C3.137,18.9067 9.41469125e-14,18.3517 9.41469125e-14,16.1277 C9.41469125e-14,13.9037 3.117,11.8997 6.809,11.8997 C10.481,11.8997 13.617,13.8847 13.617,16.1077 C13.617,18.3307 10.501,18.9067 6.809,18.9067 Z'/><path d='M6.809,8.728 C9.219,8.728 11.173,6.774 11.173,4.364 C11.173,1.954 9.219,-2.48689958e-14 6.809,-2.48689958e-14 C4.399,-2.48689958e-14 2.44496883,1.954 2.44496883,4.364 C2.436,6.766 4.377,8.72 6.778,8.728 L6.809,8.728 Z'/><path class='svgC' d='M14.0517,7.5293 C15.4547,7.1543 16.4887007,5.8753 16.4887007,4.3533 C16.4897,2.7653 15.3627,1.4393 13.8647,1.1323'/><path class='svgC' d='M14.7113,11.104 C16.6993,11.104 18.3973,12.452 18.3973,13.655 C18.3973,14.364 17.8123,15.092 16.9223,15.301'/></g></svg>
      </b:includable>
      
      <!--[ Search icon ]-->
      <b:includable id='search-icon'>
        <svg class='line' viewBox='0 0 24 24'><g><circle cx='11.36167' cy='11.36167' r='9.36167'/><line class='svgC' x1='22' x2='19.9332' y1='22' y2='19.9332'/></g></svg>
        <!--<svg class='line' viewBox='0 0 24 24'><g transform='translate(2.000000, 2.000000)'><circle cx='9.76659044' cy='9.76659044' r='8.9885584'/><line x1='16.0183067' x2='19.5423342' y1='16.4851259' y2='20.0000001'/></g></svg>-->
      </b:includable>
      
      <!--[ Share icon ]-->
      <b:includable id='share-icon'>
        <svg class='line' viewBox='0 0 24 24'><path d='M16.44 8.8999C20.04 9.2099 21.51 11.0599 21.51 15.1099V15.2399C21.51 19.7099 19.72 21.4999 15.25 21.4999H8.73998C4.26998 21.4999 2.47998 19.7099 2.47998 15.2399V15.1099C2.47998 11.0899 3.92998 9.2399 7.46998 8.9099'/><path class='svgC' d='M12 15.0001V3.62012'/><path class='svgC' d='M15.35 5.85L12 2.5L8.65002 5.85'/></svg>
      </b:includable>
      
      <!--[ Share icon ]-->
      <b:includable id='share-alt-icon'>
        <svg class='line' viewBox='0 0 24 24'><path d='M92.30583,264.72053a3.42745,3.42745,0,0,1-.37,1.57,3.51,3.51,0,1,1,0-3.13995A3.42751,3.42751,0,0,1,92.30583,264.72053Z' transform='translate(-83.28571 -252.73452)'/><circle class='svgC' cx='18.48892' cy='5.49436' r='3.51099'/><circle class='svgC' cx='18.48892' cy='18.50564' r='3.51099'/><line class='cls-3' x1='12.53012' x2='8.65012' y1='8.476' y2='10.416'/><line class='cls-3' x1='12.53012' x2='8.65012' y1='15.496' y2='13.556'/></svg>
      </b:includable>
      
      <!--[ Instagram icon ]-->
      <b:includable id='instagram-icon'>
        <svg viewBox='0 0 32 32'><path d='M22,3H10a7,7,0,0,0-7,7V22a7,7,0,0,0,7,7H22a7,7,0,0,0,7-7V10A7,7,0,0,0,22,3Zm5,19a5,5,0,0,1-5,5H10a5,5,0,0,1-5-5V10a5,5,0,0,1,5-5H22a5,5,0,0,1,5,5Z'/><path class='svgC' d='M16,9.5A6.5,6.5,0,1,0,22.5,16,6.51,6.51,0,0,0,16,9.5Zm0,11A4.5,4.5,0,1,1,20.5,16,4.51,4.51,0,0,1,16,20.5Z'/><circle class='svgC' cx='23' cy='9' r='1'/></svg>
      </b:includable>
      
      <!--[ Facebook icon ]-->
      <b:includable id='facebook-icon'>
        <svg viewBox='0 0 32 32'><path d='M24,3H8A5,5,0,0,0,3,8V24a5,5,0,0,0,5,5H24a5,5,0,0,0,5-5V8A5,5,0,0,0,24,3Zm3,21a3,3,0,0,1-3,3H17V18h4a1,1,0,0,0,0-2H17V14a2,2,0,0,1,2-2h2a1,1,0,0,0,0-2H19a4,4,0,0,0-4,4v2H12a1,1,0,0,0,0,2h3v9H8a3,3,0,0,1-3-3V8A3,3,0,0,1,8,5H24a3,3,0,0,1,3,3Z'/></svg>
      </b:includable>
      
      <!--[ Facebook rounded icon ]-->
      <b:includable id='facebook-r-icon'>
        <svg viewBox='0 0 64 64'><path d='M20.1,36h3.4c0.3,0,0.6,0.3,0.6,0.6V58c0,1.1,0.9,2,2,2h7.8c1.1,0,2-0.9,2-2V36.6c0-0.3,0.3-0.6,0.6-0.6h5.6 c1,0,1.9-0.7,2-1.7l1.3-7.8c0.2-1.2-0.8-2.4-2-2.4h-6.6c-0.5,0-0.9-0.4-0.9-0.9v-5c0-1.3,0.7-2,2-2h5.9c1.1,0,2-0.9,2-2V6.2 c0-1.1-0.9-2-2-2h-7.1c-13,0-12.7,10.5-12.7,12v7.3c0,0.3-0.3,0.6-0.6,0.6h-3.4c-1.1,0-2,0.9-2,2v7.8C18.1,35.1,19,36,20.1,36z'/></svg>
      </b:includable>
      
      <!--[ Twitter icon ]-->
      <b:includable id='twitter-icon'>
        <svg viewBox='0 0 32 32'><path d='M13.35,28A13.66,13.66,0,0,1,2.18,22.16a1,1,0,0,1,.69-1.56l2.84-.39A12,12,0,0,1,5.44,4.35a1,1,0,0,1,1.7.31,9.87,9.87,0,0,0,5.33,5.68,7.39,7.39,0,0,1,7.24-6.15,7.29,7.29,0,0,1,5.88,3H29a1,1,0,0,1,.9.56,1,1,0,0,1-.11,1.06L27,12.27c0,.14,0,.28-.05.41a12.46,12.46,0,0,1,.09,1.43A13.82,13.82,0,0,1,13.35,28ZM4.9,22.34A11.63,11.63,0,0,0,13.35,26,11.82,11.82,0,0,0,25.07,14.11,11.42,11.42,0,0,0,25,12.77a1.11,1.11,0,0,1,0-.26c0-.22.05-.43.06-.65a1,1,0,0,1,.22-.58l1.67-2.11H25.06a1,1,0,0,1-.85-.47,5.3,5.3,0,0,0-4.5-2.51,5.41,5.41,0,0,0-5.36,5.45,1.07,1.07,0,0,1-.4.83,1,1,0,0,1-.87.2A11.83,11.83,0,0,1,6,7,10,10,0,0,0,8.57,20.12a1,1,0,0,1,.37,1.05,1,1,0,0,1-.83.74Z'/></svg>
      </b:includable>
      
      <!--[ Twitter rounded icon ]-->
      <b:includable id='twitter-r-icon'>
        <svg viewBox='0 0 64 64'><path d='M11.4,26.6C11.5,26.6,11.5,26.6,11.4,26.6c-0.9,0-1.8-0.2-2.6-0.4c-1.3-0.4-2.5,0.8-2.1,2 c1.1,4.3,4.5,7.7,8.8,8.6c-1,0.3-2,0.4-3,0.4c-1,0-1.7,1.1-1.2,2c1.9,3.5,5.6,5.9,9.7,6h1c1.1,0,2,0.9,2,2c0,1.1-0.9,2-2,2 c-1.3,0-2.9-0.1-4.5-0.5c-1-0.2-2-0.2-2.9,0.1c-1.7,0.6-3.5,1.1-5.4,1.3C8.5,50.2,8,50.7,8,51.4v0c0,0.5,0.3,1,0.8,1.2 c3.9,1.7,8.3,2.7,12.9,2.7c21.1,0,32.7-17.9,32.7-33.5v0c0-0.9,0.4-1.8,1.1-2.4c1.2-1,2.3-2.1,3.3-3.4c0.4-0.5-0.1-1.2-0.7-1 c-1.2,0.4-2.4,0.7-3.7,0.9c-0.2,0-0.3-0.2-0.1-0.4c1.5-1.1,2.8-2.6,3.6-4.3c0.3-0.6-0.3-1.2-0.9-0.9c-1.1,0.6-2.3,1-3.5,1.4 c-1.2,0.4-2.6,0.1-3.6-0.7c-1.9-1.5-4.4-2.4-7-2.4c-5.3,0-9.8,3.7-11.1,8.8c-0.2,0.9,0.5,1.7,1.4,1.7c1.6-0.1,3.2-0.3,4.4-0.5 c1-0.2,2,0.3,2.4,1.2c0.5,1.2-0.2,2.4-1.3,2.7c-4.6,1.3-9.7,0.4-9.7,0.4l0,0C21.2,21.8,14.3,18,9.3,12.5C8.6,11.7,7.3,12,7,12.9 c-0.4,1.2-0.6,2.5-0.6,3.9C6.4,20.9,8.4,24.5,11.4,26.6z'/></svg>
      </b:includable>
      
      <!--[ Telegram icon ]-->
      <b:includable id='telegram-icon'>
        <svg viewBox='0 0 32 32'><path d='M24,28a1,1,0,0,1-.62-.22l-6.54-5.23a1.83,1.83,0,0,1-.13.16l-4,4a1,1,0,0,1-1.65-.36L8.2,18.72,2.55,15.89a1,1,0,0,1,.09-1.82l26-10a1,1,0,0,1,1,.17,1,1,0,0,1,.33,1l-5,22a1,1,0,0,1-.65.72A1,1,0,0,1,24,28Zm-8.43-9,7.81,6.25L27.61,6.61,5.47,15.12l4,2a1,1,0,0,1,.49.54l2.45,6.54,2.89-2.88-1.9-1.53A1,1,0,0,1,13,19a1,1,0,0,1,.35-.78l7-6a1,1,0,1,1,1.3,1.52Z'/></svg>
      </b:includable>
      
      <!--[ Telegram rounded icon ]-->
      <b:includable id='telegram-r-icon'>
        <svg viewBox='0 0 64 64'><path d='M56.4,8.2l-51.2,20c-1.7,0.6-1.6,3,0.1,3.5l9.7,2.9c2.1,0.6,3.8,2.2,4.4,4.3l3.8,12.1c0.5,1.6,2.5,2.1,3.7,0.9 l5.2-5.3c0.9-0.9,2.2-1,3.2-0.3l11.5,8.4c1.6,1.2,3.9,0.3,4.3-1.7l8.7-41.8C60.4,9.1,58.4,7.4,56.4,8.2z M50,17.4L29.4,35.6 c-1.1,1-1.9,2.4-2,3.9c-0.2,1.5-2.3,1.7-2.8,0.3l-0.9-3c-0.7-2.2,0.2-4.5,2.1-5.7l23.5-14.6C49.9,16.1,50.5,16.9,50,17.4z'/></svg>
      </b:includable>
      
      <!--[ Tiktok icon ]-->
      <b:includable id='tiktok-icon'>
        <svg viewBox='0 0 32 32'><path d='M24,3H8A5,5,0,0,0,3,8V24a5,5,0,0,0,5,5H24a5,5,0,0,0,5-5V8A5,5,0,0,0,24,3Zm3,21a3,3,0,0,1-3,3H8a3,3,0,0,1-3-3V8A3,3,0,0,1,8,5H24a3,3,0,0,1,3,3Z'/><path class='svgC' d='M22,12a3,3,0,0,1-3-3,1,1,0,0,0-2,0V19a3,3,0,1,1-3-3,1,1,0,0,0,0-2,5,5,0,1,0,5,5V13a4.92,4.92,0,0,0,3,1,1,1,0,0,0,0-2Z'/></svg>
      </b:includable>
      
      <!--[ Tumblr icon ]-->
      <b:includable id='tumblr-icon'>
        <svg viewBox='0 0 32 32'><path d='M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z'/><path class='svgC' d='M20,19a1,1,0,0,0-1,1,1,1,0,0,1-1,1H17a1,1,0,0,1-1-1V15h3a1,1,0,0,0,0-2H16V10a1,1,0,0,0-2,0v3H12a1,1,0,0,0,0,2h2v5a3,3,0,0,0,3,3h1a3,3,0,0,0,3-3A1,1,0,0,0,20,19Z'/></svg>
      </b:includable>
      
      <!--[ line icon ]-->
      <b:includable id='line-icon'>
        <svg viewBox='0 0 32 32'><path d='M16,2C8.28,2,2,7.38,2,14c0,5.48,4.34,10.24,10.44,11.6L12,28.87a1,1,0,0,0,.37.91A1,1,0,0,0,13,30a1,1,0,0,0,.35-.06C14,29.68,30,23.58,30,14,30,7.38,23.72,2,16,2ZM14.22,27.4l.33-2.47a1,1,0,0,0-.83-1.12C8.09,22.91,4,18.78,4,14,4,8.49,9.38,4,16,4S28,8.49,28,14C28,20.61,18.14,25.66,14.22,27.4Z'/><path class='svgC' d='M10,15.25H8.75V12a.75.75,0,0,0-1.5,0v4a.76.76,0,0,0,.75.75h2a.75.75,0,0,0,0-1.5Z'/><path class='svgC' d='M24,12.75a.75.75,0,0,0,0-1.5H22a.76.76,0,0,0-.75.75v4a.76.76,0,0,0,.75.75h2a.75.75,0,0,0,0-1.5H22.75v-.5H24a.75.75,0,0,0,0-1.5H22.75v-.5Z'/><path class='svgC' d='M13,11.25a.76.76,0,0,0-.75.75v4a.75.75,0,0,0,1.5,0V12A.76.76,0,0,0,13,11.25Z'/><path class='svgC' d='M19,11.25a.76.76,0,0,0-.75.75v1.75l-1.65-2.2a.75.75,0,0,0-1.35.45v4a.75.75,0,0,0,1.5,0V14.25l1.65,2.2a.75.75,0,0,0,.6.3.67.67,0,0,0,.24,0,.75.75,0,0,0,.51-.71V12A.76.76,0,0,0,19,11.25Z'/></svg>
      </b:includable>
      
      <!--[ Line rounded icon ]-->
      <b:includable id='line-r-icon'>
        <svg viewBox='0 0 24 24'><path d='M19.365 9.863c.349 0 .63.285.63.631 0 .345-.281.63-.63.63H17.61v1.125h1.755c.349 0 .63.283.63.63 0 .344-.281.629-.63.629h-2.386c-.345 0-.627-.285-.627-.629V8.108c0-.345.282-.63.63-.63h2.386c.346 0 .627.285.627.63 0 .349-.281.63-.63.63H17.61v1.125h1.755zm-3.855 3.016c0 .27-.174.51-.432.596-.064.021-.133.031-.199.031-.211 0-.391-.09-.51-.25l-2.443-3.317v2.94c0 .344-.279.629-.631.629-.346 0-.626-.285-.626-.629V8.108c0-.27.173-.51.43-.595.06-.023.136-.033.194-.033.195 0 .375.104.495.254l2.462 3.33V8.108c0-.345.282-.63.63-.63.345 0 .63.285.63.63v4.771zm-5.741 0c0 .344-.282.629-.631.629-.345 0-.627-.285-.627-.629V8.108c0-.345.282-.63.63-.63.346 0 .628.285.628.63v4.771zm-2.466.629H4.917c-.345 0-.63-.285-.63-.629V8.108c0-.345.285-.63.63-.63.348 0 .63.285.63.63v4.141h1.756c.348 0 .629.283.629.63 0 .344-.282.629-.629.629M24 10.314C24 4.943 18.615.572 12 .572S0 4.943 0 10.314c0 4.811 4.27 8.842 10.035 9.608.391.082.923.258 1.058.59.12.301.079.766.038 1.08l-.164 1.02c-.045.301-.24 1.186 1.049.645 1.291-.539 6.916-4.078 9.436-6.975C23.176 14.393 24 12.458 24 10.314'/></svg>
      </b:includable>
      
      <!--[ Youtube icon ]-->
      <b:includable id='youtube-icon'>
        <svg viewBox='0 0 32 32'><path d='M29.73,9.9A5,5,0,0,0,25.1,5.36a115.19,115.19,0,0,0-18.2,0A5,5,0,0,0,2.27,9.9a69,69,0,0,0,0,12.2A5,5,0,0,0,6.9,26.64c3,.24,6.06.36,9.1.36s6.08-.12,9.1-.36a5,5,0,0,0,4.63-4.54A69,69,0,0,0,29.73,9.9Zm-2,12A3,3,0,0,1,25,24.65a113.8,113.8,0,0,1-17.9,0,3,3,0,0,1-2.78-2.72,65.26,65.26,0,0,1,0-11.86A3,3,0,0,1,7.05,7.35C10,7.12,13,7,16,7s6,.12,9,.35a3,3,0,0,1,2.78,2.72A65.26,65.26,0,0,1,27.73,21.93Z'/><path class='svgC' d='M21.45,15.11l-8-4A1,1,0,0,0,12,12v8a1,1,0,0,0,.47.85A1,1,0,0,0,13,21a1,1,0,0,0,.45-.11l8-4a1,1,0,0,0,0-1.78ZM14,18.38V13.62L18.76,16Z'/></svg>        
      </b:includable>
      
      <!--[ LinkedIn icon ]-->
      <b:includable id='linkedIn-icon'>
        <svg viewBox='0 0 32 32'><path d='M24,3H8A5,5,0,0,0,3,8V24a5,5,0,0,0,5,5H24a5,5,0,0,0,5-5V8A5,5,0,0,0,24,3Zm3,21a3,3,0,0,1-3,3H8a3,3,0,0,1-3-3V8A3,3,0,0,1,8,5H24a3,3,0,0,1,3,3Z'/><path class='svgC' d='M11,14a1,1,0,0,0-1,1v6a1,1,0,0,0,2,0V15A1,1,0,0,0,11,14Z'/><path class='svgC' d='M19,13a4,4,0,0,0-4,4v4a1,1,0,0,0,2,0V17a2,2,0,0,1,4,0v4a1,1,0,0,0,2,0V17A4,4,0,0,0,19,13Z'/><circle class='svgC' cx='11' cy='11' r='1'/></svg>        
      </b:includable>
      
      <!--[ LinkedIn rounded icon ]-->
      <b:includable id='linkedIn-r-icon'>
        <svg viewBox='0 0 64 64'><path d='M8,54.7C8,55.4,8.6,56,9.3,56h9.3c0.7,0,1.3-0.6,1.3-1.3V23.9c0-0.7-0.6-1.3-1.3-1.3H9.3 c-0.7,0-1.3,0.6-1.3,1.3V54.7z'/><path d='M46.6,22.3c-4.5,0-7.7,1.8-9.4,3.7c-0.4,0.4-1.1,0.1-1.1-0.5l0-1.6c0-0.7-0.6-1.3-1.3-1.3h-9.4 c-0.7,0-1.3,0.6-1.3,1.3c0.1,5.7,0,25.4,0,30.7c0,0.7,0.6,1.3,1.3,1.3h9.5c0.7,0,1.3-0.6,1.3-1.3V37.9c0-1,0-2,0.3-2.7 c0.8-2,2.6-4.1,5.7-4.1c4.1,0,6,3.1,6,7.6v15.9c0,0.7,0.6,1.3,1.3,1.3h9.3c0.7,0,1.3-0.6,1.3-1.3V37.4C60,27.1,54.1,22.3,46.6,22.3 z'/><path d='M13.9,18.9L13.9,18.9c3.8,0,6.1-2.4,6.1-5.4C19.9,10.3,17.7,8,14,8c-3.7,0-6,2.3-6,5.4 C8,16.5,10.3,18.9,13.9,18.9z'/></svg>
      </b:includable>
      
      <!--[ Whatsapp icon ]-->
      <b:includable id='whatsapp-icon'>
        <svg viewBox='0 0 32 32'><path d='M16,2A13,13,0,0,0,8,25.23V29a1,1,0,0,0,.51.87A1,1,0,0,0,9,30a1,1,0,0,0,.51-.14l3.65-2.19A12.64,12.64,0,0,0,16,28,13,13,0,0,0,16,2Zm0,24a11.13,11.13,0,0,1-2.76-.36,1,1,0,0,0-.76.11L10,27.23v-2.5a1,1,0,0,0-.42-.81A11,11,0,1,1,16,26Z'/><path class='svgC' d='M19.86,15.18a1.9,1.9,0,0,0-2.64,0l-.09.09-1.4-1.4.09-.09a1.86,1.86,0,0,0,0-2.64L14.23,9.55a1.9,1.9,0,0,0-2.64,0l-.8.79a3.56,3.56,0,0,0-.5,3.76,10.64,10.64,0,0,0,2.62,4A8.7,8.7,0,0,0,18.56,21a2.92,2.92,0,0,0,2.1-.79l.79-.8a1.86,1.86,0,0,0,0-2.64Zm-.62,3.61c-.57.58-2.78,0-4.92-2.11a8.88,8.88,0,0,1-2.13-3.21c-.26-.79-.25-1.44,0-1.71l.7-.7,1.4,1.4-.7.7a1,1,0,0,0,0,1.41l2.82,2.82a1,1,0,0,0,1.41,0l.7-.7,1.4,1.4Z'/></svg>
      </b:includable>
      
      <!--[ Whatsapp rounded icon ]-->
      <b:includable id='whatsapp-r-icon'>
        <svg viewBox='0 0 64 64'><path d='M6.9,48.4c-0.4,1.5-0.8,3.3-1.3,5.2c-0.7,2.9,1.9,5.6,4.8,4.8l5.1-1.3c1.7-0.4,3.5-0.2,5.1,0.5 c4.7,2.1,10,3,15.6,2.1c12.3-1.9,22-11.9,23.5-24.2C62,17.3,46.7,2,28.5,4.2C16.2,5.7,6.2,15.5,4.3,27.8c-0.8,5.6,0,10.9,2.1,15.6 C7.1,44.9,7.3,46.7,6.9,48.4z M21.3,19.8c0.6-0.5,1.4-0.9,1.8-0.9s2.3-0.2,2.9,1.2c0.6,1.4,2,4.7,2.1,5.1c0.2,0.3,0.3,0.7,0.1,1.2 c-0.2,0.5-0.3,0.7-0.7,1.1c-0.3,0.4-0.7,0.9-1,1.2c-0.3,0.3-0.7,0.7-0.3,1.4c0.4,0.7,1.8,2.9,3.8,4.7c2.6,2.3,4.9,3,5.5,3.4 c0.7,0.3,1.1,0.3,1.5-0.2c0.4-0.5,1.7-2,2.2-2.7c0.5-0.7,0.9-0.6,1.6-0.3c0.6,0.2,4,1.9,4.7,2.2c0.7,0.3,1.1,0.5,1.3,0.8 c0.2,0.3,0.2,1.7-0.4,3.2c-0.6,1.6-2.1,3.1-3.2,3.5c-1.3,0.5-2.8,0.7-9.3-1.9c-7-2.8-11.8-9.8-12.1-10.3c-0.3-0.5-2.8-3.7-2.8-7.1 C18.9,22.1,20.7,20.4,21.3,19.8z'/></svg>
      </b:includable>
      
      <!--[ Pinterest icon ]-->
      <b:includable id='pinterest-icon'>
        <svg viewBox='0 0 32 32'><path d='M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26a12,12,0,0,1-3.81-.63l1.2-4.81A7.93,7.93,0,0,0,16,23a8.36,8.36,0,0,0,1.4-.12,8,8,0,1,0-9.27-6.49,1,1,0,0,0,2-.35,6,6,0,1,1,3.79,4.56L15,16.24A1,1,0,1,0,13,15.76l-2.7,10.81A12,12,0,1,1,16,28Z'/></svg>
      </b:includable>
      
      <!--[ Pinteres rounded icon ]-->
      <b:includable id='pinterest-r-icon'>
        <svg viewBox='0 0 64 64'><path d='M14.4,53.8c2.4,2,6.1,0.6,6.8-2.4l0-0.1c0.4-1.8,2.4-10.2,3.2-13.7c0.2-0.9,0.2-1.8-0.1-2.7 C24.2,34,24,32.8,24,31.5c0-4.1,2.4-7.2,5.4-7.2c2.5,0,3.8,1.9,3.8,4.2c0,2.6-1.6,6.4-2.5,9.9c-0.7,3,1.5,5.4,4.4,5.4 c5.3,0,8.9-6.8,8.9-14.9c0-6.1-4.1-10.7-11.6-10.7c-8.5,0-13.8,6.3-13.8,13.4c0,2.4,0.7,4.2,1.8,5.5c0.5,0.6,0.6,0.9,0.4,1.6 c-0.1,0.5-0.4,1.8-0.6,2.2c-0.2,0.7-0.8,1-1.4,0.7c-3.9-1.6-5.7-5.9-5.7-10.7c0-8,6.7-17.5,20-17.5c10.7,0,17.7,7.7,17.7,16 c0,11-6.1,19.2-15.1,19.2c-1.9,0-3.8-0.7-5.2-1.6c-0.9-0.6-2.1-0.1-2.4,0.9c-0.5,1.9-1.1,4.3-1.3,4.9c-0.1,0.5-0.3,0.9-0.4,1.4 c-1,2.7,0.9,5.5,3.7,5.7c2.1,0.1,4.2,0,6.3-0.3c12.4-2,22.1-12.2,23.4-24.7C61.5,18.1,48.4,4,32,4C16.5,4,4,16.5,4,32 C4,40.8,8.1,48.6,14.4,53.8z'/></svg>
      </b:includable>
      
      <!--[ Mail rounded icon ]-->
      <b:includable id='mail-r-icon'>
        <svg viewBox='0 0 500 500'><path d='M468.051,222.657c0-12.724-5.27-24.257-13.717-32.527 L282.253,45.304c-17.811-17.807-46.702-17.807-64.505,0L45.666,190.129c-8.448,8.271-13.717,19.803-13.717,32.527v209.054 c0,20.079,16.264,36.341,36.34,36.341h363.421c20.078,0,36.34-16.262,36.34-36.341V222.657z M124.621,186.402h250.758 c11.081,0,19.987,8.905,19.987,19.991v34.523c-0.088,4.359-1.818,8.631-5.181,11.997l-55.966,56.419l83.224,83.127 c6.904,6.904,6.904,18.081,0,24.985s-18.085,6.904-24.985,0l-85.676-85.672H193.034l-85.492,85.672 c-6.907,6.904-18.081,6.904-24.985,0c-6.906-6.904-6.906-18.081,0-24.985l83.131-83.127l-55.875-56.419 c-3.638-3.638-5.363-8.358-5.181-13.177v-33.343C104.632,195.307,113.537,186.402,124.621,186.402z'/></svg>
      </b:includable>
      
      <!--[ Circle icon ]-->
      <b:includable id='circle-icon'>
        <svg viewBox='0 0 32 32'><path d='M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z'/><path class='svgC' d='M16,9.5A6.5,6.5,0,1,0,22.5,16,6.51,6.51,0,0,0,16,9.5Zm0,11A4.5,4.5,0,1,1,20.5,16,4.51,4.51,0,0,1,16,20.5Z'/></svg>
      </b:includable>
      
      <!--[ Home icon ]-->
      <b:includable id='home-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(2.400000, 2.000000)'><line class='svgC' x1='6.6787' x2='12.4937' y1='14.1354' y2='14.1354'/><path d='M1.24344979e-14,11.713 C1.24344979e-14,6.082 0.614,6.475 3.919,3.41 C5.365,2.246 7.615,0 9.558,0 C11.5,0 13.795,2.235 15.254,3.41 C18.559,6.475 19.172,6.082 19.172,11.713 C19.172,20 17.213,20 9.586,20 C1.959,20 1.24344979e-14,20 1.24344979e-14,11.713 Z'/></g></svg>
      </b:includable>
      
      <!--[ Home alt icon ]-->
      <b:includable id='home-alt-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(2.400000, 2.000000)'><path d='M1.24344979e-14,11.713 C1.24344979e-14,6.082 0.614,6.475 3.919,3.41 C5.365,2.246 7.615,0 9.558,0 C11.5,0 13.795,2.235 15.254,3.41 C18.559,6.475 19.172,6.082 19.172,11.713 C19.172,20 17.213,20 9.586,20 C1.959,20 1.24344979e-14,20 1.24344979e-14,11.713 Z'/></g></svg>
      </b:includable>
      
      <!--[ Folder icon ]-->
      <b:includable id='folder-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(2.500000, 2.500000)'><line class='svgC' x1='6.6787' x2='12.4937' y1='12.0742685' y2='12.0742685'/><path d='M-1.13686838e-13,5.29836453 C-1.13686838e-13,2.85645977 1.25,0.75931691 3.622,0.272650243 C5.993,-0.214968804 7.795,-0.0463973758 9.292,0.761221672 C10.79,1.56884072 10.361,2.76122167 11.9,3.63645977 C13.44,4.51265024 15.917,3.19645977 17.535,4.94217405 C19.229,6.7697931 19.2200005,9.57550739 19.2200005,11.3640788 C19.2200005,18.1602693 15.413,18.6993169 9.61,18.6993169 C3.807,18.6993169 -1.13686838e-13,18.2288407 -1.13686838e-13,11.3640788 L-1.13686838e-13,5.29836453 Z'/></g></svg>
      </b:includable>
      
      <!--[ Message icon ]-->
      <b:includable id='message-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(2.452080, 2.851980)'><path class='svgC' d='M15.0928322,6.167017 C15.0928322,6.167017 11.8828071,10.0196486 9.53493746,10.0196486 C7.18807029,10.0196486 3.941955,6.167017 3.941955,6.167017'/><path d='M1.04805054e-13,9.11679198 C1.04805054e-13,2.27869674 2.38095238,8.8817842e-15 9.52380952,8.8817842e-15 C16.6666667,8.8817842e-15 19.047619,2.27869674 19.047619,9.11679198 C19.047619,15.9538847 16.6666667,18.233584 9.52380952,18.233584 C2.38095238,18.233584 1.04805054e-13,15.9538847 1.04805054e-13,9.11679198 Z'/></g></svg>
      </b:includable>
      
      <!--[ Paper icon ]-->
      <b:includable id='paper-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(3.649800, 2.749900)'><line class='svgC' x1='10.6555' x2='5.2555' y1='12.6999' y2='12.6999'/><line class='svgC' x1='8.6106' x2='5.2546' y1='8.6886' y2='8.6886'/><path d='M16.51,5.55 L10.84,0.15 C10.11,0.05 9.29,0 8.39,0 C2.1,0 -1.95399252e-14,2.32 -1.95399252e-14,9.25 C-1.95399252e-14,16.19 2.1,18.5 8.39,18.5 C14.69,18.5 16.79,16.19 16.79,9.25 C16.79,7.83 16.7,6.6 16.51,5.55 Z'/><path d='M10.2844,0.0827 L10.2844,2.7437 C10.2844,4.6017 11.7904,6.1067 13.6484,6.1067 L16.5994,6.1067'/></g></svg>
      </b:includable>
      
      <!--[ Document icon ]-->
      <b:includable id='document-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(3.610000, 2.750100)'><line class='svgC' x1='11.9858' x2='4.7658' y1='12.9463' y2='12.9463'/><line class='svgC' x1='11.9858' x2='4.7658' y1='9.1865' y2='9.1865'/><line class='svgC' x1='7.521' x2='4.766' y1='5.4272' y2='5.4272'/><path d='M7.63833441e-14,9.25 C7.63833441e-14,16.187 2.098,18.5 8.391,18.5 C14.685,18.5 16.782,16.187 16.782,9.25 C16.782,2.313 14.685,0 8.391,0 C2.098,0 7.63833441e-14,2.313 7.63833441e-14,9.25 Z'/></g></svg>
      </b:includable>
      
      <!--[ Buy icon ]-->
      <b:includable id='buy-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(3.650200, 2.850200)'><path d='M2.044,3.58024493 C7.3705141,2.243 13.9926469,2.32498848 15.5231061,4.06777179 C17.0535652,5.8105551 17.0220031,11.638 15.2330031,13.237 C13.4450031,14.836 5.68,14.988 3.22,13.237 C0.621,11.386 2.129,5.692 2.044,2.243 C2.095,0.313 -1.13686838e-13,0 -1.13686838e-13,0'/><line class='svgC' x1='10.5059' x2='13.2789' y1='7.8696' y2='7.8696'/><path class='svgC' d='M3.6138,17.2773 C3.9138,17.2773 4.1578,17.5213 4.1578,17.8213 C4.1578,18.1223 3.9138,18.3663 3.6138,18.3663 C3.3128,18.3663 3.0688,18.1223 3.0688,17.8213 C3.0688,17.5213 3.3128,17.2773 3.6138,17.2773 Z'/><path class='svgC' d='M13.9453,17.2773 C14.2463,17.2773 14.4903,17.5213 14.4903,17.8213 C14.4903,18.1223 14.2463,18.3663 13.9453,18.3663 C13.6453,18.3663 13.4013,18.1223 13.4013,17.8213 C13.4013,17.5213 13.6453,17.2773 13.9453,17.2773 Z'/></g></svg>
      </b:includable>
      
      <!--[ Download icon ]-->
      <b:includable id='download-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(2.000000, 2.000000)'><line class='svgC' x1='9.8791' x2='9.8791' y1='12.791' y2='0.75'/><polyline class='svgC' points='12.7951 9.8642 9.8791 12.7922 6.9631 9.8642'/><path d='M14.3703,5.2587 C17.9493,5.5887 19.2503,6.9287 19.2503,12.2587 C19.2503,19.3587 16.9393,19.3587 10.0003,19.3587 C3.0593,19.3587 0.7503,19.3587 0.7503,12.2587 C0.7503,6.9287 2.0503,5.5887 5.6303,5.2587'/></g></svg>
      </b:includable>
      
      <!--[ Download alt icon ]-->
      <b:includable id='download-alt-icon'><svg class='line' viewBox='0 0 24 24'><polyline class='svgC' points='8 17 12 21 16 17'/><line class='svgC' x1='12' x2='12' y1='12' y2='21'/><path d='M20.88 18.09A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.29'/></svg></b:includable>
      
      <!--[ Lock icon ]-->
      <b:includable id='lock-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(3.500000, 2.000000)'><path d='M12.9709,7.4033 L12.9709,5.2543 C12.9399,2.7353 10.8719,0.7193 8.3539,0.7503 C5.8869,0.7813 3.8919,2.7673 3.8499,5.2343 L3.8499,7.4033'/><line class='svgC' x1='8.4103' x2='8.4103' y1='12.1562' y2='14.3772'/><path d='M8.4103,6.8242 C2.6653,6.8242 0.7503,8.3922 0.7503,13.0952 C0.7503,17.7992 2.6653,19.3672 8.4103,19.3672 C14.1553,19.3672 16.0713,17.7992 16.0713,13.0952 C16.0713,8.3922 14.1553,6.8242 8.4103,6.8242 Z'/></g></svg>
      </b:includable>
      
      <!--[ Shield done icon ]-->
      <b:includable id='shield-done-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(3.500000, 2.000000)'><path d='M8.4845,19.6057 C10.8195,19.6057 16.1565,17.2837 16.1565,10.8787 C16.1565,4.4747 16.4345,3.9737 15.8195,3.3577 C15.2035,2.7417 11.9935,0.7507 8.4845,0.7507 C4.9755,0.7507 1.7655,2.7417 1.1505,3.3577 C0.5345,3.9737 0.8125,4.4747 0.8125,10.8787 C0.8125,17.2837 6.1505,19.6057 8.4845,19.6057 Z'/><polyline class='svgC' points='5.8859 9.8748 7.7779 11.7698 11.6759 7.8698'/></g></svg>
      </b:includable>
      
      <!--[ Plus icon ]-->
      <b:includable id='plus-icon'>
        <svg viewBox='0 0 512 512'><path d='M417.4,224H288V94.6c0-16.9-14.3-30.6-32-30.6c-17.7,0-32,13.7-32,30.6V224H94.6C77.7,224,64,238.3,64,256 c0,17.7,13.7,32,30.6,32H224v129.4c0,16.9,14.3,30.6,32,30.6c17.7,0,32-13.7,32-30.6V288h129.4c16.9,0,30.6-14.3,30.6-32 C448,238.3,434.3,224,417.4,224z'/></svg>
      </b:includable>
      
      <!--[ Moon icon ]-->
      <b:includable id='moon-sun-icon'>
        <svg class='line' viewBox='0 0 24 24'>
          <g class='d1'><path d='M183.72453,170.371a10.4306,10.4306,0,0,1-.8987,3.793,11.19849,11.19849,0,0,1-5.73738,5.72881,10.43255,10.43255,0,0,1-3.77582.89138,1.99388,1.99388,0,0,0-1.52447,3.18176,10.82936,10.82936,0,1,0,15.118-15.11819A1.99364,1.99364,0,0,0,183.72453,170.371Z' transform='translate(-169.3959 -166.45548)'/></g>
          <g class='d2'><path class='f' d='M12 18.5C15.5899 18.5 18.5 15.5899 18.5 12C18.5 8.41015 15.5899 5.5 12 5.5C8.41015 5.5 5.5 8.41015 5.5 12C5.5 15.5899 8.41015 18.5 12 18.5Z'/><path class='svgC' d='M19.14 19.14L19.01 19.01M19.01 4.99L19.14 4.86L19.01 4.99ZM4.86 19.14L4.99 19.01L4.86 19.14ZM12 2.08V2V2.08ZM12 22V21.92V22ZM2.08 12H2H2.08ZM22 12H21.92H22ZM4.99 4.99L4.86 4.86L4.99 4.99Z' stroke-width='2'/></g></svg>
      </b:includable>
      
      <!--[ Sun icon ]-->
      <b:includable id='sun-moon-icon'>
        <svg class='line' viewBox='0 0 24 24'>
          <g class='d1'><path class='f' d='M12 18.5C15.5899 18.5 18.5 15.5899 18.5 12C18.5 8.41015 15.5899 5.5 12 5.5C8.41015 5.5 5.5 8.41015 5.5 12C5.5 15.5899 8.41015 18.5 12 18.5Z'/><path class='svgC' d='M19.14 19.14L19.01 19.01M19.01 4.99L19.14 4.86L19.01 4.99ZM4.86 19.14L4.99 19.01L4.86 19.14ZM12 2.08V2V2.08ZM12 22V21.92V22ZM2.08 12H2H2.08ZM22 12H21.92H22ZM4.99 4.99L4.86 4.86L4.99 4.99Z' stroke-width='2'/></g>
          <g class='d2'><path d='M183.72453,170.371a10.4306,10.4306,0,0,1-.8987,3.793,11.19849,11.19849,0,0,1-5.73738,5.72881,10.43255,10.43255,0,0,1-3.77582.89138,1.99388,1.99388,0,0,0-1.52447,3.18176,10.82936,10.82936,0,1,0,15.118-15.11819A1.99364,1.99364,0,0,0,183.72453,170.371Z' transform='translate(-169.3959 -166.45548)'/></g></svg>
      </b:includable>
      
      <!--[ Cloud Icon ]-->
      <b:includable id='cloud-icon'>
        <svg class='line' viewBox='0 0 24 24'><line x1='10' x2='10' y1='5' y2='4'/><line x1='6.79' x2='6.14' y1='6.17' y2='5.4'/><line x1='5.08' x2='4.09' y1='9.13' y2='8.96'/><line x1='5.67' x2='4.8' y1='12.5' y2='13'/><line x1='13.21' x2='13.86' y1='6.17' y2='5.4'/><path d='M7.46,12.41A3.5,3.5,0,1,1,13,8.19l.08.12'/><path d='M16,15h4a1,1,0,0,0,1-1h0a3,3,0,0,0-3-3H18a3.48,3.48,0,0,0-6.92.19A2.94,2.94,0,0,0,10,11a3,3,0,0,0-3,3v1'/><line x1='13' x2='20' y1='17' y2='17'/><line x1='15' x2='18' y1='19' y2='19'/><line x1='10' x2='13' y1='19' y2='19'/><line x1='9' x2='13' y1='15' y2='15'/><line x1='8' x2='11' y1='17' y2='17'/></svg>
      </b:includable>
      
      <!--[ Chat icon ]-->
      <b:includable id='chat-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(2.000000, 2.000000)'><path d='M17.0710351,17.0698449 C14.0159481,20.1263505 9.48959549,20.7867004 5.78630747,19.074012 C5.23960769,18.8538953 1.70113357,19.8338667 0.933341969,19.0669763 C0.165550368,18.2990808 1.14639409,14.7601278 0.926307229,14.213354 C-0.787154393,10.5105699 -0.125888852,5.98259958 2.93020311,2.9270991 C6.83146881,-0.9756997 13.1697694,-0.9756997 17.0710351,2.9270991 C20.9803405,6.8359285 20.9723008,13.1680512 17.0710351,17.0698449 Z'/></g></svg>
      </b:includable>
      
      <!--[ Messages icon ]-->
      <b:includable id='messages-icon'>
        <svg class='line' viewBox='0 0 24 24'><path d='M22 10V13C22 17 20 19 16 19H15.5C15.19 19 14.89 19.15 14.7 19.4L13.2 21.4C12.54 22.28 11.46 22.28 10.8 21.4L9.3 19.4C9.14 19.18 8.77 19 8.5 19H8C4 19 2 18 2 13V8C2 4 4 2 8 2H14'/><path class='svgC' d='M19.5 7C20.8807 7 22 5.88071 22 4.5C22 3.11929 20.8807 2 19.5 2C18.1193 2 17 3.11929 17 4.5C17 5.88071 18.1193 7 19.5 7Z'/></svg>
      </b:includable>
      
      <!--[ Music icon ]-->
      <b:includable id='music-icon'>
        <svg class='line' viewBox='0 0 24 24'><path d='M9 18V5l12-2v13'/><circle cx='6' cy='18' r='3'/><circle cx='18' cy='16' r='3'/></svg>
      </b:includable>
      
      <!--[ Hamburger menu icon ]-->
      <b:includable id='ham-icon'>
        <!--<svg class='line' viewBox='0 0 24 24'><line x1='3' x2='21' y1='12' y2='12'/><line x1='3' x2='21' y1='5' y2='5'/><line x1='3' x2='21' y1='19' y2='19'/></svg>-->
        <svg class='line' viewBox='0 0 24 24'>
          <g class='h1'><path d='M 3 18 H 14 M 10 6 H 21'/><line class='svgC' x1='3' x2='21' y1='12' y2='12'/></g>
          <g class='h2' transform='translate(12.000000, 12.000000) rotate(-270.000000) translate(-12.000000, -12.000000) translate(5.000000, 8.500000)'><path d='M14,0 C14,0 9.856,7 7,7 C4.145,7 0,0 0,0'/></g>
          <g class='h3' transform='translate(12.000000, 12.000000) rotate(-90.000000) translate(-12.000000, -12.000000) translate(5.000000, 8.500000)'><path d='M14,0 C14,0 9.856,7 7,7 C4.145,7 0,0 0,0'/></g>
        </svg>
        
      </b:includable>
      
      <!--[ Menu icon ]-->
      <b:includable id='menu-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(3.000000, 3.000000)'><path class='fill' d='M18.00036,3.6738 C18.00036,5.7024 16.35516,7.3476 14.32656,7.3476 C12.29796,7.3476 10.65366,5.7024 10.65366,3.6738 C10.65366,1.6452 12.29796,-6.39488462e-15 14.32656,-6.39488462e-15 C16.35516,-6.39488462e-15 18.00036,1.6452 18.00036,3.6738 Z'/><path class='fill svgC' d='M7.3467,3.6738 C7.3467,5.7024 5.7024,7.3476 3.6729,7.3476 C1.6452,7.3476 4.79616347e-15,5.7024 4.79616347e-15,3.6738 C4.79616347e-15,1.6452 1.6452,-6.39488462e-15 3.6729,-6.39488462e-15 C5.7024,-6.39488462e-15 7.3467,1.6452 7.3467,3.6738 Z'/><path class='fill svgC' d='M18.00036,14.26194 C18.00036,16.29054 16.35516,17.93484 14.32656,17.93484 C12.29796,17.93484 10.65366,16.29054 10.65366,14.26194 C10.65366,12.23334 12.29796,10.58814 14.32656,10.58814 C16.35516,10.58814 18.00036,12.23334 18.00036,14.26194 Z'/><path class='fill' d='M7.3467,14.26194 C7.3467,16.29054 5.7024,17.93484 3.6729,17.93484 C1.6452,17.93484 4.79616347e-15,16.29054 4.79616347e-15,14.26194 C4.79616347e-15,12.23334 1.6452,10.58814 3.6729,10.58814 C5.7024,10.58814 7.3467,12.23334 7.3467,14.26194 Z'/></g></svg>
      </b:includable>
      
      <!--[ Calendar icon ]-->
      <b:includable id='calendar-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(2.749800, 2.050100)'><path d='M-1.0658141e-14,10.7255 C-1.0658141e-14,3.7695 2.319,1.4515 9.274,1.4515 C16.23,1.4515 18.549,3.7695 18.549,10.7255 C18.549,17.6815 16.23,19.9995 9.274,19.9995 C2.319,19.9995 -1.0658141e-14,17.6815 -1.0658141e-14,10.7255 Z'/><line x1='0.2754' x2='18.2834' y1='7.2739' y2='7.2739'/><line x1='13.2832' x2='13.2832' y1='2.84217094e-14' y2='3.262'/><line x1='5.2749' x2='5.2749' y1='2.84217094e-14' y2='3.262'/></g></svg>
      </b:includable>
      
      <!--[ Time square icon ]-->
      <b:includable id='time-square-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(2.000000, 2.000000)'><path d='M0.7501,10.0001 C0.7501,16.9371 3.0631,19.2501 10.0001,19.2501 C16.9371,19.2501 19.2501,16.9371 19.2501,10.0001 C19.2501,3.0631 16.9371,0.7501 10.0001,0.7501 C3.0631,0.7501 0.7501,3.0631 0.7501,10.0001 Z'/><polyline class='svgC' points='13.3902 12.0181 9.9992 9.9951 9.9992 5.6341'/></g></svg>
      </b:includable>
      
      <!--[ Bookmark icon ]-->
      <b:includable id='bookmark-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(4.500000, 2.500000)'><path d='M7.47024319,0 C1.08324319,0 0.00424318741,0.932 0.00424318741,8.429 C0.00424318741,16.822 -0.152756813,19 1.44324319,19 C3.03824319,19 5.64324319,15.316 7.47024319,15.316 C9.29724319,15.316 11.9022432,19 13.4972432,19 C15.0932432,19 14.9362432,16.822 14.9362432,8.429 C14.9362432,0.932 13.8572432,0 7.47024319,0 Z'/></g></svg>
      </b:includable>
      
      <!--[ Bookmark Alt icon ]-->
      <b:includable id='bookmark-alt-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(4.500000, 2.500000)'><path d='M7.47024319,0 C1.08324319,0 0.00424318741,0.932 0.00424318741,8.429 C0.00424318741,16.822 -0.152756813,19 1.44324319,19 C3.03824319,19 5.64324319,15.316 7.47024319,15.316 C9.29724319,15.316 11.9022432,19 13.4972432,19 C15.0932432,19 14.9362432,16.822 14.9362432,8.429 C14.9362432,0.932 13.8572432,0 7.47024319,0 Z'/><line class='svgC v' transform='translate(-4.500000, -2.500000)' x1='12' x2='12' y1='6' y2='12'/><line class='svgC h' transform='translate(-4.500000, -2.500000)' x1='15' x2='9' y1='9' y2='9'/></g></svg>
      </b:includable>
      
      <!--[ Smile icon ]-->
      <b:includable id='smile-icon'>
        <svg viewBox='0 0 16 16'><path d='M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z' fill-rule='evenodd'/><path class='svgC' d='M4.285 9.567a.5.5 0 0 1 .683.183A3.498 3.498 0 0 0 8 11.5a3.498 3.498 0 0 0 3.032-1.75.5.5 0 1 1 .866.5A4.498 4.498 0 0 1 8 12.5a4.498 4.498 0 0 1-3.898-2.25.5.5 0 0 1 .183-.683z'/><path class='svgC' d='M7 6.5C7 7.328 6.552 8 6 8s-1-.672-1-1.5S5.448 5 6 5s1 .672 1 1.5zm4 0c0 .828-.448 1.5-1 1.5s-1-.672-1-1.5S9.448 5 10 5s1 .672 1 1.5z'/></svg>
      </b:includable>
      
      <!--[ Link icon ]-->
      <b:includable id='link-icon'>
        <svg class='line' viewBox='0 0 24 24'><path class='svgC' d='M13 11L21.2 2.80005'/><path class='svgC' d='M22 6.8V2H17.2'/><path d='M11 2H9C4 2 2 4 2 9V15C2 20 4 22 9 22H15C20 22 22 20 22 15V13'/></svg>
      </b:includable>
      
      <!--[ Link icon alt ]-->
      <b:includable id='link-a-icon'>
        <svg class='line' viewBox='0 0 24 24'><path d='M15 7h3a5 5 0 0 1 5 5 5 5 0 0 1-5 5h-3m-6 0H6a5 5 0 0 1-5-5 5 5 0 0 1 5-5h3'/><line class='svgC' x1='8' x2='16' y1='12' y2='12'/></svg>
      </b:includable>
      
      <!--[ Tag icon ]-->
      <b:includable id='tag-icon'>
        <svg class='line' viewBox='0 0 24 24'><path d='M4.16989 15.3L8.69989 19.83C10.5599 21.69 13.5799 21.69 15.4499 19.83L19.8399 15.44C21.6999 13.58 21.6999 10.56 19.8399 8.69005L15.2999 4.17005C14.3499 3.22005 13.0399 2.71005 11.6999 2.78005L6.69989 3.02005C4.69989 3.11005 3.10989 4.70005 3.00989 6.69005L2.76989 11.69C2.70989 13.04 3.21989 14.35 4.16989 15.3Z'/><path class='svgC' d='M9.5 12C10.8807 12 12 10.8807 12 9.5C12 8.11929 10.8807 7 9.5 7C8.11929 7 7 8.11929 7 9.5C7 10.8807 8.11929 12 9.5 12Z'/></svg>
      </b:includable>
      
      <!--[ Link icon alt ]-->
      <b:includable id='link-alt-icon'>
        <svg class='line' viewBox='0 0 24 24'><path d='M13.0601 10.9399C15.3101 13.1899 15.3101 16.8299 13.0601 19.0699C10.8101 21.3099 7.17009 21.3199 4.93009 19.0699C2.69009 16.8199 2.68009 13.1799 4.93009 10.9399'/><path class='svgC' d='M10.59 13.4099C8.24996 11.0699 8.24996 7.26988 10.59 4.91988C12.93 2.56988 16.73 2.57988 19.08 4.91988C21.43 7.25988 21.42 11.0599 19.08 13.4099'/></svg>
      </b:includable>
      
      <!--[ Location icon ]-->
      <b:includable id='location-icon'>
        <svg class='line' viewBox='0 0 24 24'><path class='svgC' d='M12 13.4299C13.7231 13.4299 15.12 12.0331 15.12 10.3099C15.12 8.58681 13.7231 7.18994 12 7.18994C10.2769 7.18994 8.88 8.58681 8.88 10.3099C8.88 12.0331 10.2769 13.4299 12 13.4299Z'/><path d='M3.62001 8.49C5.59001 -0.169998 18.42 -0.159997 20.38 8.5C21.53 13.58 18.37 17.88 15.6 20.54C13.59 22.48 10.41 22.48 8.39001 20.54C5.63001 17.88 2.47001 13.57 3.62001 8.49Z'/></svg>
      </b:includable>
      
      <!--[ Settings icon ]-->
      <b:includable id='settings-icon'><svg class='line svg-1' viewBox='0 0 24 24'><g transform='translate(3.500000, 2.500000)'><path class='svgC' d='M8.5,7 C9.88088012,7 11,8.11911988 11,9.5 C11,10.8808801 9.88088012,12 8.5,12 C7.11911988,12 6,10.8808801 6,9.5 C6,8.11911988 7.11911988,7 8.5,7 Z'/><path d='M16.6680023,4.75024695 L16.6680023,4.75024695 C15.9844554,3.55799324 14.4712377,3.15003899 13.2885153,3.83852352 C12.2597626,4.43613205 10.9740669,3.68838056 10.9740669,2.49217572 C10.9740669,1.11619444 9.86587758,0 8.4997646,0 L8.4997646,0 C7.13365161,0 6.02546233,1.11619444 6.02546233,2.49217572 C6.02546233,3.68838056 4.73976662,4.43613205 3.71199461,3.83852352 C2.52829154,3.15003899 1.01507378,3.55799324 0.331526939,4.75024695 C-0.351039204,5.94250065 0.053989269,7.46664934 1.23769234,8.15414609 C2.26546435,8.7527424 2.26546435,10.2472576 1.23769234,10.8458539 C0.053989269,11.5343384 -0.351039204,13.0584871 0.331526939,14.2497531 C1.01507378,15.4420068 2.52829154,15.849961 3.71101391,15.1624643 L3.71199461,15.1624643 C4.73976662,14.5638679 6.02546233,15.3116194 6.02546233,16.5078243 L6.02546233,16.5078243 C6.02546233,17.8838056 7.13365161,19 8.4997646,19 L8.4997646,19 C9.86587758,19 10.9740669,17.8838056 10.9740669,16.5078243 L10.9740669,16.5078243 C10.9740669,15.3116194 12.2597626,14.5638679 13.2885153,15.1624643 C14.4712377,15.849961 15.9844554,15.4420068 16.6680023,14.2497531 C17.3515491,13.0584871 16.9455399,11.5343384 15.7628176,10.8458539 L15.7618369,10.8458539 C14.7340648,10.2472576 14.7340648,8.7527424 15.7628176,8.15414609 C16.9455399,7.46664934 17.3515491,5.94250065 16.6680023,4.75024695 Z'/></g></svg></b:includable>

      <!--[ Comments Icon ]-->
      <b:includable id='comments-icon'><svg class='line' viewBox='0 0 24 24'><g transform='translate(2.000000, 2.000000)'><line class='svgC' x1='13.9394' x2='13.9484' y1='10.413' y2='10.413'/><line class='svgC' x1='9.9304' x2='9.9394' y1='10.413' y2='10.413'/><line class='svgC' x1='5.9214' x2='5.9304' y1='10.413' y2='10.413'/><path d='M17.0710351,17.0698449 C14.0159481,20.1263505 9.48959549,20.7867004 5.78630747,19.074012 C5.23960769,18.8538953 1.70113357,19.8338667 0.933341969,19.0669763 C0.165550368,18.2990808 1.14639409,14.7601278 0.926307229,14.213354 C-0.787154393,10.5105699 -0.125888852,5.98259958 2.93020311,2.9270991 C6.83146881,-0.9756997 13.1697694,-0.9756997 17.0710351,2.9270991 C20.9803405,6.8359285 20.9723008,13.1680512 17.0710351,17.0698449 Z'/></g></svg></b:includable>
      
      <!--[ Close icon ]-->
      <b:includable id='close-icon'><svg class='svg-2 line' viewbox='0 0 24 24'><line x1='18' x2='6' y1='6' y2='18'/><line x1='6' x2='18' y1='6' y2='18'/></svg></b:includable>

      <!--[ Stats icon ]-->
      <b:includable id='stats-icon'><svg class='line' viewBox='0 0 24 24'><g transform='translate(2.300000, 2.300000)'><line class='svgC' x1='5.18305279' x2='5.18305279' y1='7.960947' y2='14.6546312'/><line class='svgC' x1='9.73684179' x2='9.73684179' y1='4.757368' y2='14.6552627'/><line class='svgC' x1='14.2156838' x2='14.2156838' y1='11.498211' y2='14.6550531'/><path d='M-3.55271368e-14,9.73684211 C-3.55271368e-14,2.43473684 2.43473684,2.13162821e-14 9.73684211,2.13162821e-14 C17.0389474,2.13162821e-14 19.4736842,2.43473684 19.4736842,9.73684211 C19.4736842,17.0389474 17.0389474,19.4736842 9.73684211,19.4736842 C2.43473684,19.4736842 -3.55271368e-14,17.0389474 -3.55271368e-14,9.73684211 Z'/></g></svg></b:includable>

      <!--[ List icon ]-->
      <b:includable id='list-icon'><svg class='line' viewBox='0 0 24 24'><line x1='8' x2='21' y1='6' y2='6'/><line x1='8' x2='21' y1='12' y2='12'/><line x1='8' x2='21' y1='18' y2='18'/><line class='svgC' x1='3' x2='3.01' y1='6' y2='6'/><line class='svgC' x1='3' x2='3.01' y1='12' y2='12'/><line class='svgC' x1='3' x2='3.01' y1='18' y2='18'/></svg></b:includable>

      <!--[ Edit icon ]-->
      <b:includable id='edit-icon'><svg class='line' viewBox='0 0 24 24'><path d='M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7'/><path class='svgC' d='M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z'/></svg></b:includable>
      
      <!--[ Edit Alt icon ]-->
      <b:includable id='edit-alt-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(3.500000, 3.500000)'><line class='svgC' x1='9.8352' x2='16.2122' y1='16.0078' y2='16.0078'/><path d='M12.5578,1.3589 L12.5578,1.3589 C11.2138,0.3509 9.3078,0.6229 8.2998,1.9659 C8.2998,1.9659 3.2868,8.6439 1.5478,10.9609 C-0.1912,13.2789 1.4538,16.1509 1.4538,16.1509 C1.4538,16.1509 4.6978,16.8969 6.4118,14.6119 C8.1268,12.3279 13.1638,5.6169 13.1638,5.6169 C14.1718,4.2739 13.9008,2.3669 12.5578,1.3589 Z'/><line x1='7.0041' x2='11.8681' y1='3.7114' y2='7.3624'/></g></svg>
      </b:includable>
      
      <!--[ Translate Icon ]-->
      <b:includable id='translate-icon'>
        <svg class='line' viewBox='0 0 24 24'><path d='M.5,2V18A1.5,1.5,0,0,0,2,19.5H17L10.5.5H2A1.5,1.5,0,0,0,.5,2Z'/><path d='M12,4.5H22A1.5,1.5,0,0,1,23.5,6V22A1.5,1.5,0,0,1,22,23.5H13.5l-1.5-4'/><line x1='17' x2='13.5' y1='19.5' y2='23.5'/><line class='svgC' x1='14.5' x2='21.5' y1='10.5' y2='10.5'/><line class='svgC' x1='17.5' x2='17.5' y1='9.5' y2='10.5'/><path class='svgC' d='M20,10.5c0,1.1-1.77,4.42-4,6'/><path class='svgC' d='M16,13c.54,1.33,4,4.5,4,4.5'/><path class='svgC' d='M10.1,7.46a4,4,0,1,0,1.4,3h-4'/></svg>
      </b:includable>
      
      <!--[ Delete Icon ]-->
      <b:includable id='delete-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(3.500000, 2.000000)'><path d='M15.3891429,7.55409524 C15.3891429,15.5731429 16.5434286,19.1979048 8.77961905,19.1979048 C1.01485714,19.1979048 2.19295238,15.5731429 2.19295238,7.55409524'/><line x1='16.8651429' x2='0.714666667' y1='4.47980952' y2='4.47980952'/><path d='M12.2148571,4.47980952 C12.2148571,4.47980952 12.7434286,0.714095238 8.78914286,0.714095238 C4.83580952,0.714095238 5.36438095,4.47980952 5.36438095,4.47980952'/></g></svg>
      </b:includable>
      
      <!--[ Verified icon ]-->
      <b:includable id='verified-icon'>
        <!--<svg viewBox='0 0 24 24'><path d='M22.5 12.5c0-1.58-.875-2.95-2.148-3.6.154-.435.238-.905.238-1.4 0-2.21-1.71-3.998-3.818-3.998-.47 0-.92.084-1.336.25C14.818 2.415 13.51 1.5 12 1.5s-2.816.917-3.437 2.25c-.415-.165-.866-.25-1.336-.25-2.11 0-3.818 1.79-3.818 4 0 .494.083.964.237 1.4-1.272.65-2.147 2.018-2.147 3.6 0 1.495.782 2.798 1.942 3.486-.02.17-.032.34-.032.514 0 2.21 1.708 4 3.818 4 .47 0 .92-.086 1.335-.25.62 1.334 1.926 2.25 3.437 2.25 1.512 0 2.818-.916 3.437-2.25.415.163.865.248 1.336.248 2.11 0 3.818-1.79 3.818-4 0-.174-.012-.344-.033-.513 1.158-.687 1.943-1.99 1.943-3.484zm-6.616-3.334l-4.334 6.5c-.145.217-.382.334-.625.334-.143 0-.288-.04-.416-.126l-.115-.094-2.415-2.415c-.293-.293-.293-.768 0-1.06s.768-.294 1.06 0l1.77 1.767 3.825-5.74c.23-.345.696-.436 1.04-.207.346.23.44.696.21 1.04z'/></svg>-->
        <svg viewBox='0 0 24 24'><path d='M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z'/></svg>
      </b:includable>
      
      <!--[ Heart icon ]-->
      <b:includable id='heart-icon'>
        <svg class='line' viewBox='0 0 24 24'><g transform='translate(2.550170, 3.550158)'><path d='M0.371729633,8.89614246 C-0.701270367,5.54614246 0.553729633,1.38114246 4.07072963,0.249142462 C5.92072963,-0.347857538 8.20372963,0.150142462 9.50072963,1.93914246 C10.7237296,0.0841424625 13.0727296,-0.343857538 14.9207296,0.249142462 C18.4367296,1.38114246 19.6987296,5.54614246 18.6267296,8.89614246 C16.9567296,14.2061425 11.1297296,16.9721425 9.50072963,16.9721425 C7.87272963,16.9721425 2.09772963,14.2681425 0.371729633,8.89614246 Z'/><path class='svgC d' d='M13.23843,4.013842 C14.44543,4.137842 15.20043,5.094842 15.15543,6.435842'/></g></svg>
      </b:includable>
      
      <!--[ Warning icon ]-->
      <b:includable id='warning-icon'><svg class='line' viewBox='0 0 24 24'><g transform='translate(2.500000, 3.000000)'><path d='M9.5,18 C3.00557739,18 0.456662548,17.5386801 0.0435259337,15.2033146 C-0.36961068,12.8679491 2.27382642,8.47741935 3.08841712,7.02846996 C5.81256986,2.18407813 7.66371927,0 9.5,0 C11.3362807,0 13.1874301,2.18407813 15.9115829,7.02846996 C16.7261736,8.47741935 19.3696107,12.8679491 18.9564741,15.2033146 C18.5443995,17.5386801 15.9944226,18 9.5,18 Z'/><line class='svgC' x1='9.5' x2='9.5' y1='5.5' y2='9.395'/><line class='svgC' x1='9.4957' x2='9.5047' y1='12.895' y2='12.895'/></g></svg></b:includable>
      
      <!--[ Alert Circle icon ]-->
      <b:includable id='alert-circle-icon'><svg class='line' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'><circle cx='12' cy='12' r='10'/><line class='svgC' x1='12' x2='12' y1='8' y2='12'/><line class='svgC' x1='12' x2='12.01' y1='16' y2='16'/></svg></b:includable>
      
      <!--[ Prohibit icon ]-->
      <b:includable id='prohibit-icon'><svg class='line' viewBox='0 0 24 24'><circle cx='12' cy='12' r='10'/><line x1='4.93' x2='19.07' y1='4.93' y2='19.07'/></svg></b:includable>

      <!--[ WiFiOn icon ]-->
      <b:includable id='wifion-icon'><svg class='line' viewBox='0 0 24 24'><path d='M5 12.55a11 11 0 0 1 14.08 0'/><path d='M1.42 9a16 16 0 0 1 21.16 0'/><path d='M8.53 16.11a6 6 0 0 1 6.95 0'/><line class='svgC' x1='12' x2='12.01' y1='20' y2='20'/></svg></b:includable>
      
      <!--[ WifiOff icon ]-->
      <b:includable id='wifioff-icon'><svg class='line' viewBox='0 0 24 24'><path d='M16.72 11.06A10.94 10.94 0 0 1 19 12.55'/><path d='M5 12.55a10.94 10.94 0 0 1 5.17-2.39'/><path d='M10.71 5.05A16 16 0 0 1 22.58 9'/><path d='M1.42 9a15.91 15.91 0 0 1 4.7-2.88'/><path d='M8.53 16.11a6 6 0 0 1 6.95 0'/><line class='svgC' x1='12' x2='12.01' y1='20' y2='20'/><line class='svgC' x1='1' x2='23' y1='1' y2='23'/></svg></b:includable>
      
    </b:defaultmarkup>
  </b:defaultmarkups>
  
  <script>/*<![CDATA[*/ eval(function(p,l,u,s,f,d){while(u--){if(s[u]){p=p.replace(new RegExp('\\b'+u+'\\b','g'),s[u])}}return p}('!16(){16 13(13,14){21 24(14- -355,13)}29 14=58();16 17(13,14){21 24(13- -183,14)}45(;;)61{176(178==-35(17(-125,-92))*(35(13(23,39))/2)+35(17(-71,-77))/3*(-35(17(-106,-125))/4)+35(13(51,51))/5*(-35(13(19,32))/6)+35(13(-1,26))/7+-35(13(71,83))/8+-35(13(69,55))/9*(-35(13(43,57))/10)+35(17(-83,-64))/11)181;14.117(14.135())}53(13){14.117(14.135())}}();65 132=70(185,186),129=70(187,194),126="195",123="196+/=";29 60={38:132+129+126+123,149:16(13){16 14(13,14){21 24(13-152,14)}29 17,18,27,28,30,33,40="",37=0;45(13=60[14(155,162)](13);37<13[14(199,206)];)27=(33=13[67(120,243)](37++))>>2,28=(3&33)<<4|(17=13[14(244,250)](37++))>>4,30=(15&17)<<2|(18=13[67(120,254)](37++))>>6,33=63&18,122(17)?30=33=64:122(18)&&(33=64),40=40+42.38.47(27)+42.38.47(28)+42.38[14(255,257)](30)+42.38.47(33);16 67(13,14){21 24(13-188,14)}21 40},261:16(13){29 14,17,18,27,28,30="",33=0;45(13=13[40(271,256)](/[^210-213-214-9\\+\\/\\=]/78,"");33<13.59;)14=42.38.75(13.47(33++))<<2|(18=42.38.75(13.47(33++)))>>4,17=(15&18)<<4|(27=42.38[37(217,219)](13[40(221,228)](33++)))>>2,18=(3&27)<<6|(28=42.38[40(231,236)](13[37(205,242)](33++))),30+=34.74(14),64!=27&&(30+=34[37(235,234)](17)),64!=28&&(30+=34[40(233,232)](18));16 40(13,14){21 24(13-229,14)}16 37(13,14){21 24(13-227,14)}21 60.89(30)},108:16(13){16 14(13,14){21 24(14- -258,13)}13=13.102(/\\18\\14/78,"\\14");45(29 17="",18=0;18<13[14(180,174)];18++){29 27=13[14(133,150)](18);27<128?17+=34[14(165,173)](27):(127<27&&27<226?17+=34[14(146,173)](27>>6|192):(17+=34[24(225,223)](27>>12|224),17+=34[14(169,173)](27>>6&63|128)),17+=34[14(198,173)](63&27|128))}21 17},89:16(13){45(29 14="",17=0,18=208=52=0;17<13.59;)(18=13[28(95,220)](17))<128?(14+=34.74(18),17++):191<18&&18<224?(52=13[27(-8,22)](17+1),14+=34[27(15,46)]((31&18)<<6|63&52),17+=2):(52=13.100(17+1),84=13[28(95,218)](17+2),14+=34[28(216,215)]((15&18)<<12|(63&52)<<6|63&84),17+=3);16 27(13,14){21 24(13- -212,14)}16 28(13,14){21 24(13-292,14)}21 14},211:16(13){16 14(13,14){21 24(13-273,14)}21(13=41[14(209,237)][14(222,81)](50 238(14(81,272)+13[14(124,82)](/([.$?*|{}()[\\]\\\\/+^])/78,"$1")+"=([^;]*)")))?269(13[1]):268 0},107:16(13,14,17={}){16 18(13,14){21 24(14-134,13)}(17={267:"/",...17}).62 266 265&&(17[24(392,-151)]=17.62[18(264,263)]());262 27=97(101(13))+"="+97(101(14));45(29 28 241 17){27+="; "+28;29 30=17[28];!0!==30&&(27+="="+30)}41[18(109,253)]=27},252:16(13){29 14={};14[24(373,251)]=-1,60.107(13,"",14)},249:16(13,14){76[24(391,248)](13,14)},246:16(13){21 76.105(13)},245:16(13){76[24(114,240)](13)},207:16(13,14){73[24(391,134)](13,14)},167:16(13){21 73[24(166,-393)](13)},164:16(13){73[24(114,-113)](13)},160:16(13){21 13[36[24(385,358)](36.159()*13.59)]},157:16(13){29 14=36[17(113,153)](44(13));16 17(13,14){21 24(14-103,13)}16 18(13,14){21 24(13-230,14)}21 85<=36[18(48,110)](44(13))?14*(36[17(139,87)](44(13))/85)[18(145,144)](2)+"143":96<=36[18(48,142)](44(13))?14*(36[18(48,154)](44(13))/96).79(2)+"189":88<=36[18(48,204)](44(13))?14*(36[17(203,87)](44(13))/88).79(2)+"197":36[18(48,94)](44(13))},190:16(17){29 18=50 93;61{18=50 93}53(13){61{18=50 111("175.99")}53(13){61{18=50 111(14(110,179))}53(13){21 177.172(14(138,94)),!1}}}16 13(13,14){21 24(14-184,13)}16 14(13,14){21 24(13-239,14)}18[14(274,202)]=16(){16 13(13,14){21 24(13-171,14)}29 14;4==18.170&&(200==18[24(140,147)]?(14=18[13(148,156)],17[13(163,115)](14)):13(82,115)==275 17.98&&17[13(277,402)](18))},18[14(367,368)](13(369,378),17[13(376,394)],17[14(382,381)]),18[14(364,379)]()}};16 70(13,14){21 24(14-380,13)}16 56(13){21 41.351(13)}16 352(13){21 41[24(395,356)](13)}16 118(13){21 41[24(388,-109)](13)}16 363(13){21 41[24(365,366)](13)}16 58(){29 13=["349","377","375","372","370","348","362","361","98","360","116","357","353","350","100","105","396","408","407","(?:^|; )","406","405","75","404","401","400","137","398","397","390","389","386","121","383","399","387","90","74","59","403","359","47","16","108","346","310","130","343","307-306","305","304","303","302","301","102","300","299","79","298.99","297","296","295 293 276!","289","288","287","286","285","62","284"];21(58=16(){21 13})()}16 283(13,14){16 17(13,14){21 24(14- -282,13)}21 13[17(-354,-347)][17(-312,-317)](14)}16 281(13,14){21 13[24(384,-280)][24(279,278)](14)}16 345(13){16 14(13,14){21 24(14-308,13)}294[14(309,329)]({342:56(13)[24(341,340)]-20,339:0,90:14(338,337)})}16 336(13){29 14={};16 17(13,14){21 24(14-335,13)}16 18(13,14){21 24(13-291,14)}14[18(334,333)]=17(332,331),14[18(330,328)]=17(311,327),56(13).137(14)}16 326(13,14){21 24(14- -325,13)}16 323(13){29 14={};16 17(13,14){21 24(13- -150,14)}16 18(13,14){21 24(14- -344,13)}14[18(119,86)]=18(68,49),14.130=17(247,259),56(13)[17(270,290)](14)}16 24(13,14){29 17=58();21(24=16(13,14){21 17[13-=371]})(13,14)}16 322(13,14,17,18,27){65 28=41.121(24(374,131));16 30(13,14){21 24(14- -260,13)}28.136=14,28[30(193,161)]=17,28[30(168,141)]=13,27&&(28.116=27),118(18)[30(182,158)](28)}16 321(13,14,17){16 18(13,14){21 24(14- -324,13)}16 27(13,14){21 24(13-320,14)}65 28=41[27(124,319)](18(25,54));28.136=14,28[18(66,72)]=18(103,91),28.318="316/315",28.314=13,17&&(28[18(104,80)]=17),41[18(112,83)][27(313,201)](28)}', 10, 409, '|||||||||||||t|n||function|e|r|||return|||_0x178b|||o|u|var|c|||i|String|parseInt|Math|s|kS||a|document|this||Number|for||charAt|641||new||c2|catch|||getid||_0x55e1|length|Pu|try|expires|||const||f|||_0x51f4a5|||sessionStorage|fromCharCode|indexOf|localStorage||g|toFixed||1202|1155||c3|1e9||514|1e3|u8_de|behavior|||XMLHttpRequest|624|700|1e6|unescape|error|XMLHTTP|charCodeAt|encodeURIComponent|replace|||getItem||sC|u8_en|544|622|ActiveXObject||500|429|1178|onload|push|qSel||596|createElement|isNaN|vjggsbmc|1168||pkhgedjk|||hgjggyfh|block||ggfuguhg|||shift|id|scrollIntoView|625|490|428||666|B|600|612||872|1091|en|||992|508|646|1429|1097|abv||random|rdm||1428|1146|rSS||409|gSS|||readyState|719|warn|||Msxml2|if|console|873854|597||break||505|836|870|889|869||M|gAj||||863|osqeigwlhvyz|ukx3508749216|K||1424||1134|676|483|634|1043|1394|sSS|c1|1179|A|gC|416|Za|z0|737|723|1024|717|1013|688|1144|1208|839||431|2048|608|1156|709||1125|1172|1140|1032|1039|1148|1157|RegExp||513|in|1053|627|1400|rLS|gLS||879|sLS|1393|904|dC|524|609|1427|1119|1408||||de|let|534|509|Date|instanceof|path|void|decodeURIComponent||1088|1192|789|664|typeof|wrong|1121|599|423|549|remCt|731|addCt|smooth|setItem|cookie|center|querySelector|49758EOuVVI||||went|window|Something|floor|classList|Microsoft|3512376kRaahX|3260yCibet|link|GET|url|open|script|age|max|845|1264|offsetTop|1176||1160|href|css|text||type|1166|742|ldCss|ldJs|stE||774|_0x5949b4|1199|690|1278|662|1203|1222|753|721|810|stC|1238|1261|left|438|439|top|responseText||stS|3923688UGcGhI||524usdNgG|160GWrUHi|head|getElementById|getclass|610PAtQkJ|||525|sign||18027onqsIt|send|src|toUTCString|qSell|642|417|1107|614|595|1239|LHVYZUKXmbdrtnfjcap||end|||rel|1193|getElementsByClassName|1213|659|465|658|660|success|||onreadystatechange|removeItem||MBDRTNFJCAPOSQEIGW|remove||||1212||9uXEtTy|31401524ZYnZiy|async|status|match|appendChild|1115|scroll|querySelectorAll|stylesheet|add|675880bQrepP|abs'.split('|'))); function remPar(n,r){function t(n,r){return _0x50d0(r-93,n)}function e(n,r){return _0x50d0(n-406,r)}function a(n){return n[_0x50d0(0,-900)](/([.?*+^$[\]\\(){}|-])/g,"\\$1")}window[t(95,94)][e(408,411)]&&(n=n?a(n):"",r=r?a(r):"",window[t(93,94)][e(408,405)](null,"",location.pathname+(""!=n?location[e(409,406)][t(93,93)](new RegExp(e(410,411)+n+"="+r+t(99,98),"g"),"").replace(new RegExp(e(410,411)+n+(""!==r?"="+r:""),"g"),"")[t(96,93)](/^&/,"?"):"")+location.hash))}function _0x476a(){var n=["replace","history","replaceState","search","[?&]","[^&]+"];return(_0x476a=function(){return n})()}function _0x50d0(n,r){var t=_0x476a();return(_0x50d0=function(n,r){return t[n=+n]})(n,r)}; /*]]>*/</script>
  <script>/*<![CDATA[*/
/* Remove ?m=0, ?m=1 - some features may not work */ /*remPar("m", "0"); remPar("m", "1");*/
/* Disable Default Cookie Consent */ cookieChoices = {};
/* @shinsenter/defer.js */ !function(c,i,t){var f,o=/^data-(.+)/,u='IntersectionObserver',r=/p/.test(i.readyState),s=[],a=s.slice,d='lazied',n='load',e='pageshow',l='forEach',m='hasAttribute',h='shift';function p(e){i.head.appendChild(e)}function v(e,n){a.call(e.attributes)[l](n)}function y(e,n,t,o){return o=(o=n?i.getElementById(n):o)||i.createElement(e),n&&(o.id=n),t&&(o.onload=t),o}function b(e,n){return a.call((n||i).querySelectorAll(e))}function g(t,e){b('source',t)[l](g),v(t,function(e,n){(n=o.exec(e.name))&&(t[n[1]]=e.value)}),e&&(t.className+=' '+e),n in t&&t[n]()}function I(e){f(function(o){o=b(e||'[type=deferjs]'),function e(n,t){(n=o[h]())&&(n.parentNode.removeChild(n),(t=y(n.nodeName)).text=n.text,v(n,function(e){'type'!=e.name&&(t[e.name]=e.value)}),t.src&&!t[m]('async')?(t.onload=t.onerror=e,p(t)):(p(t),e()))}()})}(f=function(e,n){r?t(e,n):s.push(e,n)}).all=I,f.js=function(n,t,e,o){f(function(e){(e=y('SCRIPT',t,o)).src=n,p(e)},e)},f.css=function(n,t,e,o){f(function(e){(e=y('LINK',t,o)).rel='stylesheet',e.href=n,p(e)},e)},f.dom=function(e,n,t,o,i){function r(e){o&&!1===o(e)||g(e,t)}f(function(t){t=u in c&&new c[u](function(e){e[l](function(e,n){e.isIntersecting&&(n=e.target)&&(t.unobserve(n),r(n))})},i),b(e||'[data-src]')[l](function(e){e[m](d)||(e.setAttribute(d,''),t?t.observe(e):r(e))})},n)},f.reveal=g,c.Defer=f,c.addEventListener('on'+e in c?e:n,function(){for(I();s[0];t(s[h](),s[h]()))r=1})}(this,document,setTimeout),function(e,n){e.defer=n=e.Defer,e.deferscript=n.js,e.deferstyle=n.css,e.deferimg=e.deferiframe=n.dom}(this);
/* Adsense Functions */
/* Push Ads */ function pushAds(){for(let a,b=qSell("ins.adsbygoogle"),c=0;c<b.length;c++)a=b[c],a&&null==a.getAttribute("data-ad-pushed")&&!a.classList.contains("adsbygoogle-noablate")&&((adsbygoogle=window.adsbygoogle||[]).push({}),a.setAttribute("data-ad-pushed","true"))};
/* Insert After */ function insertAfter(e,n){var t;null!=e&&null!=n&&((t=n.parentNode).lastChild==n?t.appendChild(e):t.insertBefore(e,n.nextSibling))};
/* Middle Ads */ function middleAds(e,n){for(var t=0;t<n.length;t++){var l=document.getElementById("postBody"),d=document.getElementById(n[t][0]),l=l.getElementsByTagName(e);0<l.length?insertAfter(d,l[n[t][1]]):null!=d&&(d.remove())}};
/* Recurring Ads */ function recurAds(e,n){var t=document.getElementById("postBody"),l=n.ads?document.getElementById(n.ads):null,d=t.getElementsByTagName(e);if(0<d.length&&null!=l)for(var r,i=0;i<d.length;i++)i%n.every==0&&0!==i&&((r=document.createElement("div")).classList.add("recAd"),r.innerHTML=l?l.innerHTML:n.html||"",insertAfter(r,d[i])),i==d.length-1&&null!=l&&(l.remove());else null!=l&&(l.remove())};
/*]]>*/</script>
  
  <script id='polyfill-js'>&#39;IntersectionObserver&#39;in window||document.write(&#39;&lt;script src=&quot;https://polyfill.io/v3/polyfill.min.js?features=IntersectionObserver&quot;&gt;&lt;\/script&gt;&#39;);</script>
  
  <script type='application/ld+json'>{&quot;@context&quot;: &quot;https://schema.org&quot;, &quot;@type&quot;: &quot;WebSite&quot;, &quot;url&quot;: &quot;<data:blog.homepageUrl.canonical/>&quot;, &quot;name&quot;: &quot;<data:blog.title/>&quot;, &quot;alternateName&quot;: &quot;<data:blog.title/>&quot;, &quot;potentialAction&quot;: {&quot;@type&quot;: &quot;SearchAction&quot;, &quot;target&quot;: &quot;<data:blog.homepageUrl.canonical/>search?q={search_term_string}&quot;, &quot;query-input&quot;: &quot;required name=search_term_string&quot;}}
  </script>
  
  <!--[ Defer Adsense script use shinsenter/defer.js ]-->
  <!--<script type='deferjs' src='https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-0000000000000000' crossorigin='anonymous'/>-->
  
  <!--[ </head> close ]-->
  &lt;!--<head/><b:if cond='!data:blog.adsenseClientId'>--&gt;&lt;/head&gt;</b:if>

  <!--[ <body> open ]-->
  <body>
    <b:attr name='id' value='mainCont'/>
    
    <!--[ Show only one grid column in Mobile ]-->
    <b:class cond='data:view.isMultipleItems' name='oneGrd'/>
    
    <!--[ Enable tag below to change Mobile Menu style, try 'MN-2', 'MN-3' or 'MN-4' ]-->
    <b:class name='MN-3 mobS'/>
    
    <!--[ Enable tag below to change Header style (Only Mobile) ]-->
    <!--<b:class name='HD-2'/>-->
    
    <!--[ Enable tag below to make some Elements floating ]-->
    <b:class name='flT'/>
    
    <!--[ Enable tag below to minimize Navigation Menu in desktop ]-->
    <b:class name='hdMn'/>
    
    <!--[ Change basic layout style in desktop, try 'LS-2' or 'LS-3' ]-->
    <!--<b:class name='LS-2'/>-->
    
    <b:class name='bD'/>
    <b:class cond='data:view.url == data:view.url params { amp: &quot;1&quot; }' name='nAmp'/>
    <b:class cond='data:blog.languageDirection == &quot;rtl&quot;' name='Rtl'/>
    <b:class cond='data:view.isMultipleItems' name='onIndx'/>
    <b:class cond='data:view.isMultipleItems != data:view.isHomepage' name='onMlt'/>
    <b:class cond='data:view.url == data:blog.homepageUrl.canonical path &quot;search&quot;' name='onBlg'/>
    <b:class cond='data:view.isHomepage' name='onHm'/>
    <b:class cond='data:view.isSingleItem' name='onItm'/>
    <b:class cond='data:view.isPage' name='onPg'/>
    <b:class cond='data:view.isPost' name='onPs'/>
    <b:class cond='data:view.isError' name='on404'/>

    <b:if cond='!data:view.isError'>
      <!--[ Active function ]-->
      <input class='prfI hidden' id='offPrf' type='checkbox'/>
      <input class='navI hidden' id='offNav' type='checkbox'/>
      <input class='navM hidden' id='onMode' type='checkbox'/>
      
      <!--[ Preloader ]-->
      <b:include cond='data:view.isSingleItem and !data:view.isPreview' name='preloader'/>

      <!--[ Toast Notif ]-->
      <div class='tNtf alt notranslate' id='toastNotif'/>
      
      <!--[ Fixed Notif ]-->
      <div id='fixedNotif'/>
      
      <!--[ Neon Lighting ]-->
      <div class='nLght' id='neonLight'/>
    </b:if>
    
    <b:section class='hidden' id='license' name='License' showaddelement='false'>
      <b:widget id='HTML15' locked='true' title='Do not need License Key' type='HTML' version='2' visible='true'>
        <b:widget-settings>
          <b:widget-setting name='content'>Do not need License Key</b:widget-setting>
        </b:widget-settings>
        <b:includable id='main'>
          <div class='blog-admin' id='admCk'/>
          <span class='pu-views' data-add='true' data-id='WebsiteStats'/>
          <!--[ Blog Info, Don't delete it ]-->
          <script><b:eval expr='&quot;const blogUrl=&apos;&quot; + data:blog.canonicalHomepageUrl + &quot;&apos;;&quot;'/><b:eval expr='&quot;const blogID=&apos;&quot; + data:blog.blogId + &quot;&apos;;&quot;'/><b:eval expr='&quot;const postID=&apos;&quot; + (data:view.isSingleItem ? data:blog.postId : false) + &quot;&apos;;&quot;'/><b:eval expr='&quot;const blogTitle=&apos;&quot; + data:blog.title.jsonEscaped + &quot;&apos;;&quot;'/><b:eval expr='&quot;const isPreview=&apos;&quot; + (data:view.isPreview ? true : false) + &quot;&apos;;&quot;'/><b:eval expr='&quot;const isHomepage=&apos;&quot; + (data:view.isHomepage ? true : false) + &quot;&apos;;&quot;'/><b:eval expr='&quot;const isSearch=&apos;&quot; + (data:view.isSearch ? true : false) + &quot;&apos;;&quot;'/><b:eval expr='&quot;const isBlog=&apos;&quot; + (data:view.url == data:blog.homepageUrl.canonical path &quot;search&quot; ? true : false) + &quot;&apos;;&quot;'/><b:eval expr='&quot;const isSingleItem=&apos;&quot; + (data:view.isSingleItem ? true : false) + &quot;&apos;;&quot;'/><b:eval expr='&quot;const isPost=&apos;&quot; + (data:view.isPost ? true : false) + &quot;&apos;;&quot;'/><b:eval expr='&quot;const isPage=&apos;&quot; + (data:view.isPage ? true : false) + &quot;&apos;;&quot;'/><b:eval expr='&quot;const isError=&apos;&quot; + (data:view.isError ? true : false) + &quot;&apos;;&quot;'/><b:eval expr='&quot;const isMobile=&apos;&quot; + (data:blog.isMobileRequest ? true : false) + &quot;&apos;;&quot;'/><b:eval expr='&quot;const isPrivateBlog=&apos;&quot; + (data:blog.isPrivateBlog ? true : false) + &quot;&apos;;&quot;'/><b:eval expr='&quot;const analyticsID=&apos;&quot; + data:blog.analyticsAccountNumber + &quot;&apos;;&quot;'/><b:eval expr='&quot;const caPubAdsense=&apos;&quot; + data:blog.adsenseClientId + &quot;&apos;;&quot;'/><b:eval expr='&quot;const licenseKey=&quot; + (data:title contains &quot;#&quot; ? data:content : &quot;&apos;&quot; + data:content + &quot;&apos;&quot;) + &quot;;&quot;'/> /*<![CDATA[[*/ /* Adtnl JS */ function _0x5bbe(){var e=["4376qYpvmR","55450FYjxnM","toString","getPropertyValue","151770RPDrAo","getComputedStyle","147UGmURR","3700773qzVVQQ","drK","search","apply","length","classList","(((.+)+)+)+$","26ftCMLj","matchMedia","lgT","5531890YAocLl","theme0","webMode","matches","878332QutNDF","constructor","add","20709HiTzhJ","content","sLS","gLS","themeC","5791210DcgbdX","body","syD","webTheme"];return(_0x5bbe=function(){return e})()}function _0x4b2a59(e,t){return _0xce6d(t- -836,e)}function _0x5a8f2f(e,t){return _0xce6d(e-659,t)}function themeColor(e){function t(e,t){return _0xce6d(t-532,e)}function n(e,t){return _0xce6d(e-968,t)}e=window[t(543,532)](document[n(993,979)])[n(999,1010)]("--"+e);for(var r=qSell("meta[name='theme-color'],meta[name='msapplication-navbutton-color'],meta[name='apple-mobile-web-app-status-bar-style']"),a=0;a<r[t(525,538)];a++)r[a][n(988,988)]=e}function _0xce6d(e,t){var n=_0x5bbe();return(_0xce6d=function(e,t){return n[e=+e]})(e,t)}!function(){function e(e,t){return _0xce6d(t- -164,e)}function t(e,t){return _0xce6d(e- -735,t)}for(var n=_0x5bbe();;)try{if(664889==-parseInt(t(-706,-703))*(-parseInt(t(-726,-740))/2)+-parseInt(e(-171,-162))/3+parseInt(t(-719,-705))/4+parseInt(e(-157,-152))/5+parseInt(t(-703,-702))/6*(parseInt(t(-734,-722))/7)+parseInt(e(-140,-136))/8*(-parseInt(t(-716,-726))/9)+parseInt(t(-711,-702))/10)break;n.push(n.shift())}catch(e){n.push(n.shift())}}(),null!=Pu[_0x4b2a59(-829,-814)](_0x4b2a59(-837,-822))?function(){function e(e,t){return _0xce6d(t-896,e)}var t,n,r=(t=this,o=function(){if(n){var e=n[_0xce6d(5,-620)](t,arguments);return n=null,e}},n=function(){function e(e,t){return _0xce6d(t- -93,e)}return r[e(-71,-63)]().search(e(-69,-85))[e(-53,-63)]()[e(-62,-76)](r)[_0xce6d(4,-619)](e(-93,-85))},o);function a(e,t){return _0xce6d(e-984,t)}r();var o=document[e(925,921)];e(883,899)==Pu[e(904,918)](a(998,1011))?(addCt(o,a(987,972)),themeColor(e(903,919))):a(995,993)==Pu[a(1006,1002)](a(998,1011))?addCt(o,e(916,907)):a(1010,1023)==Pu[e(911,918)](a(998,1006))&&window.matchMedia&&(addCt(o,a(1010,1016)),window[a(994,994)]("(prefers-color-scheme: dark)")[e(911,911)]?(addCt(o,a(987,987)),themeColor(a(1007,995))):addCt(o,a(995,1003)))}():(Pu[_0x5a8f2f(680,676)](_0x5a8f2f(673,681),_0x5a8f2f(670,681)),document.body[_0x4b2a59(-822,-829)][_0x4b2a59(-809,-818)](_0x4b2a59(-827,-825))),null!=Pu.gLS(_0x4b2a59(-810,-809))?function(){function e(e,t){return _0xce6d(t-251,e)}var t=Pu[e(284,273)](_0xce6d(27,1005));document.body[e(253,258)][e(262,269)](t),themeColor("themeC")}():(Pu[_0x4b2a59(-819,-815)](_0x5a8f2f(686,681),_0x4b2a59(-812,-823)),document.body[_0x4b2a59(-813,-829)][_0x5a8f2f(677,686)](_0x4b2a59(-807,-823))); /* toastNotif */ function toastNotif(e){var tNotif = qSel('#toastNotif'); if (tNotif != null){tNotif.innerHTML = '<span>' + e + '</span>'}}; /* fixedNotif */ function fixedNotif(e){var fNotif = qSel('#fixedNotif'); if (fNotif != null){fNotif.innerHTML = "<input class='nJs hidden' id='forFxdNtf' type='checkbox'/><div class='noJs' data-text='" + e + "'><label for='forFxdNtf'></label></div>"}}; /* neonLight */ function neonLight(e){var neonL = qSel('#neonLight'); if (neonL != null) {addCt(neonL, 'vsbl'); setTimeout(function (){remCt(neonL, 'vsbl')},e+1000)}}; /* Vibrate */ function vibRate(e) {if ('vibrate' in navigator) {navigator.vibrate([e])}}; /*]]>*/</script>
        </b:includable>
      </b:widget>
    </b:section>

    <b:tag class='mntnM' name='div'>              
      <!--[ Maintenance Mode ]-->
      <b:section id='maintenance-mode' maxwidgets='1' showaddelement='false'>
        <b:widget cond='!data:view.isPreview' id='HTML00' locked='true' title='Site is Under Maintenance' type='HTML' version='2' visible='false'>
          <b:widget-settings>
            <b:widget-setting name='content'>Please come back again in...

&lt;script type=&#39;text/javascript&#39;&gt;/*&lt;![CDATA[*/
  const maintenanceEndOn = &#39;May 04 2022 00:00:00 GMT+05:30&#39;;
/*]]&gt;*/&lt;/script&gt;</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='mtm' id='maintainCont'>
              <div class='mtmC'>
                <div class='mtmH'><data:title/></div>
                <div class='mtmD'><data:content/></div>
                <div class='clock'>
                  <div class='tBox'>
                    <span class='days'>00</span>
                    <span class='unit'>Days</span>
                  </div>
                  <div class='tBox'>
                    <span class='hours'>00</span>
                    <span class='unit'>Hours</span>
                  </div>
                  <div class='tBox'>
                    <span class='minutes'>00</span>
                    <span class='unit'>Minutes</span>
                  </div>
                  <div class='tBox'>
                    <span class='seconds'>00</span>
                    <span class='unit'>Seconds</span>
                  </div>
                </div>
              </div>
            </div>
            <script>/*<![CDATA[*/ /* Maintenance Mode */ if (qSel('#maintainCont')!=null){const dayDisplay=qSel('.tBox .days'),hourDisplay=qSel('.tBox .hours'),minuteDisplay=qSel('.tBox .minutes'),secondDisplay=qSel('.tBox .seconds'),maintainCont=qSel('#maintainCont'),maintainEndDate=new Date(maintenanceEndOn);let maintenanceDone=!1;const updateTimer=()=>{let e=new Date;var t=maintainEndDate.getTime()-e.getTime();t<=1e3&&(maintenanceDone=!0);var n=36e5,a=Math.floor(t/864e5),o=Math.floor(t%864e5/n),n=Math.floor(t%n/6e4),t=Math.floor(t%6e4/1e3);dayDisplay.innerText=a<10?'0'+a:a,hourDisplay.innerText=o<10?'0'+o:o,minuteDisplay.innerText=n<10?'0'+n:n,secondDisplay.innerText=t<10?'0'+t:t};setInterval(()=>{maintenanceDone?addCt(maintainCont,'hdn'):updateTimer()},1000);}; /*]]>*/</script>
          </b:includable>
        </b:widget>
      </b:section>
    </b:tag>
    
    <b:tag class='mainWrp' cond='!data:view.isError' name='div'>
      <b:if cond='!data:view.isError'>
        
        <!--[ Header section ]-->
        <header class='header' id='header'>
          
          <!--[ Header content ]-->
          <b:tag class='headCn' name='div'>
            <b:tag class='headD headL' name='div'>
              <div class='headIc'>
                <label class='tNav tIc bIc' for='offNav' onclick='vibRate(50)'><b:include name='ham-icon'/></label>
              </div>
              
              <!--[ Header widget ]-->
              <b:section class='headN' id='header-title' maxwidgets='1' showaddelement='false'>
                <b:widget id='Header1' locked='true' title='Fizadis (Header)' type='Header' version='2' visible='true'>
                  <b:widget-settings>
                    <b:widget-setting name='displayUrl'/>
                    <b:widget-setting name='displayHeight'>0</b:widget-setting>
                    <b:widget-setting name='sectionWidth'>-1</b:widget-setting>
                    <b:widget-setting name='useImage'>false</b:widget-setting>
                    <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
                    <b:widget-setting name='imagePlacement'>BEHIND</b:widget-setting>
                    <b:widget-setting name='displayWidth'>0</b:widget-setting>
                  </b:widget-settings>
                  <b:includable id='main' var='this'>
                    <b:include cond='data:imagePlacement in {&quot;REPLACE&quot;, &quot;BEFORE_DESCRIPTION&quot;}' name='image'/>
                    <b:include cond='data:imagePlacement not in {&quot;REPLACE&quot;, &quot;BEFORE_DESCRIPTION&quot;}' name='title'/>
                    <b:include cond='data:imagePlacement != &quot;REPLACE&quot;' name='description'/>
                    <b:include cond='data:imagePlacement == &quot;BEHIND&quot;' name='behindImageStyle'/>
                  </b:includable>
                  <b:includable id='behindImageStyle'>
                    <b:if cond='data:sourceUrl'>
                      <b:include cond='data:this.image' data='{image: data:this.image, selector: &quot;.Header&quot;}' name='responsiveImageStyle'/>
                    </b:if>
                  </b:includable>
                  <b:includable id='description'>
                    <b:if cond='data:this.description'>
                      <div class='headDsc hidden'><data:this.description/></div>
                    </b:if>
                  </b:includable>
                  <b:includable id='image'>
                    <!-- Header Image -->
                    <a expr:href='data:blog.homepageUrl.canonical'><img expr:alt='data:title' expr:height='data:height' expr:src='resizeImage(data:sourceUrl, 200)' expr:width='data:width'/></a>
                    <b:include cond='data:this.imagePlacement == &quot;REPLACE&quot;' name='title'/>
                  </b:includable>
                  <b:includable id='title'>
                    <!-- Header Title -->
                    <div class='headInnr'>
                      <b:class cond='data:this.imagePlacement == &quot;REPLACE&quot;' name='hidden'/>
                      <b:tag class='headH notranslate' expr:name='!data:view.isSingleItem ? &quot;h1&quot; : &quot;h2&quot;'>
                        <b:class cond='data:this.description' name='hasSub'/>
                        <bdi>
                          <b:tag class='headTtl' expr:name='!data:view.isHomepage ? &quot;a&quot; : &quot;span&quot;'>
                            <b:attr cond='!data:view.isHomepage' expr:value='data:blog.homepageUrl.canonical' name='href'/>
                            <data:title/>
                          </b:tag>
                        </bdi>
                        <!--[ Header Description ]-->
                        <b:tag class='headSub' cond='data:this.description' expr:data-text='data:this.description' name='span'/>
                      </b:tag>
                    </div>
                  </b:includable>
                </b:widget>
              </b:section>
            </b:tag>
            
            <b:tag class='headD headR' name='div'>
              <b:tag class='headI' cond='!data:view.isLayoutMode' name='div'>
                <b:section class='headS' id='header-search' maxwidgets='1' showaddelement='false'>
                  <b:widget id='BlogSearch1' locked='false' title='Try &apos;Adventure&apos;' type='BlogSearch' version='2' visible='true'>
                    <b:includable id='main'>
                      <b:include name='content'/>
                    </b:includable>
                    <b:includable id='content'>
                      <b:include name='searchForm'/>
                    </b:includable>
                    <b:includable id='searchForm'>
                      <form class='srchF' expr:action='data:blog.searchUrl'>
                        <b:attr cond='not data:view.isPreview' name='target' value='_top'/>
                        <b:include name='urlParamsAsFormInput'/>
                        <input autocomplete='off' expr:aria-label='data:messages.searchThisBlog' expr:placeholder='data:title' expr:value='data:view.isSearch ? data:view.search.query.escaped : &quot;&quot;' id='searchIn' minlength='3' name='q' required='required'/>
                        <b:include name='searchSubmit'/>
                        <button aria-label='Clear' class='sb' data-text='âœ•' type='reset'/>
                        <span class='fCls'/>
                      </form>
                    </b:includable>
                    <b:includable id='searchSubmit'>
                      <span class='sb'><b:include name='search-icon'/></span>
                    </b:includable>
                  </b:widget>
                </b:section>
                <b:section class='headP' id='header-icon' maxwidgets='2' showaddelement='false'>
                  <b:widget id='TextList000' locked='true' title='Header Icon' type='TextList' version='2' visible='true'>
                    <b:widget-settings>
                      <b:widget-setting name='shownum'>5</b:widget-setting>
                      <b:widget-setting name='item-4'>Mode</b:widget-setting>
                      <b:widget-setting name='item-3'>Profile</b:widget-setting>
                      <b:widget-setting name='sorting'>NONE</b:widget-setting>
                      <b:widget-setting name='item-2'>Translate</b:widget-setting>
                      <b:widget-setting name='item-1'>Bookmark</b:widget-setting>
                      <b:widget-setting name='item-0'>Search</b:widget-setting>
                    </b:widget-settings>
                    <b:includable id='main'>
                      <b:include name='content'/>
                    </b:includable>
                    <b:includable id='content'>
                      <ul class='headIc'>
                        <b:loop index='icon' values='data:items' var='item'>
                          <b:if cond='data:icon &lt;= 5'>
                            <b:if cond='data:item == &quot;Search&quot;'>
                              <li>
                                <b:class cond='data:item == &quot;Search&quot;' name='isSrh'/>
                                <!--[ Search button ]-->
                                <label class='tSrch tIc bIc' expr:aria-label='data:item' for='searchIn' onclick='vibRate(50)'><b:include name='search-icon'/></label>
                              </li>
                                
                              <b:elseif cond='data:item == &quot;Mode&quot;'/>
                              <li>
                                <b:class cond='data:item == &quot;Mode&quot;' name='isDrk'/>
                                <!--[ Dark mode button ]-->
                                <label class='navM tDark tIc tDL bIc' expr:aria-label='data:item' for='onMode' role='button'><b:include name='moon-sun-icon'/></label>
                                <div class='headM' data-text='Change Mode'>
                                  <span aria-label='Light' class='lgtB' onclick='modeL(); vibRate(50)' role='button'/>
                                  <span aria-label='Dark' class='drkB' onclick='modeD(); vibRate(50)' role='button'/>
                                  <span aria-label='System Default' class='sydB' onclick='modeS(); vibRate(50)' role='button'/>
                                  <!--[ Delete below code to remove Theme Customisation ]-->
                                  <label aria-label='Theme Color' class='themeBtn' for='forCusThm' id='themeBtn' role='button'/>
                                </div>
                                <label class='fCls' for='onMode'/>
                                <script>/*<![CDATA[*/ if (window.matchMedia){qSel('.headR .headM .sydB').style.display='block'}; /*]]>*/</script>
                                
                                <b:include name='theme-custom'/>
                              </li>
                              
                              <b:elseif cond='data:item == &quot;Bookmark&quot;'/>
                              <b:if cond='!data:view.isPreview'>
                                <li class='isBkm'>
                                  <!--[ Bookmark ]-->
                                  <label aria-label='Bookmark' class='tBkmt tIc bIc n' for='offBkm' onclick='vibRate(50)'><b:include name='bookmark-icon'/></label>
                                </li>
                              </b:if>
                              
                              <b:elseif cond='data:item == &quot;Translate&quot;'/>
                              <li class='gTrans hidden'>
                                <!--[ Google Translate button ]-->
                                <span class='tIc bIc' expr:aria-label='data:item' onclick='vibRate(50)' role='button'><b:include name='translate-icon'/><span id='google_translate_element'/></span>
                              </li>
                              
                              <b:elseif cond='data:item == &quot;Profile&quot;'/>
                              <b:if cond='data:view.isHomepage'>
                                <li>
                                  <!--[ Profile button ]-->
                                  <label class='tPrfl tIc bIc' expr:aria-label='data:item' for='offPrf' onclick='vibRate(50)'><b:include name='profile-icon'/></label>
                                </li>
                              </b:if>
                            </b:if>
                          </b:if>
                        </b:loop>
                      </ul>
                    </b:includable>
                  </b:widget>
                  <b:widget cond='data:view.isHomepage' id='Profile1' locked='true' title='Contributors' type='Profile' version='2' visible='true'>
                    <b:widget-settings>
                      <b:widget-setting name='showaboutme'>true</b:widget-setting>
                      <b:widget-setting name='showlocation'>true</b:widget-setting>
                    </b:widget-settings>
                    <b:includable id='main' var='this'>
                      <b:include name='content'/>
                    </b:includable>
                    <b:includable id='authorProfileImage'>
                      <div class='im' expr:data-style='&quot;background-image: url(&quot; + resizeImage(data:authorPhoto.image,60) + &quot;)&quot;'>
                        <b:class cond='data:view.url != data:view.url params { amp: &quot;1&quot; }' name='lazy'/>
                        <b:attr cond='data:view.url == data:view.url params { amp: &quot;1&quot; }' expr:value='&quot;background-image: url(&quot; + resizeImage(data:authorPhoto.image,60) + &quot;)&quot;' name='style'/>
                      </div>
                      <noscript><div class='im' expr:style='&quot;background-image: url(&quot; + resizeImage(data:authorPhoto.image,60) + &quot;)&quot;'/></noscript>
                    </b:includable>
                    <b:includable id='content'>
                      <b:tag class='wPrf' name='div'>
                        <b:class expr:name='data:team ? &quot;tm&quot; : &quot;sl&quot;'/>
                        <b:class cond='data:showlocation and data:location != &quot;&quot;' name='nLoc'/>
                        <div class='prfS fixLs'>
                          <b:include name='profileHeader'/>
                          <div class='prfC'>
                            <b:if cond='data:team'>
                              <b:include name='teamProfile'/>
                              <b:else/>
                              <b:include name='userProfile'/>
                            </b:if>
                          </div>
                        </div>
                      </b:tag>
                      
                      <label class='fCls' for='offPrf'/>
                    </b:includable>
                    <b:includable id='defaultProfileImage'>
                      <div class='im'><b:include name='profile-icon'/></div>
                    </b:includable>
                    <b:includable id='profileHeader'>
                      <div class='prfH fixH fixT' expr:data-text='data:title'>
                        <label aria-label='Close' class='c cl' for='offPrf'/>
                      </div>
                    </b:includable>
                    <b:includable id='profileImage'>
                      <b:if cond='data:authorPhoto.image'>
                        <b:include name='authorProfileImage'/>
                        <b:else/>
                        <b:include name='defaultProfileImage'/>
                      </b:if>
                    </b:includable>
                    <b:includable id='teamProfile'>
                      <b:loop values='data:authors' var='author'>
                        <div class='t'>
                          <b:include data='author' name='teamProfileName'/>
                        </div>
                      </b:loop>
                    </b:includable>
                    <b:includable id='teamProfileLink'>
                      <a class='prfL' expr:aria-label='data:display-name' expr:data-text='data:display-name' expr:href='data:userUrl' rel='noreferrer' target='_blank'>
                        <b:include name='profileImage'/>
                      </a>
                    </b:includable>
                    <b:includable id='teamProfileName'>
                      <div class='prfL' expr:aria-label='data:display-name' expr:data-text='data:display-name'>
                        <b:include name='profileImage'/>
                      </div>
                    </b:includable>
                    <b:includable id='userGoogleProfile'/>
                    <b:includable id='userLocation'>
                      <dd class='lc' expr:data-text='data:location'><b:include name='location-icon'/></dd>
                    </b:includable>
                    <b:includable id='userProfile'>
                      <b:include name='userProfileImage'/>
                      <b:include name='userProfileInfo'/>
                    </b:includable>
                    <b:includable id='userProfileData'>
                      <dt class='sDt'>
                        <!--[ Profile name without link ]-->
                        <!--<b:include name='userProfileNameOnly'/>-->
                        
                        <!--[ Profile name with link ]-->
                        <b:include name='userProfileLink'/>
                      </dt>
                    </b:includable>
                    <b:includable id='userProfileImage'>
                      <div class='sImg'>
                        <b:include name='profileImage'/>
                      </div>
                    </b:includable>
                    <b:includable id='userProfileInfo'>
                      <dl class='sInf'>
                        <b:include name='userProfileData'/>
                        <b:include cond='data:aboutme != &quot;&quot;' name='userProfileText'/>
                        <b:include cond='data:showlocation and data:location != &quot;&quot;' name='userLocation'/>
                      </dl>
                    </b:includable>
                    <b:includable id='userProfileLink'>
                      <a class='l extL' expr:href='data:userUrl' expr:title='data:messages.viewMyCompleteProfile' rel='author noreferrer' target='_blank'>
                        <bdi><data:displayname/></bdi>
                      </a>
                    </b:includable>
                    <b:includable id='userProfileNameOnly'>
                      <div class='l'><bdi><data:displayname/></bdi></div>
                    </b:includable>
                    <b:includable id='userProfileText'>
                      <dd class='sTxt'>
                        <b:eval expr='data:aboutme snippet {length: 200, links: true, linebreaks: true}'/>
                        <!--<data:aboutme/>-->
                      </dd>
                    </b:includable>
                    <b:includable id='viewProfileLink'/>
                  </b:widget>
                </b:section>
              </b:tag>
            </b:tag>
          </b:tag>
        </header>
        
        <!--[ Content section ]-->
        <div class='mainIn'>
          
          <!--[ Menu content ]-->
          <div class='blogMn'>
            <div class='mnBr'>
              <div class='mnBrs'>
                <b:if cond='!data:view.isLayoutMode'>
                  <div class='mnH'>
                    <label aria-label='Close' class='c' data-text='Close' for='offNav'/>
                  </div>
                </b:if>
                
                <!--[ Mobile additional menu(only shown in mobile view) ]-->
                <b:section class='mnMob' id='nav-widget-1' maxwidgets='2' showaddelement='false'>
                  <b:widget id='PageList002' locked='true' title='Additional Menu' type='PageList' version='2' visible='true'>
                    <b:widget-settings>
                      <b:widget-setting name='pageListJson'><![CDATA[{"link1":{"href":"/p/disclaimer.html","position":1,"title":"Disclaimer"},"link0":{"href":"/p/sitemap.html","position":0,"title":"Sitemap"},"link2":{"href":"/p/privacy.html","position":2,"title":"Privacy"}}]]></b:widget-setting>
                      <b:widget-setting name='homeTitle'>Home</b:widget-setting>
                    </b:widget-settings>
                    <b:includable id='main'>
                      <b:include name='content'/>
                    </b:includable>
                    <b:includable id='content'>
                      <b:include name='pageList'/>
                    </b:includable>
                    <b:includable id='overflowButton'/>
                    <b:includable id='overflowablePageList'/>
                    <b:includable id='pageLink'>
                      <b:if cond='data:t &lt;= 3'>
                        <li>
                          <b:class cond='data:overflow' name='overflowable-item'/>
                          <b:class cond='data:link.isCurrentPage' name='selected'/>
                           <b:tag expr:name='data:link.href != &quot;#&quot; ? &quot;a&quot; : &quot;span&quot;'>
                            <b:attr cond='data:link.href != &quot;#&quot;' expr:value='data:link.href' name='href'/>
                            <data:link.title/>
                          </b:tag>
                        </li>
                      </b:if>
                    </b:includable>
                    <b:includable id='pageList'>
                      <ul class='mMenu'>
                        <b:class cond='data:pageListClass' expr:name='data:pageListClass'/>
                        <b:loop index='t' values='data:links' var='link'>
                          <b:include name='pageLink'/>
                        </b:loop>
                      </ul>
                    </b:includable>
                  </b:widget>
                  <b:widget id='LinkList002' locked='true' title='Social Media' type='LinkList' version='2' visible='true'>
                    <b:widget-settings>
                      <b:widget-setting name='text-8'>Pinterest</b:widget-setting>
                      <b:widget-setting name='link-7'>#</b:widget-setting>
                      <b:widget-setting name='link-8'>#</b:widget-setting>
                      <b:widget-setting name='link-5'>#</b:widget-setting>
                      <b:widget-setting name='link-6'>#</b:widget-setting>
                      <b:widget-setting name='link-3'>#</b:widget-setting>
                      <b:widget-setting name='link-4'>#</b:widget-setting>
                      <b:widget-setting name='text-1'>Instagram</b:widget-setting>
                      <b:widget-setting name='text-0'>Facebook</b:widget-setting>
                      <b:widget-setting name='text-3'>Tiktok</b:widget-setting>
                      <b:widget-setting name='text-2'>Twitter</b:widget-setting>
                      <b:widget-setting name='text-5'>Telegram</b:widget-setting>
                      <b:widget-setting name='text-4'>Whatsapp</b:widget-setting>
                      <b:widget-setting name='text-7'>LinkedIn</b:widget-setting>
                      <b:widget-setting name='text-6'>Youtube</b:widget-setting>
                      <b:widget-setting name='shownum'>6</b:widget-setting>
                      <b:widget-setting name='sorting'>NONE</b:widget-setting>
                      <b:widget-setting name='link-1'>#</b:widget-setting>
                      <b:widget-setting name='link-2'>#</b:widget-setting>
                      <b:widget-setting name='link-0'>#</b:widget-setting>
                    </b:widget-settings>
                    <b:includable id='main'>
                      <b:include name='content'/>
                    </b:includable>
                    <b:includable id='content'>
                      <div class='mNav'>
                        <label class='tIc bIc' for='offNav'><b:include name='plus-icon'/></label>
                      </div>
                      <ul class='mSoc'>
                        <b:loop index='soc' values='data:links' var='link'>
                          <b:if cond='data:soc &lt;= 5'>
                            <li>
                              <b:tag class='a tIc bIc' expr:name='data:link.target != &quot;#&quot; ? &quot;a&quot; : &quot;span&quot;'>
                                <b:attr cond='data:link.target != &quot;#&quot;' expr:value='data:link.target' name='href'/>
                                <b:attr cond='data:link.target != &quot;#&quot;' name='role' value='button'/>
                                <b:attr cond='data:link.target != &quot;#&quot;' expr:value='data:link.name' name='aria-label'/>
                                <b:attr cond='data:link.target != &quot;#&quot;' name='target' value='_blank'/>
                                <b:attr cond='data:link.target != &quot;#&quot;' name='rel' value='noopener'/>
                                <b:if cond='data:link.name == &quot;Facebook&quot;'>
                                  <b:include name='facebook-icon'/>
                          
                                  <b:elseif cond='data:link.name == &quot;Instagram&quot;'/>
                                  <b:include name='instagram-icon'/>
                              
                                  <b:elseif cond='data:link.name == &quot;Twitter&quot;'/>
                                  <b:include name='twitter-icon'/>
                              
                                  <b:elseif cond='data:link.name == &quot;Youtube&quot;'/>
                                  <b:include name='youtube-icon'/>
                              
                                  <b:elseif cond='data:link.name == &quot;LinkedIn&quot;'/>
                                  <b:include name='linkedIn-icon'/>
                              
                                  <b:elseif cond='data:link.name == &quot;Pinterest&quot;'/>
                                  <b:include name='pinterest-icon'/>
                            
                                  <b:elseif cond='data:link.name == &quot;Whatsapp&quot;'/>
                                  <b:include name='whatsapp-icon'/>
                              
                                  <b:elseif cond='data:link.name == &quot;Telegram&quot;'/>
                                  <b:include name='telegram-icon'/>
                              
                                  <b:elseif cond='data:link.name == &quot;Tiktok&quot;'/>
                                  <b:include name='tiktok-icon'/>
                              
                                  <b:else/>
                                  <b:include name='circle-icon'/>
                                </b:if>
                              </b:tag>
                            </li>
                          </b:if>
                        </b:loop>
                      </ul>
                    </b:includable>
                  </b:widget>
                </b:section>
                
                <b:section class='mnMen' id='nav-widget-2' maxwidgets='2' showaddelement='false'>
                  <b:widget id='HTML000' locked='true' title='Navigation Menu' type='HTML' version='2' visible='true'>
                    <b:widget-settings>
                      <b:widget-setting name='content'><![CDATA[<!--[ you can only edit this widget from HTML theme ]-->]]></b:widget-setting>
                    </b:widget-settings>
                    <b:includable id='main'>
                      <ul class='mnMn' itemscope='itemscope' itemtype='https://schema.org/SiteNavigationElement'>
                  
                        <b:if cond='!data:view.isHomepage'>
                          <!--[ Home link ]-->
                          <li class='hm'>
                            <a class='a' expr:href='data:blog.homepageUrl.canonical' itemprop='url'>
                              <b:include name='home-icon'/>
                        
                              <!--[ Title navigation ]-->
                              <span class='n' itemprop='name'><data:messages.home/></span>
                            </a>
                          </li>
                        </b:if>
                        
                        <!--[ Standar menu ]-->
                        <li>
                          <!--[ Change attribute href='#' to add url ]-->
                          <a class='a' href='#' itemprop='url'>
                            <!--[ Icon ]-->
                            <svg class='line' viewBox='0 0 24 24'><g transform='translate(2.000000, 2.000000)'><path class='svgC' d='M4.0706,14.459 C4.0706,14.459 4.8826,12.822 6.0646,12.822 C7.2466,12.822 7.8506,14.197 9.1606,14.197 C10.4696,14.197 11.9386,10.749 13.4226,10.749 C14.9046,10.749 15.9706,13.14 15.9706,13.14'/><path class='svgC' d='M8.1393,7.1049 C8.1393,7.9649 7.4423,8.6629 6.5813,8.6629 C5.7213,8.6629 5.0243,7.9649 5.0243,7.1049 C5.0243,6.2449 5.7213,5.5469 6.5813,5.5469 C7.4423,5.5479 8.1393,6.2449 8.1393,7.1049 Z'/><path d='M0.7503,10.0001 C0.7503,16.9371 3.0633,19.2501 10.0003,19.2501 C16.9373,19.2501 19.2503,16.9371 19.2503,10.0001 C19.2503,3.0631 16.9373,0.7501 10.0003,0.7501 C3.0633,0.7501 0.7503,3.0631 0.7503,10.0001 Z'/></g></svg>
                          
                            <!--[ Title navigation ]-->
                            <span class='n' itemprop='name'>Landing Page</span>
                          </a>
                        </li>
                    
                        <!--[ Dropdown style 1 ]-->
                        <li class='drp'>
                          <input class='drpI hidden' id='drpDwn-1' name='drpDwn' type='checkbox'/>
                          <label class='a' for='drpDwn-1'>
                            <!--[ Icon ]-->
                            <b:include name='folder-icon'/>
                          
                            <!--[ Title navigation ]-->
                            <span class='n'>Sub Menu</span>
                        
                            <b:include name='arow-down-icon'/>
                          </label>
                          <ul>
                            <!--[ Change attribute href='#' to add url ]-->
                            <li itemprop='name'><a href='#' itemprop='url'>Sub Menu 01</a></li>
                            <li itemprop='name'><a href='#' itemprop='url'>Sub Menu 02</a></li>
                            <li itemprop='name'><a href='#' itemprop='url'>Sub Menu 03</a></li>
                            <li itemprop='name'><a href='#' itemprop='url'>Sub Menu 04</a></li>
                          </ul>
                        </li>
                    
                        <!--[ Dropdown style 3 ]-->
                        <li class='drp br'>
                          <input class='drpI hidden' id='drpDwn-2' name='drpDwn' type='checkbox'/>
                          <label class='a' for='drpDwn-2'>
                            <!--[ Icon ]-->
                            <b:include name='folder-icon'/>
                          
                            <!--[ Title navigation ]-->
                            <span class='n new'>Sub Menu</span>
                        
                            <b:include name='arow-down-icon'/>
                          </label>
                          <ul class='s'>
                            <!--[ Change attribute href='#' to add url ]-->
                            <li itemprop='name'><a href='#' itemprop='url'><span><b:include name='folder-icon'/>Sub Menu 01</span></a></li>
                            <li itemprop='name'><a href='#' itemprop='url'><span><b:include name='folder-icon'/>Sub Menu 02</span></a></li>
                            <li itemprop='name'><a href='#' itemprop='url'><span><b:include name='folder-icon'/>Sub Menu 03</span></a></li>
                            <li itemprop='name'><a href='#' itemprop='url'><span><b:include name='folder-icon'/>Sub Menu 04</span></a></li>
                            <li itemprop='name'><a href='#' itemprop='url'><span><b:include name='folder-icon'/>Sub Menu 05</span></a></li>
                          </ul>
                        </li>
                          
                        <!--[ Standar menu ]-->
                        <li>
                          <!--[ Change attribute href='#' to add url ]-->
                          <a class='a' expr:href='data:blog.homepageUrl.canonical path &quot;p/about.html&quot;' itemprop='url'>
                            <!--[ Icon ]-->
                            <b:include name='profiles-icon'/>
                          
                            <!--[ Title navigation ]-->
                            <span class='n' itemprop='name'>About</span>
                          </a>
                        </li>
                          
                        <!--[ Standar menu ]-->
                        <li class='br'>
                          <!--[ Change attribute href='#' to add url ]-->
                          <a class='a' expr:href='data:blog.homepageUrl.canonical path &quot;p/contact.html&quot;' itemprop='url'>
                            <!--[ Icon ]-->
                            <b:include name='message-icon'/>
                          
                            <!--[ Title navigation ]-->
                            <span class='n' itemprop='name'>Contact</span>
                          </a>
                        </li>
                        
                        <!--[ Standar menu ]-->
                        <li class='br'>
                          <!--[ Change attribute href='#' to add url ]-->
                          <a class='a' expr:href='data:blog.homepageUrl.canonical path &quot;p/safelink-generator.html&quot;' itemprop='url'>
                            <!--[ Icon ]-->
                            <b:include name='shield-done-icon'/>
                          
                            <!--[ Title navigation ]-->
                            <span class='n' itemprop='name'>Safelink</span>
                          </a>
                        </li>
                      
                        <!--[ Dropdown style 2 ]-->
                        <li class='drp mr'>
                          <input checked='checked' class='drpI hidden' id='drpMr-2' name='drpDwn' type='checkbox'/>
                          <label class='a' for='drpMr-2'>
                            
                            <!--[ Title navigation ]-->
                            <span class='n'>More...</span>
                          
                            <b:include name='arow-down-icon'/>
                          </label>
                          <ul>
                            <!--[ Change attribute href='#' to add url ]-->
                            <li itemprop='name'><a class='new' expr:href='data:blog.canonicalUrl params { hl: &quot;ar&quot; }' itemprop='url'>Try RTL Mode</a></li>
                          </ul>
                        </li>
                          
                        <li>
                          <a class='a' href='https://www.Dr-FarFar.com' itemprop='url' rel='nofollow' target='_blank'>
                            <svg class='line' viewBox='0 0 24 24'><polyline points='8 17 12 21 16 17'/><line x1='12' x2='12' y1='12' y2='21'/><path d='M20.88 18.09A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.29'/></svg>
                            <span class='n' itemprop='name'>Download this theme</span>
                          </a>
                        </li>
                        
                      </ul>
                    </b:includable>
                  </b:widget>
                  <b:widget id='PageList000' locked='true' title='Navigation Menu (Simple)' type='PageList' version='2' visible='false'>
                    <b:widget-settings>
                      <b:widget-setting name='pageListJson'><![CDATA[{"link1":{"href":"#","position":1,"title":"About"},"link0":{"href":"#","position":0,"title":"Home"},"link3":{"href":"#","position":3,"title":"Custom menu"},"link2":{"href":"#","position":2,"title":"Contact"}}]]></b:widget-setting>
                      <b:widget-setting name='homeTitle'>Home</b:widget-setting>
                    </b:widget-settings>
                    <b:includable id='main'>
                      <b:include name='content'/>
                    </b:includable>
                    <b:includable id='content'>
                      <b:include name='pageList'/>
                    </b:includable>
                    <b:includable id='overflowButton'/>
                    <b:includable id='overflowablePageList'/>
                    <b:includable id='pageLink'>
                      <b:if cond='data:i &lt;= 4'>
                        <li>
                          <b:class cond='data:link.isCurrentPage' name='selected'/>
                          <b:class cond='data:link.href == data:blog.homepageUrl.canonical' name='hm'/>
                        
                          <b:tag class='a' expr:name='!data:link.isCurrentPage and data:link.href != &quot;#&quot; ? &quot;a&quot; : &quot;span&quot;'>
                            <b:attr cond='!data:link.isCurrentPage and data:link.href != &quot;#&quot;' expr:value='data:link.href' name='href'/>
                            <b:attr cond='!data:link.isCurrentPage and data:link.href != &quot;#&quot;' name='itemprop' value='url'/>
                              
                            <!--[ Icon ]-->
                            <b:if cond='data:link.title == &quot;Home&quot;'>
                              <b:include name='home-icon'/>
                                
                              <b:elseif cond='data:link.title == &quot;About&quot;'/>
                              <b:include name='profiles-icon'/>
                              
                              <b:elseif cond='data:link.title == &quot;Contact&quot;'/>
                              <b:include name='message-icon'/>
                                
                              <b:elseif cond='data:link.title == &quot;Download this theme&quot; or data:link.title == &quot;Download&quot;'/>
                              <b:include name='download-icon'/>
                              
                              <b:elseif cond='data:link.title == &quot;Sitemap&quot;'/>
                              <b:include name='document-icon'/>
                          
                              <b:elseif cond='data:link.title == &quot;Disclaimers&quot;'/>
                              <b:include name='shield-done-icon'/>
                          
                            
                              <b:elseif cond='data:link.title == &quot;Privacy&quot;'/>
                              <b:include name='lock-icon'/>
                                
                              <b:else/>
                              <b:include name='folder-icon'/>
                            </b:if>
                              
                            <!--[ Title navigation ]-->
                            <span class='n'>
                              <b:attr cond='!data:link.isCurrentPage and data:link.href != &quot;#&quot;' name='itemprop' value='name'/>
                              <data:link.title/>
                            </span>
                          </b:tag>
                        </li>
                      </b:if>
                        
                      <b:if cond='data:i == 5'>
                        <li class='drp'>
                          <input class='dropIn hidden' id='drpDwn-00' name='drpDwn' type='checkbox'/>
                          <label class='a' for='drpDwn-00'>
                            <!--[ Icon ]-->
                            <b:include name='folder-icon'/>
                              
                            <!--[ Title navigation ]-->
                            <span class='n'><data:messages.moreEllipsis/></span>
                        
                            <b:include name='arow-down-icon'/>
                          </label>
                          <ul>
                            <b:loop index='all' values='data:links' var='link'>
                              <b:if cond='data:all &gt;= 5'>
                                <li>
                                  <b:attr cond='!data:link.isCurrentPage and data:link.href != &quot;#&quot;' name='itemprop' value='name'/>
                                  
                                  <b:tag expr:name='!data:link.isCurrentPage and data:link.href != &quot;#&quot; ? &quot;a&quot; : &quot;span&quot;'>
                                    <b:attr cond='!data:link.isCurrentPage and data:link.href != &quot;#&quot;' expr:value='data:link.href' name='href'/>
                                    <b:attr cond='!data:link.isCurrentPage and data:link.href != &quot;#&quot;' name='itemprop' value='url'/>
                                    <data:link.title/>
                                  </b:tag>
                                </li>
                              </b:if>
                            </b:loop>
                          </ul>
                        </li>
                      </b:if>
                    </b:includable>
                    <b:includable id='pageList'>
                      <ul class='mnMn' itemscope='itemscope' itemtype='https://schema.org/SiteNavigationElement'>
                        <b:loop index='i' values='data:links' var='link'>
                          <b:include name='pageLink'/>
                        </b:loop>
                      </ul>
                    </b:includable>
                  </b:widget>
                </b:section>
              </div>
            </div>
            <label class='fCls' for='offNav'/>
          </div>
          
          <!--[ Blog content ]-->
          <div class='blogCont'>
            <div class='secIn'>
              
              <b:section cond='!data:view.isPreview' id='notif-widget' maxwidgets='1' showaddelement='false'>
                <b:widget id='LinkList001' locked='true' title='Scroll Menu' type='LinkList' version='2' visible='true'>
                  <b:widget-settings>
                    <b:widget-setting name='link-5'>/search/label/Food</b:widget-setting>
                    <b:widget-setting name='link-6'>/search/label/Adventure</b:widget-setting>
                    <b:widget-setting name='link-3'>/search/label/Photography</b:widget-setting>
                    <b:widget-setting name='link-4'>/search/label/Nature</b:widget-setting>
                    <b:widget-setting name='text-1'>Travel</b:widget-setting>
                    <b:widget-setting name='text-0'>Art</b:widget-setting>
                    <b:widget-setting name='text-3'>Photography</b:widget-setting>
                    <b:widget-setting name='text-2'>Life Style</b:widget-setting>
                    <b:widget-setting name='text-5'>Food</b:widget-setting>
                    <b:widget-setting name='text-4'>Nature</b:widget-setting>
                    <b:widget-setting name='text-6'>Adventure</b:widget-setting>
                    <b:widget-setting name='shownum'>7</b:widget-setting>
                    <b:widget-setting name='sorting'>NONE</b:widget-setting>
                    <b:widget-setting name='link-1'>/search/label/Travel</b:widget-setting>
                    <b:widget-setting name='link-2'>/search/label/Life%20Style</b:widget-setting>
                    <b:widget-setting name='link-0'>/search/label/Art</b:widget-setting>
                  </b:widget-settings>
                  <b:includable id='main'>
                    <b:include name='content'/>
                  </b:includable>
                  <b:includable id='content'>
                    <!--[ Scroll Menu ]-->
                    <nav class='navS scrlH'>
                      <div class='secIn'>
                        <ul>
                          <b:loop index='s' values='data:links' var='link'>
                            <b:if cond='data:s &lt;= 12'>
                              <li>
                                <b:tag class='l' expr:data-text='data:link.name' expr:name='data:link.target != &quot;#&quot; and data:link.target != data:blog.url.canonical ? &quot;a&quot; : &quot;span&quot;'>
                                  <b:attr cond='data:link.target != &quot;#&quot; and data:link.target != data:blog.url.canonical' expr:value='data:link.target' name='href'/>
                                  <b:attr cond='data:link.target != &quot;#&quot; and data:link.target != data:blog.url.canonical' expr:value='data:link.name' name='aria-label'/>
                                  <b:class cond='data:link.target == data:blog.url.canonical' name='a'/>
                                </b:tag>
                              </li>
                            </b:if>
                          </b:loop>
                        </ul>
                      </div>
                    </nav>
                  </b:includable>
                </b:widget>
                <b:widget id='HTML0' locked='true' title='Notification' type='HTML' version='2' visible='true'>
                  <b:widget-settings>
                    <b:widget-setting name='content'>&lt;!--[ Your content here, Lorem ipsum dolor sit amet, consectetur adipiscing elit. ]--&gt;

&lt;!--[ Alternatif content with button link ]--&gt;
&lt;div class=&#39;ntfA&#39;&gt; 
  &lt;span&gt;

Notification texts go here &lt;a href=&#39;/p/contact.html&#39; target=&#39;_blank&#39;&gt;Contact Us&lt;/a&gt;

  &lt;/span&gt; 
  &lt;a href=&#39;/p/purchase.html&#39; target=&#39;_blank&#39;&gt;Buy Now!&lt;/a&gt;
&lt;/div&gt;</b:widget-setting>
                  </b:widget-settings>
                  <b:includable id='main'>
                    <!--[ Notification Widget ]-->
                    <input checked='checked' class='ntfI hidden' id='forNft' type='checkbox'/>
                    <div class='ntfC'>
                      <div class='ntfT'><data:content/></div>
                      <label aria-label='Close Menu' class='c' for='forNft'/>
                    </div>
                    <script>/*<![CDATA[*/ let notIn = qSel("#forNft"); if (Pu.gC("NOTIF_CLOSE") == undefined) {notIn.checked = !1; notIn.addEventListener("change", function(){if (this.checked == !0){Pu.sC("NOTIF_CLOSE", 1 , {secure: !0, "max-age": 1800})} });}; /*]]>*/</script>
                  </b:includable>
                </b:widget>
              </b:section>
                
              <b:if cond='!data:view.isPreview'>
                <!--[ Ad content ]-->
                <b:tag class='blogAd' name='div'>
                  <b:section id='horizontal-ad' maxwidgets='3' showaddelement='true'>
                    <b:widget id='HTML91' locked='true' title='Under Header Ad' type='HTML' version='2' visible='true'>
                      <b:widget-settings>
                        <b:widget-setting name='content'/>
                      </b:widget-settings>
                      <b:includable id='main'>
                        <b:if cond='data:content != &quot;&quot;'>
                          <data:content/>
                          
                          <b:else/>
                          <!--[ Blank ad ]-->
                          <div class='adB' expr:data-text='data:messages.adsGoHere + &quot; (&quot; + data:title + &quot;)&quot;'/>
                        </b:if>
                      </b:includable>
                    </b:widget>
                  </b:section>
                </b:tag>
              </b:if>
              
              <b:tag class='blogM' name='div'>
                <!--[ Main content ]-->
                <main class='blogItm mainbar'>
                    
                  <b:if cond='data:view.isHomepage'>
                    <b:section aria-label='Gallery' class='sldO scrlH' id='slider-widget' maxwidgets='4' showaddelement='false'>
                      <b:widget id='Image1' locked='true' title='Slider Image 01' type='Image' version='2' visible='false'>
                        <b:widget-settings>
                          <b:widget-setting name='displayUrl'>https://blogger.googleusercontent.com/img/a/AVvXsEgF4A6PJyI_yombSlfL5wtS-ApMNF25tOYbpTG9tp-WClypBscTyONa1-IUa9LLNyAWOKjkPzcrcydOwVvXF7Y9qx4r4ysyL6qo5wz2QA8LWaOIpAI65c8C1nwoOA_3YKzqT8FHiAeD3JazSuIUK_nmT6MFsiERTbU0pD09WhoPJ71oK3KKKLAvXgV-yA=s1600</b:widget-setting>
                          <b:widget-setting name='displayHeight'>648</b:widget-setting>
                          <b:widget-setting name='sectionWidth'>379</b:widget-setting>
                          <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
                          <b:widget-setting name='displayWidth'>1600</b:widget-setting>
                          <b:widget-setting name='link'/>
                          <b:widget-setting name='caption'/>
                        </b:widget-settings>
                        <b:includable id='main'>
                          <b:include name='content'/>
                        </b:includable>
                        <b:includable id='content'>
                          <div class='sldC' expr:id='data:widget.instanceId + &quot;_img&quot;'>
                            <b:tag class='sldIm' expr:data-style='&quot;background-image: url(&quot; + data:sourceUrl + &quot;)&quot;' expr:name='data:link ? &quot;a&quot; : &quot;div&quot;'>
                              <b:class cond='data:view.url != data:view.url params { amp: &quot;1&quot; }' name='lazy'/>
                              <b:attr cond='data:view.url == data:view.url params { amp: &quot;1&quot; }' expr:value='&quot;background-image: url(&quot; + data:sourceUrl + &quot;)&quot;' name='style'/>
                              <b:attr cond='data:link' expr:value='data:link' name='href'/>
                              <b:attr cond='data:link' expr:value='data:title' name='aria-label'/>
                              <b:if cond='data:caption'><span class='sldT'><data:caption/></span></b:if>
                            </b:tag>
                            <noscript>
                              <b:tag class='sldIm' expr:name='data:link ? &quot;a&quot; : &quot;div&quot;' expr:style='&quot;background-image: url(&quot; + data:sourceUrl + &quot;)&quot;'>
                                <b:attr cond='data:link' expr:value='data:link' name='href'/>
                                <b:attr cond='data:link' expr:value='data:title' name='aria-label'/>
                                <b:if cond='data:caption'><span class='sldT'><data:caption/></span></b:if>
                              </b:tag>
                            </noscript>
                          </div>
                          <div class='sldS'/>
                        </b:includable>
                      </b:widget>
                      <b:widget id='Image2' locked='true' title='Slider Image 02' type='Image' version='2' visible='false'>
                        <b:widget-settings>
                          <b:widget-setting name='displayUrl'>https://blogger.googleusercontent.com/img/a/AVvXsEiUbPscESj3GrSMxIZhj93fe0cJhoYZpJ_GmuE79cWHjf-6Jh544LW8KTWui7h0UWzigWqEFOP8YleQXZzQrbUMA9AElSjcpyJPrRKBMjJrMKECxmYa4WxaLoK6urVvcfcv6urw9TasF4H596ZKK6MS3qajIzfnJyYJewb2eRjzasKNY4Xc-X4Sl7kLLg=s1600</b:widget-setting>
                          <b:widget-setting name='displayHeight'>648</b:widget-setting>
                          <b:widget-setting name='sectionWidth'>379</b:widget-setting>
                          <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
                          <b:widget-setting name='displayWidth'>1600</b:widget-setting>
                          <b:widget-setting name='link'>/2021/12/new-in-plus-ui.html</b:widget-setting>
                          <b:widget-setting name='caption'/>
                        </b:widget-settings>
                        <b:includable id='main'>
                          <b:include name='content'/>
                        </b:includable>
                        <b:includable id='content'>
                          <div class='sldC' expr:id='data:widget.instanceId + &quot;_img&quot;'>
                            <b:tag class='sldIm' expr:data-style='&quot;background-image: url(&quot; + data:sourceUrl + &quot;)&quot;' expr:name='data:link ? &quot;a&quot; : &quot;div&quot;'>
                              <b:class cond='data:view.url != data:view.url params { amp: &quot;1&quot; }' name='lazy'/>
                              <b:attr cond='data:view.url == data:view.url params { amp: &quot;1&quot; }' expr:value='&quot;background-image: url(&quot; + data:sourceUrl + &quot;)&quot;' name='style'/>
                              <b:attr cond='data:link' expr:value='data:link' name='href'/>
                              <b:attr cond='data:link' expr:value='data:title' name='aria-label'/>
                              <b:if cond='data:caption'><span class='sldT'><data:caption/></span></b:if>                                
                            </b:tag>
                            <noscript>
                              <b:tag class='sldIm' expr:name='data:link ? &quot;a&quot; : &quot;div&quot;' expr:style='&quot;background-image: url(&quot; + data:sourceUrl + &quot;)&quot;'>
                                <b:attr cond='data:link' expr:value='data:link' name='href'/>
                                <b:attr cond='data:link' expr:value='data:title' name='aria-label'/>
                                <b:if cond='data:caption'><span class='sldT'><data:caption/></span></b:if>
                              </b:tag>
                            </noscript>
                          </div>
                          <div class='sldS'/>
                        </b:includable>
                      </b:widget>
                      <b:widget id='Image3' locked='true' title='Slider Image 03' type='Image' version='2' visible='false'>
                        <b:widget-settings>
                          <b:widget-setting name='displayUrl'>https://1.bp.blogspot.com/-vK7BQxXeYnk/YF1Q9MVgZ8I/AAAAAAAAQls/OanP_Tl4sd4616Y1RaD2JPA_UOWtMkDAQCNcBGAsYHQ/s1600/slider-3-min.png</b:widget-setting>
                          <b:widget-setting name='displayHeight'>648</b:widget-setting>
                          <b:widget-setting name='sectionWidth'>286</b:widget-setting>
                          <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
                          <b:widget-setting name='displayWidth'>1600</b:widget-setting>
                          <b:widget-setting name='link'/>
                          <b:widget-setting name='caption'/>
                        </b:widget-settings>
                        <b:includable id='main'>
                          <b:include name='content'/>
                        </b:includable>
                        <b:includable id='content'>
                          <div class='sldC' expr:id='data:widget.instanceId + &quot;_img&quot;'>
                            <b:tag class='sldIm' expr:data-style='&quot;background-image: url(&quot; + data:sourceUrl + &quot;)&quot;' expr:name='data:link ? &quot;a&quot; : &quot;div&quot;'>
                              <b:class cond='data:view.url != data:view.url params { amp: &quot;1&quot; }' name='lazy'/>
                              <b:attr cond='data:view.url == data:view.url params { amp: &quot;1&quot; }' expr:value='&quot;background-image: url(&quot; + data:sourceUrl + &quot;)&quot;' name='style'/>
                              <b:attr cond='data:link' expr:value='data:link' name='href'/>
                              <b:attr cond='data:link' expr:value='data:title' name='aria-label'/>
                              <b:if cond='data:caption'><span class='sldT'><data:caption/></span></b:if>                                
                            </b:tag>
                            <noscript>
                              <b:tag class='sldIm' expr:name='data:link ? &quot;a&quot; : &quot;div&quot;' expr:style='&quot;background-image: url(&quot; + data:sourceUrl + &quot;)&quot;'>
                                <b:attr cond='data:link' expr:value='data:link' name='href'/>
                                <b:attr cond='data:link' expr:value='data:title' name='aria-label'/>
                                <b:if cond='data:caption'><span class='sldT'><data:caption/></span></b:if>
                              </b:tag>
                            </noscript>
                          </div>
                          <div class='sldS'/>
                        </b:includable>
                      </b:widget>
                      <b:widget id='Image4' locked='true' title='Slider Image 04' type='Image' version='2' visible='false'>
                        <b:widget-settings>
                          <b:widget-setting name='displayUrl'>https://1.bp.blogspot.com/-Q_BGkGuukLE/YF1Q67reH4I/AAAAAAAAQlk/jXe6LIyIjkkNpJu7ShtztoUWV4JylmCkgCNcBGAsYHQ/s1600/slider-4-min.png</b:widget-setting>
                          <b:widget-setting name='displayHeight'>648</b:widget-setting>
                          <b:widget-setting name='sectionWidth'>286</b:widget-setting>
                          <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
                          <b:widget-setting name='displayWidth'>1600</b:widget-setting>
                          <b:widget-setting name='link'/>
                          <b:widget-setting name='caption'/>
                        </b:widget-settings>
                        <b:includable id='main'>
                          <b:include name='content'/>
                        </b:includable>
                        <b:includable id='content'>
                          <div class='sldC' expr:id='data:widget.instanceId + &quot;_img&quot;'>
                            <b:tag class='sldIm' expr:data-style='&quot;background-image: url(&quot; + data:sourceUrl + &quot;)&quot;' expr:name='data:link ? &quot;a&quot; : &quot;div&quot;'>
                              <b:class cond='data:view.url != data:view.url params { amp: &quot;1&quot; }' name='lazy'/>
                              <b:attr cond='data:view.url == data:view.url params { amp: &quot;1&quot; }' expr:value='&quot;background-image: url(&quot; + data:sourceUrl + &quot;)&quot;' name='style'/>
                              <b:attr cond='data:link' expr:value='data:link' name='href'/>
                              <b:attr cond='data:link' expr:value='data:title' name='aria-label'/>
                              <b:if cond='data:caption'><span class='sldT'><data:caption/></span></b:if>                                
                            </b:tag>
                            <noscript>
                              <b:tag class='sldIm' expr:name='data:link ? &quot;a&quot; : &quot;div&quot;' expr:style='&quot;background-image: url(&quot; + data:sourceUrl + &quot;)&quot;'>
                                <b:attr cond='data:link' expr:value='data:link' name='href'/>
                                <b:attr cond='data:link' expr:value='data:title' name='aria-label'/>
                                <b:if cond='data:caption'><span class='sldT'><data:caption/></span></b:if>
                              </b:tag>
                            </noscript>
                          </div>
                          <div class='sldS'/>
                        </b:includable>
                      </b:widget>
                    </b:section>
                  </b:if>
                    
                  <b:section cond='data:view.isHomepage' id='top-widget' showaddelement='true'>
                    <b:widget cond='!data:view.isPreview' id='HTML92' locked='true' title='Ad Placement' type='HTML' version='2' visible='true'>
                      <b:widget-settings>
                        <b:widget-setting name='content'/>
                      </b:widget-settings>
                      <b:includable id='main'>
                        <div class='widget-content'>
                          <b:if cond='data:content != &quot;&quot;'>
                            <data:content/>
                          
                            <b:else/>
                            <!--[ Blank ad ]-->
                            <div class='adB' expr:data-text='data:messages.adsGoHere + &quot; (&quot; + data:title + &quot;)&quot;'/>
                          </b:if>
                        </div>
                      </b:includable>
                    </b:widget>
                    <b:widget id='FeaturedPost1' locked='true' title='Pinned Post' type='FeaturedPost' version='2' visible='true'>
                      <b:widget-settings>
                        <b:widget-setting name='showSnippet'>true</b:widget-setting>
                        <b:widget-setting name='showPostTitle'>true</b:widget-setting>
                        <b:widget-setting name='postId'>742365616331781427</b:widget-setting>
                        <b:widget-setting name='showFirstImage'>true</b:widget-setting>
                        <b:widget-setting name='useMostRecentPost'>true</b:widget-setting>
                      </b:widget-settings>
                      <b:includable id='main' var='this'>
                        <b:if cond='data:title != &quot;&quot;'>
                          <h2 class='title dt'><data:title/></h2>
                        </b:if>
                        <b:include name='snippetedPosts'/>
                      </b:includable>
                      <b:includable id='blogThisShare'/>
                      <b:includable id='bylineByName' var='byline'/>
                      <b:includable id='bylineRegion' var='regionItems'/>
                      <b:includable id='commentsLink'/>
                      <b:includable id='commentsLinkIframe'/>
                      <b:includable id='content'>
  <div class='post-summary'>
    <b:if cond='data:showPostTitle and data:postTitle != &quot;&quot;'>
      <h3><a expr:href='data:postUrl'><data:postTitle/></a></h3>
    </b:if>
    <b:if cond='data:showSnippet and data:postSummary != &quot;&quot;'>
      <p>
        <data:postSummary/>
      </p>
    </b:if>
    <b:if cond='data:showFirstImage and data:postFirstImage != &quot;&quot;'>
      <img class='image' expr:src='data:postFirstImage'/>
    </b:if>
  </div>

  <style type='text/css'>
    .image {
      width: 100%;
    }
  </style>
</b:includable>
                      <b:includable id='emailPostIcon'/>
                      <b:includable id='facebookShare'/>
                      <b:includable id='footerBylines'/>
                      <b:includable id='googlePlusShare'/>
                      <b:includable id='headerByline'/>
                      <b:includable id='linkShare'/>
                      <b:includable id='otherSharingButton'/>
                      <b:includable id='platformShare'/>
                      <b:includable id='postAuthor'/>
                      <b:includable id='postCommentsLink'/>
                      <b:includable id='postInfo'>
                        <div class='pInf pSml'>
                          <b:class cond='data:post.labels any (label =&gt; label.name in [ &quot;Sponsored&quot; ])' name='nSpr'/>
                          <b:if cond='data:post.labels none (label =&gt; label.name in [ &quot;Sponsored&quot; ])'>
                            <!--[ Post timestamp ]-->
                            <b:include cond='data:widgets.Blog.first.allBylineItems.timestamp' name='postTimestampPublish'/>
                          </b:if>
                    
                          <!--[ Post jumplinks ]-->
                          <b:include cond='data:widgets.Blog.first.allBylineItems.timestamp' name='postJumpLinks'/>
                        </div>
                      </b:includable>
                      <b:includable id='postJumpLink' var='post'/>
                      <b:includable id='postLabels'/>
                      <b:includable id='postLocation'/>
                      <b:includable id='postReactions'/>
                      <b:includable id='postShareButtons'/>
                      <b:includable id='postTimestamp'/>
                      <b:includable id='sharingButton'/>
                      <b:includable id='sharingButtonContent'/>
                      <b:includable id='sharingButtons'/>
                      <b:includable id='sharingButtonsMenu'/>
                      <b:includable id='sharingPlatformIcon'/>
                      <b:includable id='snippetedPostByline'/>
                      <b:includable id='snippetedPostContent'>
                        <!--[ Post Thumbnail ]-->
                        <b:include name='snippetedPostThumbnail'/>
                    
                        <div class='iCtnt'>
                          <!--[ Post header ]-->
                          <b:include name='postHeaders'/>
                          
                          <b:include cond='data:this.postDisplay.showTitle' name='snippetedPostTitle'/>
                          <b:include cond='data:this.postDisplay.showSnippet' name='snippetedPostEntry'/>
                      
                          <!--[ Post info ]-->
                          <b:include cond='!data:view.isPage and data:post.labels none (label =&gt; label.name in [ &quot;Product&quot; ])' name='postInfo'/>
                        </div>
                      </b:includable>
                      <b:includable id='snippetedPostEntry'>
                        <div class='pSnpt'>
                          <b:class cond='data:post.labels any (label =&gt; label.name in [ &quot;Product&quot; ])' name='nTag'/>
                          <b:include name='postEntrySnippet'/>
                        </div>
                      </b:includable>
                      <b:includable id='snippetedPostThumbnail'>
                        <div class='iThmb pThmb'>
                          <b:class cond='data:post.featuredImage.isYoutube' name='iyt'/>
                          <b:class cond='!data:this.postDisplay.showFeaturedImage or !data:post.featuredImage' name='nul'/>
                          <b:tag class='thmb' expr:name='data:this.postDisplay.showFeaturedImage and data:post.featuredImage ? &quot;a&quot; : &quot;div&quot;'>
                            <b:attr cond='data:this.postDisplay.showFeaturedImage and data:post.featuredImage' expr:value='data:post.url' name='href'/>
                            <b:if cond='data:this.postDisplay.showFeaturedImage and data:post.featuredImage'>
                              <b:include name='postEntryThumbnail'/>
                              <b:else/>
                              <span class='imgThm' data-text='No image'/>
                            </b:if>
                          </b:tag>
                          
                          <!--[ Comments count ]-->
                          <div class='iFxd'>
                            <b:if cond='data:post.allowComments and data:post.numberOfComments &gt; 0 or data:post.labels any (label =&gt; label.name in [ &quot;Sponsored&quot; , &quot;Product&quot; ])' name='div'>
                              <b:if cond='data:post.labels any (label =&gt; label.name in [ &quot;Sponsored&quot; ])'>
                                <b:include name='postSponsored'/>
                                <b:elseif cond='data:post.labels any (label =&gt; label.name in [ &quot;Product&quot; ])'/>
                                <b:include name='postProduct'/>
                              </b:if>
                              <b:if cond='data:widgets.Blog.first.allBylineItems.comments and data:post.labels none (label =&gt; label.name in [ &quot;Product&quot; ])'>
                                <b:include name='postCommentsLinks'/>
                              </b:if>
                            </b:if>
                            
                            <!--[ Post Views ]-->
                            <span class='pV hidden pu-views' expr:data-id='data:post.id'><b:include name='eye-icon'/></span>
                            
                            <!--[ Post Bookmark ]-->
                            <b:include name='thmb-bookmark'/>
                          </div>
                          
                          <div class='iFxd bl'>
                            <!--[ Post Author ]-->
                            <b:include name='thmb-athr'/>
                          </div>
                          
                          <!--[ Quick Edit ]-->
                          <b:include name='thmb-quickedit'/>
                        </div>
                      </b:includable>
                      <b:includable id='snippetedPostTitle'>
                        <h3 class='pTtl aTtl'><a expr:href='data:post.url.canonical'><data:post.title.escaped/></a></h3>
                      </b:includable>
                      <b:includable id='snippetedPosts'>
                        <div class='itemFt' role='feed'>
                          <!-- Don't render the post that we're currently already looking at. -->
                          <b:loop values='data:posts filter (p =&gt; p.id != data:view.postId)' var='post'>
                            <article class='itm'>
                              <b:class cond='data:this.postDisplay.showFeaturedImage and !data:post.featuredImage' name='noThmb'/>
                              <b:class cond='!data:this.postDisplay.showFeaturedImage' name='noImage'/>
                              <b:class cond='data:post.labels any (label =&gt; label.name in [ &quot;Product&quot; ])' name='pTag'/>
                              <b:include name='snippetedPostContent'/>
                            </article>
                          </b:loop>
                        </div>
                      </b:includable>
                    </b:widget>
                  </b:section>
                
                  <b:section id='main-widget' showaddelement='true'>
                    <b:widget cond='!data:view.isPreview and !data:blog.isMobileRequest and (data:view.isHomepage or data:view.isPost)' id='HTML93' locked='true' title='Ad Placement - Desktop only' type='HTML' version='2' visible='true'>
                      <b:widget-settings>
                        <b:widget-setting name='content'/>
                      </b:widget-settings>
                      <b:includable id='main'>
                        <div class='widget-content'>
                          <b:if cond='data:content != &quot;&quot;'>
                            <data:content/>
                          
                            <b:else/>
                            <!--[ Blank ad ]-->
                            <div class='adB' expr:data-text='data:messages.adsGoHere + &quot; (&quot; + data:title + &quot;)&quot;'/>
                          </b:if>
                        </div>
                      </b:includable>
                    </b:widget>
                    <b:widget id='Blog1' locked='true' title='Blog Posts' type='Blog' version='2' visible='true'>
                      <b:widget-settings>
                        <b:widget-setting name='showDateHeader'>false</b:widget-setting>
                        <b:widget-setting name='commentLabel'>Comment</b:widget-setting>
                        <b:widget-setting name='style.textcolor'>#1e1e1e</b:widget-setting>
                        <b:widget-setting name='showShareButtons'>true</b:widget-setting>
                        <b:widget-setting name='authorLabel'>Published by</b:widget-setting>
                        <b:widget-setting name='showCommentLink'>true</b:widget-setting>
                        <b:widget-setting name='style.urlcolor'>#08102b</b:widget-setting>
                        <b:widget-setting name='showAuthor'>true</b:widget-setting>
                        <b:widget-setting name='style.linkcolor'>#482dff</b:widget-setting>
                        <b:widget-setting name='style.unittype'>TextAndImage</b:widget-setting>
                        <b:widget-setting name='style.bgcolor'>#ffffff</b:widget-setting>
                        <b:widget-setting name='timestampLabel'>On</b:widget-setting>
                        <b:widget-setting name='reactionsLabel'/>
                        <b:widget-setting name='showAuthorProfile'>true</b:widget-setting>
                        <b:widget-setting name='style.layout'>1x1</b:widget-setting>
                        <b:widget-setting name='showLabels'>true</b:widget-setting>
                        <b:widget-setting name='showLocation'>false</b:widget-setting>
                        <b:widget-setting name='postLabelsLabel'>in</b:widget-setting>
                        <b:widget-setting name='showTimestamp'>true</b:widget-setting>
                        <b:widget-setting name='postsPerAd'>1</b:widget-setting>
                        <b:widget-setting name='showBacklinks'>false</b:widget-setting>
                        <b:widget-setting name='style.bordercolor'>#ffffff</b:widget-setting>
                        <b:widget-setting name='showInlineAds'>false</b:widget-setting>
                        <b:widget-setting name='showReactions'>false</b:widget-setting>
                      </b:widget-settings>
                      <b:includable id='main' var='this'>
                          
                        <b:include cond='data:view.isMultipleItems' name='titlePost'/>
                  
                        <div class='blogPts'>
                          <b:class cond='data:posts.empty' name='mty'/>
                  
                          <!--[ Fullpage condition ]-->
                          <b:class cond='data:view.isSingleItem and data:posts any (p =&gt; p.labels any (l =&gt; l.name in [ &quot;Fullpage&quot; , &quot;Label_2&quot; ]))' name='fullP'/>
                          <b:include cond='data:view.isSingleItem and data:posts any (p =&gt; p.labels any (l =&gt; l.name in [ &quot;Fullpage&quot; , &quot;Label_2&quot; ]))' name='post-singlePage'/>
                      
                          <!--[ If no content found ]-->
                          <b:include name='noContentPlaceholder'/>
                      
                          <b:with value='data:widgets.FeaturedPost filter (w =&gt; w.sectionId == &quot;top-widget&quot;) map (w =&gt; w.postId)' var='featuredPostIds'>
                            <b:with value='data:view.isHomepage ? data:posts filter (post =&gt; post.id not in data:featuredPostIds) : data:posts' var='posts'>
                            
                              <b:loop index='i' values='data:posts' var='post'>
                                <!--[ Remove comment tag below to enable in-feed ad ]-->
                                <!--<b:if cond='data:i == 3'>
                                  <b:include name='post-adIn'/>                                
                                </b:if>
                                <b:if cond='data:i == 6'>
                                  <b:include name='post-adIn'/>                                
                                </b:if>-->
                                <b:include data='post' name='postCommentsAndAd'/>
                              </b:loop>
                          
                            </b:with>
                          </b:with>
                        </div>
                  
                        <b:include cond='data:view.isMultipleItems and !data:posts.empty' name='postPagination'/>
                      </b:includable>
                      <b:includable id='aboutPostAuthor'/>
                      <b:includable id='addComments'/>
                      <b:includable id='backlinkDeleteIcon' var='backlink'/>
                      <b:includable id='backlinks' var='post'/>
                      <b:includable id='blogThisShare'/>
                      <b:includable id='breadcrumb' var='post'>
                        <!--[ Post breadcrumbs ]-->
                        <div class='brdCmb' itemscope='itemscope' itemtype='https://schema.org/BreadcrumbList'>
                          <b:class cond='data:post.labels any (label =&gt; label.name in [ &quot;Sponsored&quot; ])' name='sponsored'/>
                          <div class='hm' itemprop='itemListElement' itemscope='itemscope' itemtype='https://schema.org/ListItem'>
                            <a expr:href='data:blog.homepageUrl.canonical' itemprop='item'><span itemprop='name'><data:messages.home/></span></a>
                            <meta content='1' itemprop='position'/>
                          </div>
                          <b:if cond='data:post.labels'>
                            <b:loop index='num' values='data:post.labels' var='label'>
                              <b:if cond='data:num == 0'>
                                <div class='lb' itemprop='itemListElement' itemscope='itemscope' itemtype='https://schema.org/ListItem'>
                                  <a expr:href='data:label.url.canonical' itemprop='item'><span itemprop='name'><data:label.name/></span></a>
                                  <meta expr:content='data:num+2' itemprop='position'/>
                                </div>
                              </b:if>
                              <b:if cond='data:num == 1 and data:post.labels none (label =&gt; label.name in [ &quot;Sponsored&quot; ])'>
                                <div class='lb' itemprop='itemListElement' itemscope='itemscope' itemtype='https://schema.org/ListItem'>
                                  <a expr:href='data:label.url.canonical' itemprop='item'><span itemprop='name'><data:label.name/></span></a>
                                  <meta expr:content='data:num+2' itemprop='position'/>
                                </div>
                              </b:if>
                            </b:loop>
                            <b:elseif cond='data:view.isPost'/>
                            <div class='lb nob'><data:messages.posts/></div>
                          </b:if>
                            
                          <!--[ Change cond='data:view.isPage' to cond='data:view.isSingleItem' if you want to show post title on breadcrumb ]-->
                          <b:tag class='tl' cond='data:view.isPage' expr:data-text='data:post.title' name='div'/>
                        </div>
                      </b:includable>
                      <b:includable id='bylineByName' var='byline'/>
                      <b:includable id='bylineRegion' var='regionItms'/>
                      <b:includable id='comment-form' var='post'>
  <div class='comment-form'>
    <a name='comment-form'/>
    <b:if cond='data:mobile'>
      <h4 id='comment-post-message'>
        <a expr:id='data:widget.instanceId + &quot;_comment-editor-toggle-link&quot;' href='javascript:void(0)'><data:postCommentMsg/></a></h4>
      <p><data:blogCommentMessage/></p>
      <data:blogTeamBlogMessage/>
      <a expr:href='data:post.commentFormIframeSrc' id='comment-editor-src'/>
      <iframe allowtransparency='true' class='blogger-iframe-colorize blogger-comment-from-post' expr:height='data:cmtIframeInitialHeight' frameborder='0' id='comment-editor' name='comment-editor' src='' style='display: none' width='100%'/>
    <b:else/>
      <h4 id='comment-post-message'><data:postCommentMsg/></h4>
      <p><data:blogCommentMessage/></p>
      <data:blogTeamBlogMessage/>
      <a expr:href='data:post.commentFormIframeSrc' id='comment-editor-src'/>
      <iframe allowtransparency='true' class='blogger-iframe-colorize blogger-comment-from-post' expr:height='data:cmtIframeInitialHeight' frameborder='0' id='comment-editor' name='comment-editor' src='' width='100%'/>
    </b:if>
    <data:post.cmtfpIframe/>
    <script type='text/javascript'>
      BLOG_CMT_createIframe(&#39;<data:post.appRpcRelayPath/>&#39;);
    </script>
  </div>
</b:includable>
                      <b:includable id='commentAuthorAvatar'/>
                      <b:includable id='commentDeleteIcon' var='comment'/>
                      <b:includable id='commentForm' var='post'/>
                      <b:includable id='commentFormIframeSrc' var='post'/>
                      <b:includable id='commentItem' var='comment'/>
                      <b:includable id='commentList' var='comments'/>
                      <b:includable id='commentPicker' var='post'/>
                      <b:includable id='comment_count_picker' var='post'>
  <a class='comment-link' expr:href='data:post.addCommentUrl' expr:onclick='data:post.addCommentOnclick'>
    <data:post.commentLabelFull/>:
  </a>
</b:includable>
                      <b:includable id='comment_picker' var='post'>
  <b:if cond='data:post.showThreadedComments'>
    <b:include data='post' name='threaded_comments'/>
  <b:else/>
    <b:include data='post' name='comments'/>
  </b:if>
</b:includable>
                      <b:includable id='commentblock' var='cb'>
                        <div class='cmAv'>
                          <b:if cond='data:cb.level.authorAvatarSrc'>
                            <div class='im' expr:data-style='&quot;background-image: url(&quot; + resizeImage(data:cb.level.authorAvatarSrc, 35, &quot;1:1&quot;) + &quot;)&quot;'>
                              <b:class cond='data:view.url != data:view.url params { amp: &quot;1&quot; }' name='lazy'/>
                              <b:attr cond='data:view.url == data:view.url params { amp: &quot;1&quot; }' expr:value='&quot;background-image: url(&quot; + resizeImage(data:cb.level.authorAvatarSrc, 35, &quot;1:1&quot;) + &quot;)&quot;' name='style'/>
                            </div>
                          </b:if>
                        </div>
                  
                        &lt;div class=&#39;cmIn&#39;&gt;
                        <div class='cmBd'>
                          <b:class cond='data:cb.level.isDeleted' name='del'/>
                          <b:attr cond='!data:cb.level.isDeleted' name='itemscope' value='itemscope'/>
                          <b:attr cond='!data:cb.level.isDeleted' name='itemtype' value='https://schema.org/Comment'/>
                          <div class='cmHr'>
                            <b:class cond='data:post.author.name == data:cb.level.author' name='a'/>
                            <span class='n'>
                              <b:attr cond='!data:cb.level.isDeleted' name='itemprop' value='author'/>
                              <b:attr cond='!data:cb.level.isDeleted' name='itemscope' value='itemscope'/>
                              <b:attr cond='!data:cb.level.isDeleted' name='itemtype' value='https://schema.org/Person'/>
                              <b:if cond='data:cb.level.author != &quot;Anonymous&quot;'>
                                <bdi><b:attr cond='!data:cb.level.isDeleted' name='itemprop' value='name'/><data:cb.level.author/></bdi>
                                <b:else/>
                                <bdi><b:attr cond='!data:cb.level.isDeleted' name='itemprop' value='name'/>Anonymous</bdi>
                              </b:if>
                            </span>
                    
                            <b:if cond='!data:cb.level.isDeleted'>
                              <!--[ Remove dtTm in className to disable Second Ago script ]-->
                              <b:if cond='data:blog.languageDirection != &quot;rtl&quot;'>
                                <span class='d dtTm' expr:data-datetime='data:cb.level.timestamp'/>
                              </b:if>
                              <span class='d date' expr:data-datetime='data:cb.level.timestamp' itemprop='datePublished'><data:cb.level.timestamp/></span>
                            </b:if>
                          </div>

                          <div class='cmCo'>
                            <b:class cond='!data:post.allowNewComments' name='dis'/>
                            <b:attr cond='!data:cb.level.isDeleted' name='itemprop' value='text'/>
                            <b:if cond='data:cb.level.author != data:post.author.name'>
                              <b:eval expr='data:cb.level.body snippet { links: false }'/>
                              <b:else/>
                              <data:cb.level.body/>
                            </b:if>
                          </div>
                    
                        </div>
                      </b:includable>
                      <b:includable id='comments' var='post'/>
                      <b:includable id='commentsLink'/>
                      <b:includable id='commentsLinkIframe'/>
                      <b:includable id='commentsTitle'>
                        <h3 class='title'>
                          <b:if cond='data:post.numberOfComments &gt; 0'>
                            <b:message name='messages.numberOfComments'>
                              <b:param expr:value='data:post.numberOfComments' name='numComments'/>
                            </b:message>
                            <b:else/>
                            <data:messages.postAComment/>
                          </b:if>
                        </h3>
                      </b:includable>
                      <b:includable id='defaultAdUnit'>
                        <ins class='adsbygoogle' data-ad-format='auto' expr:data-ad-client='data:adClientId ?: data:blog.adsenseClientId' expr:data-ad-host='data:blog.adsenseHostId' expr:data-analytics-uacct='data:blog.analyticsAccountNumber' expr:style='data:style ?: &quot;display: block;&quot;'/>
                        <script>(adsbygoogle = window.adsbygoogle || []).push({});</script>
                      </b:includable>
                      <b:includable id='emailPostIcon'/>
                      <b:includable id='facebookShare'/>
                      <b:includable id='feedLinks'/>
                      <b:includable id='feedLinksBody' var='links'/>
                      <b:includable id='footerBylines'/>
                      <b:includable id='googlePlusShare'/>
                      <b:includable id='headerByline'/>
                      <b:includable id='homePageLink'/>
                      <b:includable id='iframeComments' var='post'/>
                      <b:includable id='iframe_comments' var='post'>
  <!-- G+ comments, no longer available. The includable is retained for backwards-compatibility. -->
</b:includable>
                      <b:includable id='inlineAd' var='post'>
                        <b:if cond='!data:view.isPreview'>
                          <b:if cond='data:this.adCode or data:this.adClientId or data:blog.adsenseClientId'>
                            <!-- Ad -->
                            <div class='inline-ad'>
                              <b:if cond='data:this.adCode != &quot;&quot;'>
                                <data:this.adCode/>
                                <b:else/>
                                <b:include cond='data:this.adClientId or data:blog.adsenseClientId' name='defaultAdUnit'/>
                              </b:if>
                            </div>
                          </b:if>
                          <b:else/>
                          <div class='inline-ad'>
                            <div class='inline-ad-placeholder'>
                              <span><b:message name='data:messages.adsGoHere + &quot; (&quot; + data:title + &quot;)&quot;'/></span>
                            </div>
                          </div>
                        </b:if>
                      </b:includable>
                      <b:includable id='linkShare'/>
                      <b:includable id='manageComments'/>
                      <b:includable id='mobile-index-post' var='post'>
  <div class='mobile-date-outer date-outer'>
    <b:if cond='data:post.dateHeader'>
      <div class='date-header'>
        <span><data:post.dateHeader/></span>
      </div>
    </b:if>

    <div class='mobile-post-outer'>
      <a expr:href='data:post.url'>
        <h3 class='mobile-index-title entry-title' itemprop='name'>
          <data:post.title/>
        </h3>

        <div class='mobile-index-arrow'>&amp;rsaquo;</div>

        <div class='mobile-index-contents'>
          <b:if cond='data:post.thumbnailUrl'>
            <div class='mobile-index-thumbnail'>
              <div class='Image'>
                <img expr:src='data:post.thumbnailUrl'/>
              </div>
            </div>
          </b:if>

          <div class='post-body'>
            <b:if cond='data:post.snippet'><data:post.snippet/></b:if>
          </div>
        </div>

        <div style='clear: both;'/>
      </a>

      <div class='mobile-index-comment'>
        <b:include cond='data:blog.pageType != &quot;static_page&quot;                          and data:post.allowComments                          and data:post.numComments != 0' data='post' name='comment_count_picker'/>
      </div>
    </div>
  </div>
</b:includable>
                      <b:includable id='mobile-main' var='top'>
    <!-- posts -->
    <div class='blog-posts hfeed'>

      <b:include data='top' name='status-message'/>

      <b:if cond='data:blog.pageType == &quot;index&quot;'>
        <b:loop values='data:posts' var='post'>
          <b:include data='post' name='mobile-index-post'/>
        </b:loop>
      <b:else/>
        <b:loop values='data:posts' var='post'>
          <b:include data='post' name='mobile-post'/>
        </b:loop>
      </b:if>
    </div>

   <b:include name='mobile-nextprev'/>
</b:includable>
                      <b:includable id='mobile-nextprev'>
  <div class='blog-pager' id='blog-pager'>
    <b:if cond='data:newerPageUrl'>
      <div class='mobile-link-button' id='blog-pager-newer-link'>
      <a class='blog-pager-newer-link' expr:href='data:newerPageUrl' expr:id='data:widget.instanceId + &quot;_blog-pager-newer-link&quot;' expr:title='data:newerPageTitle'>&amp;lsaquo;</a>
      </div>
    </b:if>

    <b:if cond='data:olderPageUrl'>
      <div class='mobile-link-button' id='blog-pager-older-link'>
      <a class='blog-pager-older-link' expr:href='data:olderPageUrl' expr:id='data:widget.instanceId + &quot;_blog-pager-older-link&quot;' expr:title='data:olderPageTitle'>&amp;rsaquo;</a>
      </div>
    </b:if>

    <div class='mobile-link-button' id='blog-pager-home-link'>
    <a class='home-link' expr:href='data:blog.homepageUrl'><data:homeMsg/></a>
    </div>

    <div class='mobile-desktop-link'>
      <a class='home-link' expr:href='data:desktopLinkUrl'><data:desktopLinkMsg/></a>
    </div>

  </div>
  <div class='clear'/>
</b:includable>
                      <b:includable id='mobile-post' var='post'>
  <div class='date-outer'>
    <b:if cond='data:post.dateHeader'>
      <h2 class='date-header'><span><data:post.dateHeader/></span></h2>
    </b:if>
    <div class='date-posts'>
      <div class='post-outer'>

        <div class='post hentry uncustomized-post-template' itemscope='itemscope' itemtype='http://schema.org/BlogPosting'>
          <b:if cond='data:post.thumbnailUrl'>
            <meta expr:content='data:post.thumbnailUrl' itemprop='image_url'/>
          </b:if>
          <meta expr:content='data:blog.blogId' itemprop='blogId'/>
          <meta expr:content='data:post.id' itemprop='postId'/>

          <a expr:name='data:post.id'/>
          <b:if cond='data:post.title'>
            <h3 class='post-title entry-title' itemprop='name'>
              <b:if cond='data:post.link'>
                <a expr:href='data:post.link'><data:post.title/></a>
              <b:elseif cond='data:post.url and data:blog.url != data:post.url'/>
                <a expr:href='data:post.url'><data:post.title/></a>
              <b:else/>
                <data:post.title/>
              </b:if>
            </h3>
          </b:if>

          <div class='post-header'>
            <div class='post-header-line-1'/>
          </div>

          <div class='post-body entry-content' expr:id='&quot;post-body-&quot; + data:post.id' itemprop='articleBody'>
            <data:post.body/>
            <div style='clear: both;'/> <!-- clear for photos floats -->
          </div>

          <div class='post-footer'>
            <div class='post-footer-line post-footer-line-1'>
              <span class='post-author vcard'>
                <b:if cond='data:top.showAuthor'>
                  <b:if cond='data:post.authorProfileUrl'>
                    <span class='fn' itemprop='author' itemscope='itemscope' itemtype='http://schema.org/Person'>
                      <meta expr:content='data:post.authorProfileUrl' itemprop='url'/>
                      <a expr:href='data:post.authorProfileUrl' rel='author' title='author profile'>
                        <span itemprop='name'><data:post.author/></span>
                      </a>
                    </span>
                  <b:else/>
                    <span class='fn' itemprop='author' itemscope='itemscope' itemtype='http://schema.org/Person'>
                      <span itemprop='name'><data:post.author/></span>
                    </span>
                  </b:if>
                </b:if>
              </span>

              <span class='post-timestamp'>
                <b:if cond='data:top.showTimestamp'>
                  <data:top.timestampLabel/>
                  <b:if cond='data:post.url'>
                    <meta expr:content='data:post.url.canonical' itemprop='url'/>
                    <a class='timestamp-link' expr:href='data:post.url' rel='bookmark' title='permanent link'><abbr class='published' expr:title='data:post.timestampISO8601' itemprop='datePublished'><data:post.timestamp/></abbr></a>
                  </b:if>
                </b:if>
              </span>

              <span class='post-comment-link'>
                <b:include cond='data:blog.pageType not in {&quot;item&quot;,&quot;static_page&quot;}                                  and data:post.allowComments' data='post' name='comment_count_picker'/>
              </span>
            </div>

            <div class='post-footer-line post-footer-line-2'>
              <b:if cond='data:top.showMobileShare'>
                <div class='mobile-link-button goog-inline-block' id='mobile-share-button'>
                  <a href='javascript:void(0);'><data:shareMsg/></a>
                </div>
              </b:if>
            </div>

          </div>
        </div>

        <b:include cond='data:blog.pageType in {&quot;static_page&quot;,&quot;item&quot;}' data='post' name='comment_picker'/>
      </div>
    </div>
  </div>
</b:includable>
                      <b:includable id='nextPageLink'/>
                      <b:includable id='nextprev'>
  <div class='blog-pager' id='blog-pager'>
    <b:if cond='data:newerPageUrl'>
      <span id='blog-pager-newer-link'>
      <a class='blog-pager-newer-link' expr:href='data:newerPageUrl' expr:id='data:widget.instanceId + &quot;_blog-pager-newer-link&quot;' expr:title='data:newerPageTitle'><data:newerPageTitle/></a>
      </span>
    </b:if>

    <b:if cond='data:olderPageUrl'>
      <span id='blog-pager-older-link'>
      <a class='blog-pager-older-link' expr:href='data:olderPageUrl' expr:id='data:widget.instanceId + &quot;_blog-pager-older-link&quot;' expr:title='data:olderPageTitle'><data:olderPageTitle/></a>
      </span>
    </b:if>

    <a class='home-link' expr:href='data:blog.homepageUrl'><data:homeMsg/></a>

    <b:if cond='data:mobileLinkUrl'>
      <div class='blog-mobile-link'>
        <a expr:href='data:mobileLinkUrl'><data:mobileLinkMsg/></a>
      </div>
    </b:if>

  </div>
  <div class='clear'/>
</b:includable>
                      <b:includable id='noContentPlaceholder'>
                        <b:if cond='data:posts.empty'>
                          <!--[ No posts messages ]-->
                          <div class='noPosts'>
                            <span><data:messages.noResultsFound/>...</span>
                          </div>
                        </b:if>
                      </b:includable>
                      <b:includable id='otherSharingButton'/>
                      <b:includable id='platformShare'/>
                      <b:includable id='post' var='post'/>
                      <b:includable id='postAuthor'/>
                      <b:includable id='postBody' var='post'/>
                      <b:includable id='postBodySnippet' var='post'/>
                      <b:includable id='postCommentsAndAd' var='post'>
                    
                        <!--[ Post article ]-->
                        <article class='ntry'>
                          <b:class cond='data:view.isSingleItem' name='ps'/>
                          <b:class cond='data:view.isSingleItem' name='post'/>
                          <b:class cond='data:view.isMultipleItems and !data:post.featuredImage' name='noThmb'/>
                          <b:class cond='!data:widgets.Blog.first.allBylineItems.timestamp' name='noInf'/>
                          <b:class cond='data:post.labels any (label =&gt; label.name in [ &quot;Sponsored&quot; ])' name='noAd'/>
                          <b:class cond='data:post.labels any (label =&gt; label.name in [ &quot;Product&quot; ])' name='pTag'/>
                          
                          <b:if cond='data:view.isPost and !data:view.isPreview'>
                            <div class='pGV'>
                              <!--[ Greetings ]-->
                              <span class='pGrt' data-text='Welcome!' id='greetings'><b:include name='smile-icon'/></span>
                              
                              <!--[ Page Views ]-->
                              <span class='pVws hidden pu-views' data-add='true' expr:data-id='data:post.id'><b:include name='eye-icon'/></span>
                            </div>
                           <script>/*<![CDATA[*/ var welElem = document.querySelector("#greetings"); var curHr = new Date().getHours(); var welMes = ["Have a Sweet Dreams!", "Good Morning!", "Good Afternoon!", "Good Evening!", "Good Night!", "It's time to sleep!"]; let welText = ""; if (curHr < 4) welText = welMes[0]; else if (curHr < 12) welText = welMes[1]; else if (curHr < 17) welText = welMes[2]; else if (curHr < 19) welText = welMes[3]; else if (curHr < 22) welText = welMes[4]; else welText = welMes[5]; welElem.setAttribute('data-text', welText); /*]]>*/</script>
                          </b:if>
                    
                          <!--[ Post brdCmb ]-->
                          <b:include cond='data:view.isSingleItem' data='post' name='breadcrumb'/>
                        
                          <b:if cond='data:view.isMultipleItems'>
                            <!--[ Post thumbnail and labels ]-->
                            <div class='pThmb'>
                              <b:class cond='data:post.featuredImage.isYoutube' name='iyt'/>
                              <b:class cond='!data:post.featuredImage' name='nul'/>
                              <b:tag class='thmb' expr:name='data:post.featuredImage ? &quot;a&quot; : &quot;div&quot;'>
                                <b:attr cond='data:post.featuredImage' expr:value='data:post.url' name='href'/>
                                <b:if cond='data:post.featuredImage'>
                                  <b:include name='postEntryThumbnail'/>
                                  <b:else/>
                                  <span class='imgThm' data-text='No image'/>
                                </b:if>
                              </b:tag>
                                
                              <!--[ Comments count ]-->
                              <div class='iFxd'>
                                <b:if cond='data:post.allowComments and data:post.numberOfComments &gt; 0 or data:post.labels any (label =&gt; label.name in [ &quot;Sponsored&quot; , &quot;Product&quot; ])'>
                                  <b:if cond='data:post.labels any (label =&gt; label.name in [ &quot;Sponsored&quot; ])'>
                                    <b:include name='postSponsored'/>
                                    <b:elseif cond='data:post.labels any (label =&gt; label.name in [ &quot;Product&quot; ])'/>
                                    <b:include name='postProduct'/>
                                  </b:if>
                                  <b:if cond='data:widgets.Blog.first.allBylineItems.comments and data:post.labels none (label =&gt; label.name in [ &quot;Product&quot; ])'>
                                    <b:include name='postCommentsLinks'/>
                                  </b:if>
                                </b:if>
                                
                                <!--[ Post Views ]-->
                                <span class='pV hidden pu-views' expr:data-id='data:post.id'><b:include name='eye-icon'/></span>
                                
                                <!--[ Post Bookmark ]-->
                                <b:include name='thmb-bookmark'/>
                              </div>
                              
                              <div class='iFxd bl'>
                                <!--[ Post Author ]-->
                                <b:include name='thmb-athr'/>
                              </div>
                          
                              <!--[ Quick Edit ]-->
                              <b:include name='thmb-quickedit'/>
                            </div>
                          </b:if>
                    
                          <b:tag class='pCntn' cond='data:view.isMultipleItems' name='div'>
                              
                            <!--[ Post header ]-->
                            <b:include cond='data:view.isMultipleItems' name='postHeaders'/>
                            
                            <!--[ Post title ]-->
                            <b:tag class='pTtl aTtl sml' expr:name='data:post.link or (data:post.url and data:view.url != data:post.url) ? &quot;h2&quot; : &quot;h1&quot;'>
                              <b:class cond='data:view.isSingleItem' name='itm'/>
                              <b:class cond='data:post.labels any (label =&gt; label.name in [ &quot;Sponsored&quot; ])' name='nSpr'/>
                              <b:tag expr:name='data:post.link or (data:post.url and data:view.url != data:post.url) ? &quot;a&quot; : &quot;span&quot;'>
                                <b:attr cond='data:post.link or (data:post.url and data:view.url != data:post.url)' expr:value='data:post.url.canonical' name='href'/>
                                <b:attr cond='data:post.link or (data:post.url and data:view.url != data:post.url)' expr:value='data:post.title' name='data-text'/>
                                <b:attr cond='data:post.link or (data:post.url and data:view.url != data:post.url)' name='rel' value='bookmark'/>
                                <data:post.title/>
                              </b:tag>
                            </b:tag>
                              
                            <b:if cond='data:blog.metaDescription and data:view.isPost'>
                              <!-- Post Description -->
                              <div class='pDesc'><data:blog.metaDescription/></div>
                            </b:if>
                          
                            <b:if cond='data:view.isMultipleItems'>
                              <!--[ Post snippets ]-->
                              <div class='pSnpt'>
                                <b:class cond='data:post.labels any (label =&gt; label.name in [ &quot;Product&quot; ])' name='nTag'/>
                                <b:include name='postEntrySnippet'/>
                              </div>
                            </b:if>
                        
                            <!--[ Post info ]-->
                            <b:include cond='!data:view.isPage' name='postInfo'/>
                        
                            <b:tag class='pInr' cond='data:view.isSingleItem' name='div'>
                              <b:if cond='data:post.labels none (label =&gt; label.name in [ &quot;Sponsored&quot; ])'>
                                <!--[ Post top ad ]-->
                                <b:include cond='data:view.isPost and !data:view.isPreview' data='post' name='post-adTop'/>
                              </b:if>
                          
                              <b:if cond='data:view.isSingleItem'>
                                <!--[ Post body ]-->
                                <div class='pEnt' expr:id='&quot;postID-&quot; + data:post.id'>
                                  <div class='pS post-body postBody' id='postBody'>
                                    <data:post.body/>
                                    <!--[ Previous / Next Chapter ]-->
                                    <div id='aChp'/>
                                  </div>
                                </div>
                                
                                <!--[ In-Post Quick Edit ]-->
                                <b:include cond='!data:view.isPreview' name='quick-edit'/>
                              </b:if>
                              
                              <b:if cond='data:post.labels none (label =&gt; label.name in [ &quot;Sponsored&quot; ])'>
                                <!--[ Post bottom ad ]-->
                                <b:include cond='data:view.isPost and !data:view.isPreview' data='post' name='post-adBot'/>
                              </b:if>
                      
                              <b:include data='post' name='postMetadataJSON'/>
                              
                              <b:if cond='data:view.isPost and data:widgets.Blog.first.allBylineItems.timestamp and data:post.labels none (label =&gt; label.name in [ &quot;Product&quot; ])'>
                                <script>/*<![CDATA[*/ function get_text(el) {ret = ''; var length = el.childNodes.length; for(var i = 0; i < length; i++) {var node = el.childNodes[i]; if(node.nodeType != 8) {ret += node.nodeType != 1 ? node.nodeValue : get_text(node);} } return ret;} var words = get_text(document.getElementById('postBody')); var count = words.split(' ').length; var avg = 200; var counted = count / avg; var maincount = Math.round(counted); document.getElementById('rdTime').innerHTML = 'Estimated read time: ' + maincount + ' min'; /*]]>*/</script>
                              </b:if>
                              
                              <b:if cond='data:post.labels none (label =&gt; label.name in [ &quot;Sponsored&quot; , &quot;Product&quot; ])'>
                                <!--[ Post writter ]-->
                                <b:include cond='data:post.author.aboutMe and data:view.isPost and !data:view.isPreview' name='post-authorProfile'/>
                              </b:if>
                          
                              <!--[ Share button ]-->
                              <b:include cond='data:post.shareUrl and data:view.isPost and !data:view.isPreview' name='postInfoShare'/>
                            </b:tag>
                          
                          </b:tag>
                        </article>
                  
                        <!--[ Post footer ]-->
                        <b:tag class='pFoot' cond='data:view.isPost' name='div'>
                          <!--[ Share button pop-up/sticky ]-->
                          <b:include cond='data:post.shareUrl and data:view.isPost and !data:view.isPreview' name='postInfoShareMore'/>
                            
                          <!--[ Related post ]-->
                          <b:include cond='data:view.isPost and !data:view.isPreview and !data:blog.isPrivateBlog' data='post' name='post-related'/>
                    
                          <!--[ Matched content ad ]-->
                          <!--<b:include cond='data:view.isPost and !data:view.isPreview' data='post' name='post-relatedAd'/>-->
                    
                          <b:tag class='pCmnts' cond='data:post.allowComments and data:view.isPost and !data:view.isPreview' id='comment' name='div'>
                            <!--[ Blogger Comments ]-->
                            <b:include cond='data:post.allowComments and data:view.isPost and !data:view.isPreview' data='post' name='threadedComments-modifV3'/>
                          </b:tag>
                        </b:tag>
                        
                        <!--[ Show ad. ]-->
                        <b:include cond='data:post.includeAd' data='post' name='inlineAd'/>
                      </b:includable>
                      <b:includable id='postCommentsLink'/>
                      <b:includable id='postFooter' var='post'/>
                      <b:includable id='postFooterAuthorProfile' var='post'/>
                      <b:includable id='postHeader'/>
                      <b:includable id='postInfo'>
                        <b:tag class='pInf pSml' name='div'>
                          <b:class cond='data:view.isSingleItem' name='ps'/>
                          <b:class cond='data:post.labels any (label =&gt; label.name in [ &quot;Sponsored&quot; ])' name='nSpr'/>
                          <b:class cond='!data:widgets.Blog.first.allBylineItems.timestamp' name='nTm'/>
                          <b:class cond='!data:widgets.Blog.first.allBylineItems.author and !data:widgets.Blog.first.allBylineItems.timestamp and (!data:post.shareUrl and !data:widgets.Blog.first.allBylineItems.share) and (!data:post.allowComments and !data:widgets.Blog.first.allBylineItems.comments or data:post.numberOfComments == 0)' name='nul'/>
                        
                          <b:if cond='data:view.isMultipleItems'>
                            <b:if cond='data:post.labels none (label =&gt; label.name in [ &quot;Sponsored&quot; , &quot;Product&quot; ])'>
                              <!--[ Post timestamp ]-->
                              <b:include cond='data:widgets.Blog.first.allBylineItems.timestamp' name='postTimestampPublish'/>
                            </b:if>
                      
                            <!--[ Post jumplinks ]-->
                            <b:include cond='data:widgets.Blog.first.allBylineItems.timestamp and data:post.labels none (label =&gt; label.name in [ &quot;Product&quot; ])' name='postJumpLinks'/>                    
                        
                            <b:else/>
                      
                            <!--[ Info in item page ]-->
                            <b:if cond='data:widgets.Blog.first.allBylineItems.author and data:post.labels none (label =&gt; label.name in [ &quot;Sponsored&quot; , &quot;Product&quot; ])'>
                              <div class='pIm'><b:include name='post-authorImage'/></div>
                            </b:if>
                              
                            <b:tag class='pNm' cond='data:widgets.Blog.first.allBylineItems.author or data:widgets.Blog.first.allBylineItems.timestamp' name='div'>
                              <b:class cond='!data:widgets.Blog.first.allBylineItems.timestamp and data:post.labels any (label =&gt; label.name in [ &quot;Sponsored&quot; , &quot;Product&quot; ])' name='l'/>
                              <b:class cond='!data:post.author.authorPhoto.image' name='n'/>
                              <b:include cond='data:widgets.Blog.first.allBylineItems.author and data:post.labels none (label =&gt; label.name in [ &quot;Sponsored&quot; , &quot;Product&quot; ])' name='post-authorName'/>
                                
                              <b:if cond='data:widgets.Blog.first.allBylineItems.timestamp'>
                                <div class='pDr'>
                                  <bdi class='pDt pIn'><b:include name='postTimestamps'/></bdi>
                                
                                  <b:if cond='data:post.labels none (label =&gt; label.name in [ &quot;Product&quot; ])'>
                                    <!--[ Reading time ]-->
                                    <div class='pRd pIn'><bdi id='rdTime'/></div>
                                  </b:if>
                                </div>
                              </b:if>
                            </b:tag>
                             
                            <div class='pCm'>
                              <b:class cond='(data:post.allowComments and data:widgets.Blog.first.allBylineItems.comments and data:post.numberOfComments &gt; 0) and (data:post.shareUrl and data:widgets.Blog.first.allBylineItems.share)' name='l'/>
                              <div class='pIc'>
                                <!--[ Bookmark ]-->
                                <label aria-label='Read later' class='bmPs tIc' data-added='Remove' expr:bm-id='data:post.id' expr:bm-img='resizeImage(data:post.featuredImage, 240, &quot;1:1&quot;)' expr:bm-ttl='data:post.title' expr:bm-url='data:post.url'><b:include name='bookmark-alt-icon'/></label> 
                                <b:if cond='(data:post.allowComments and data:widgets.Blog.first.allBylineItems.comments and data:post.numberOfComments &gt; 0) or (data:post.shareUrl and data:widgets.Blog.first.allBylineItems.share)'>
                                  <!--[ Comment ]-->
                                  <b:include cond='data:post.allowComments and data:widgets.Blog.first.allBylineItems.comments' name='postCommentsLabel'/>
                                  <!--[ Share ]-->
                                  <b:if cond='data:post.shareUrl and data:widgets.Blog.first.allBylineItems.share'>
                                    <label class='sh tIc' for='forShare'><b:include name='share-icon'/></label>
                                  </b:if>
                                </b:if>
                              </div>
                            </div>
                          
                          </b:if>
                        </b:tag>
                      </b:includable>
                      <b:includable id='postInfoShare'>
                        <div class='pSh'>
                          <b:class cond='data:post.labels any (label =&gt; label.name in [ &quot;Sponsored&quot; ])' name='nSpr'/>
                          <b:if cond='data:post.labels'>
                            <!--[ Label HashTags ]-->
                            <div class='lbHt'>
                              <b:loop index='num' values='data:post.labels' var='label'>
                                <b:if cond='data:num &lt;= 2'>
                                  <a expr:aria-label='data:label.name' expr:href='data:label.url.canonical'><b:eval expr='&quot;#&quot; + data:label.name'/></a>
                                </b:if>
                              </b:loop>
                            </div>
                          </b:if>
                          <div class='pShc' expr:data-text='data:messages.share + &quot;:&quot;'>
                            <!-- Share to Facebook -->
                            <a aria-label='Facebook' class='c fb' data-text='Share' expr:href='&quot;https://www.facebook.com/sharer.php?u=&quot; + data:blog.url.canonical' rel='noopener' role='button' target='_blank'><b:include name='facebook-r-icon'/></a>
                            
                            <!-- Share to Whatsapp -->
                            <a aria-label='Whatsapp' class='c wa' data-text='Share' expr:href='&quot;https://api.whatsapp.com/send?text=&quot; + data:blog.url.canonical' rel='noopener' role='button' target='_blank'><b:include name='whatsapp-r-icon'/></a>
                                
                            <!-- Share to Twitter -->
                            <a aria-label='Twitter' class='c tw' data-text='Tweet' expr:href='&quot;https://twitter.com/share?url=&quot; + data:blog.url.canonical' rel='noopener' role='button' target='_blank'><b:include name='twitter-r-icon'/></a>
                            
                            <label expr:aria-label='data:messages.shareToOtherApps' for='forShare'><b:include name='plus-icon'/></label>
                          </div>
                        </div>
                      </b:includable>
                      <b:includable id='postInfoShareMore'>
                        <input class='shIn fixi hidden' id='forShare' type='checkbox'/>
                        <div class='shBr fixL'>
                          <div class='shBri fixLi'>
                            <div class='shBrs fixLs'>
                              <div class='shH fixH fixT' expr:data-text='data:messages.shareToOtherApps'>
                                <label aria-label='Close' class='c cl' for='forShare'/>
                              </div>
                              <div class='shC'>
                                <!--[ Share to other apps ]-->
                                <div class='shL'>
                                  <div data-text='Facebook'>
                                    <!-- Share to Facebook -->
                                    <a aria-label='Facebook' expr:href='&quot;https://www.facebook.com/sharer.php?u=&quot; + data:blog.url.canonical' rel='noopener' target='_blank'>
                                      <b:include name='facebook-r-icon'/>
                                    </a>
                                  </div>
                                    
                                  <div data-text='WhatsApp'>
                                    <!-- Share to Whatsapp -->
                                    <a aria-label='Whatsapp' expr:href='&quot;https://api.whatsapp.com/send?text=&quot; + data:blog.url.canonical' rel='noopener' target='_blank'>
                                      <b:include name='whatsapp-r-icon'/>
                                    </a>
                                  </div>
                                    
                                  <div data-text='Twitter'>
                                    <!-- Share to Twitter -->
                                    <a aria-label='Twitter' expr:href='&quot;https://twitter.com/share?url=&quot; + data:blog.url.canonical' rel='noopener' target='_blank'>
                                      <b:include name='twitter-r-icon'/>
                                    </a>
                                  </div>
                                    
                                  <div data-text='Telegram'>
                                    <!-- Share to Telegram -->
                                    <a aria-label='Telegram' expr:href='&quot;https://t.me/share/url?url=&quot; + data:blog.url.canonical' rel='noopener' target='_blank'>
                                      <b:include name='telegram-r-icon'/>
                                    </a>
                                  </div>
                                    
                                  <div data-text='Pinterest'>
                                    <!-- Pin to Pinterst -->
                                    <a aria-label='Pinterest' data-pin-config='beside' expr:href='&quot;https://pinterest.com/pin/create/button/?url=&quot; + data:blog.url.canonical + &quot;&amp;media=&quot; + resizeImage(data:blog.postImageUrl, 1600)' rel='noopener' target='_blank'>
                                      <b:include name='pinterest-r-icon'/>
                                    </a>
                                  </div>
                                    
                                  <div data-text='LinkedIn'>
                                    <!-- Share Linkedin -->
                                    <a aria-label='Linkedin' expr:href='&quot;https://www.linkedin.com/sharing/share-offsite/?url=&quot; + data:blog.url.canonical' rel='noopener' target='_blank'>
                                      <b:include name='linkedIn-r-icon'/>
                                    </a>
                                  </div>
                                    
                                  <div data-text='Line'>
                                    <!-- Share to Line -->
                                    <a aria-label='Line' expr:href='&quot;https://timeline.line.me/social-plugin/share?url=&quot; + data:blog.url.canonical' rel='noopener' target='_blank'>
                                      <b:include name='line-r-icon'/>
                                    </a>
                                  </div>
                                    
                                  <div data-text='Email'>
                                    <!-- Send to email -->
                                    <a aria-label='Email' expr:href='&quot;mailto:?body=&quot; + data:blog.url.canonical' target='_blank'>
                                      <b:include name='mail-r-icon'/>
                                    </a>
                                  </div>
                                </div>
                                  
                                <!--[ Copy link ]-->
                                <div class='cpL' data-text='or copy link' expr:data-message='data:messages.copyToClipboard'>
                                  <div class='cpLb'>
                                    <b:include name='link-alt-icon'/>
                                    <input expr:value='data:blog.url.canonical' id='getlink' onClick='this.select()' readonly='readonly'/>
                                    <label for='getlink' onclick='copyFunction()'><data:messages.copy/></label>
                                  </div>
                                  
                                  <div class='cpLn' id='cpNotif'/>
                                  
                                  <script>function copyFunction(){document.getElementById(&#39;getlink&#39;).select(),document.execCommand(&#39;copy&#39;),toastNotif(&#39;&lt;i class=&quot;copy&quot;&gt;&lt;/i&gt;<data:messages.linkCopiedToClipboard/>&#39;); if(&#39;vibrate&#39; in navigator){navigator.vibrate([50])};}</script>
                              </div>
                              </div>
                            </div>
                          </div>
                            
                          <label class='fCls' for='forShare'/>
                        </div>
                      </b:includable>
                      <b:includable id='postJumpLink' var='post'/>
                      <b:includable id='postLabels'/>
                      <b:includable id='postLocation'/>
                      <b:includable id='postMeta' var='post'>
                        <b:include data='post' name='postMetadataJSON'/>
                      </b:includable>
                      <b:includable id='postMetadataJSON'>
                        <script type='application/ld+json'>{&quot;@context&quot;: &quot;http://schema.org&quot;, &quot;@type&quot;: &quot;BlogPosting&quot;, &quot;mainEntityOfPage&quot;: {&quot;@type&quot;: &quot;WebPage&quot;, &quot;@id&quot;: &quot;<data:post.url.canonical.jsonEscaped/>&quot;}, &quot;headline&quot;: &quot;<data:post.title.jsonEscaped/>&quot;, &quot;description&quot;: &quot;<b:eval expr='snippet(data:post.snippets.long, {length: 250, links: false, linebreaks: false, ellipsis: true }).jsonEscaped'/>&quot;, <b:if cond='data:view.isSingleItem'>&quot;articleBody&quot;: &quot;<b:eval expr='snippet(data:post.body, {links: false, linebreaks: false, ellipsis: true}).jsonEscaped'/>&quot;, </b:if>&quot;datePublished&quot;: &quot;<data:post.date.iso8601.jsonEscaped/>&quot;, &quot;dateModified&quot;: &quot;<data:post.lastUpdated.iso8601.jsonEscaped/>&quot;, <b:include data='post' name='postMetadataJSONImage'/><b:include data='post' name='postMetadataJSONPublisher'/> &quot;author&quot;: { &quot;@type&quot;: &quot;Person&quot;, &quot;name&quot;: &quot;<data:post.author.name.jsonEscaped/>&quot;, &quot;url&quot;: &quot;<b:if cond='data:post.author.profileUrl'><data:post.author.profileUrl.jsonEscaped/><b:else/><data:blog.homepageUrl.jsonEscaped/></b:if>&quot;, &quot;image&quot;: &quot;<data:post.author.authorPhoto.image.jsonEscaped/>&quot;}}</script>
                      </b:includable>
                      <b:includable id='postMetadataJSONImage'>&quot;image&quot;: {&quot;@type&quot;: &quot;ImageObject&quot;, &quot;url&quot;: &quot;<b:eval expr='(data:post.featuredImage ? resizeImage(data:post.featuredImage, 1200, &quot;1200:630&quot;) : &quot;https://1.bp.blogspot.com/-Y87XSHXfRwc/YgJF1axHpvI/AAAAAAAACSY/5KKOYgp2trYXwQv3oqNDn_8Ee42P63TzwCNcBGAsYHQ/s1200-rw/Fineshop_Design.webp&quot;).jsonEscaped'/>&quot;, &quot;height&quot;: <b:eval expr='data:post.featuredImage ? 630 : 348'/>, &quot;width&quot;: 1200},</b:includable>
                      <b:includable id='postMetadataJSONPublisher'>&quot;publisher&quot;: {&quot;@type&quot;: &quot;Organization&quot;, &quot;name&quot;: &quot;<data:blog.title.escaped/>&quot;, &quot;logo&quot;: {&quot;@type&quot;: &quot;ImageObject&quot;, &quot;url&quot;: &quot;https://1.bp.blogspot.com/-WvkZcW5G-wQ/YhuAbZBiHtI/AAAAAAAACVI/iqgRN-mBkg4zNmoOB5Qfa_57mZulFOqbACNcBGAsYHQ/s297/fd-logo.png&quot;, &quot;width&quot;: 297, &quot;height&quot;: 45}},</b:includable>
                      <b:includable id='postPagination'>
                        <!--[ Blog navigation ]-->
                        <div class='blogPg' id='blogPager'>
                          <b:tag class='nPst' cond='data:view.url == data:blog.homepageUrl and !data:olderPageUrl' expr:data-text='data:messages.noResultsFound' name='div'/>
                      
                          <b:include cond='!data:posts.empty' name='postPagination.new'/>
                          <b:include cond='!data:posts.empty' name='postPagination.home'/>
                          <b:include cond='!data:posts.empty' name='postPagination.old'/>
                        </div>
                      </b:includable>
                      <b:includable id='postPagination.home'>
                        <b:tag class='hmLnk' expr:name='data:view.url != data:blog.homepageUrl ? &quot;a&quot; : &quot;div&quot;'>
                          <b:class cond='data:view.url == data:blog.homepageUrl' name='nPst'/>
                          <b:attr cond='data:view.url != data:blog.homepageUrl' expr:value='data:blog.homepageUrl.canonical' name='href'/>
                          <b:attr cond='data:view.url != data:blog.homepageUrl' expr:value='data:messages.home' name='aria-label'/>
                          <b:attr cond='data:posts.empty' expr:value='data:messages.home' name='data-text'/>
                          <b:include cond='!data:posts.empty' name='home-alt-icon'/>
                        </b:tag>                      
                      </b:includable>
                      <b:includable id='postPagination.new'>
                        <b:if cond='data:newerPageUrl and data:view.url != data:blog.homepageUrl.canonical path &quot;search&quot;'>
                          <a class='nwLnk' expr:aria-label='data:messages.newest' expr:data-text='data:messages.newest' expr:href='data:newerPageUrl.canonical'>
                            <b:if cond='data:blog.languageDirection != &quot;rtl&quot;'>
                              <b:include name='arrow-left-icon'/>
                              <b:else/>
                              <b:include name='arrow-right-icon'/>
                            </b:if>
                          </a>
                          <b:else/>
                          <div class='nwLnk nPst' expr:data-text='data:messages.newest'>
                            <b:if cond='data:blog.languageDirection != &quot;rtl&quot;'>
                              <b:include name='arrow-left-icon'/>
                              <b:else/>
                              <b:include name='arrow-right-icon'/>
                            </b:if>
                          </div>
                        </b:if>
                      </b:includable>
                      <b:includable id='postPagination.old'>
                        <b:tag class='olLnk' expr:data-text='data:messages.oldest' expr:name='data:olderPageUrl ? &quot;a&quot; : &quot;div&quot;'>
                          <b:class cond='!data:olderPageUrl' name='nPst'/>
                          <b:attr cond='data:olderPageUrl' expr:value='data:olderPageUrl.canonical' name='href'/>
                          <b:attr cond='data:olderPageUrl' expr:value='data:messages.oldest' name='aria-label'/>
                          <b:if cond='data:blog.languageDirection != &quot;rtl&quot;'>
                            <b:include name='arrow-right-icon'/>
                            <b:else/>
                            <b:include name='arrow-left-icon'/>
                          </b:if>
                        </b:tag>
                      </b:includable>
                      <b:includable id='postQuickEdit' var='post'>
  <b:if cond='data:post.editUrl'>
    <span expr:class='&quot;item-control &quot; + data:post.adminClass'>
      <a expr:href='data:post.editUrl' expr:title='data:top.editPostMsg'>
        <img alt='' class='icon-action' height='18' src='https://resources.blogblog.com/img/icon18_edit_allbkg.gif' width='18'/>
      </a>
    </span>
  </b:if>
</b:includable>
                      <b:includable id='postReactions'/>
                      <b:includable id='postShareButtons'/>
                      <b:includable id='postTimestamp'/>
                      <b:includable id='postTitle' var='post'/>
                      <b:includable id='previousPageLink'/>
                      <b:includable id='shareButtons' var='post'>
  <b:if cond='data:top.showEmailButton'><a class='goog-inline-block share-button sb-email' expr:href='data:post.sharePostUrl + &quot;&amp;target=email&quot;' expr:title='data:top.emailThisMsg' target='_blank'><span class='share-button-link-text'><data:top.emailThisMsg/></span></a></b:if><b:if cond='data:top.showBlogThisButton'><a class='goog-inline-block share-button sb-blog' expr:href='data:post.sharePostUrl + &quot;&amp;target=blog&quot;' expr:onclick='&quot;window.open(this.href, \&quot;_blank\&quot;, \&quot;height=270,width=475\&quot;); return false;&quot;' expr:title='data:top.blogThisMsg' target='_blank'><span class='share-button-link-text'><data:top.blogThisMsg/></span></a></b:if><b:if cond='data:top.showTwitterButton'><a class='goog-inline-block share-button sb-twitter' expr:href='data:post.sharePostUrl + &quot;&amp;target=twitter&quot;' expr:title='data:top.shareToTwitterMsg' target='_blank'><span class='share-button-link-text'><data:top.shareToTwitterMsg/></span></a></b:if><b:if cond='data:top.showFacebookButton'><a class='goog-inline-block share-button sb-facebook' expr:href='data:post.sharePostUrl + &quot;&amp;target=facebook&quot;' expr:onclick='&quot;window.open(this.href, \&quot;_blank\&quot;, \&quot;height=430,width=640\&quot;); return false;&quot;' expr:title='data:top.shareToFacebookMsg' target='_blank'><span class='share-button-link-text'><data:top.shareToFacebookMsg/></span></a></b:if><b:if cond='data:top.showPinterestButton'><a class='goog-inline-block share-button sb-pinterest' expr:href='data:post.sharePostUrl + &quot;&amp;target=pinterest&quot;' expr:title='data:top.shareToPinterestMsg' target='_blank'><span class='share-button-link-text'><data:top.shareToPinterestMsg/></span></a></b:if>
</b:includable>
                      <b:includable id='sharingButton'/>
                      <b:includable id='sharingButtonContent'/>
                      <b:includable id='sharingButtons'/>
                      <b:includable id='sharingButtonsMenu'/>
                      <b:includable id='sharingPlatformIcon'/>
                      <b:includable id='status-message'>
  <b:if cond='data:navMessage'>
  <div class='status-msg-wrap'>
    <div class='status-msg-body'>
      <data:navMessage/>
    </div>
    <div class='status-msg-border'>
      <div class='status-msg-bg'>
        <div class='status-msg-hidden'><data:navMessage/></div>
      </div>
    </div>
  </div>
  <div style='clear: both;'/>
  </b:if>
</b:includable>
                      <b:includable id='threaded-comment-form' var='post'>
  <div class='comment-form'>
    <a name='comment-form'/>
    <b:if cond='data:mobile'>
      <p><data:blogCommentMessage/></p>
      <data:blogTeamBlogMessage/>
      <a expr:href='data:post.commentFormIframeSrc' id='comment-editor-src'/>
      <iframe allowtransparency='true' class='blogger-iframe-colorize blogger-comment-from-post' expr:height='data:cmtIframeInitialHeight' frameborder='0' id='comment-editor' name='comment-editor' src='' style='display: none' width='100%'/>
    <b:else/>
      <p><data:blogCommentMessage/></p>
      <data:blogTeamBlogMessage/>
      <a expr:href='data:post.commentFormIframeSrc' id='comment-editor-src'/>
      <iframe allowtransparency='true' class='blogger-iframe-colorize blogger-comment-from-post' expr:height='data:cmtIframeInitialHeight' frameborder='0' id='comment-editor' name='comment-editor' src='' width='100%'/>
    </b:if>
    <data:post.cmtfpIframe/>
    <script type='text/javascript'>
      BLOG_CMT_createIframe(&#39;<data:post.appRpcRelayPath/>&#39;);
    </script>
  </div>
</b:includable>
                      <b:includable id='threadedCommentForm' var='post'/>
                      <b:includable id='threadedCommentJs' var='post'/>
                      <b:includable id='threadedComments' var='post'/>
                      <b:includable id='threadedComments-form' var='post'>
                        <div id='commentForm'>
                          <!--[ Comment message ]-->
                          <b:if cond='data:this.messages.blogComment != &quot;&quot;'>
                            <div class='cmMs note'>
                              <data:this.messages.blogComment/>
                            </div>
                          </b:if>
                            
                          <!--[ Comment iframe (remove comment tag below to improve height of comment form) ]-->
                          <!--<a aria-label='Comment Form' expr:href='data:post.commentFormIframeSrc + &quot;&amp;skin=contempo&quot; + data:variantParam' id='comment-editor-src'/>-->
                          <!--<iframe class='blogger-iframe-colorize blogger-comment-from-post lazy' expr:data-src='data:post.commentFormIframeSrc appendParams {skin: &quot;contempo&quot;}' height='296' id='comment-editor' name='comment-editor' title='Blogger comment' width='100'/>-->
                          <button class='iCmn' expr:data-comment='data:post.commentFormIframeSrc appendParams {skin: &quot;contempo&quot;}' expr:data-text='data:messages.joinTheConversation + &quot; (&quot; + data:post.numberOfComments + &quot;)&quot;' id='iCmn'/>
                          <script>/*<![CDATA[*/ (function(){var tCom=getid('iCmn'),urlCom=tCom.getAttribute('data-comment'),ifCom=document.createElement('iframe');ifCom.width='100%';ifCom.height='296';ifCom.setAttribute('data-src',urlCom);ifCom.setAttribute('id','comment-editor');ifCom.setAttribute('class','blogger-iframe-colorize blogger-comment-from-post lazy');ifCom.setAttribute('name','comment-editor');ifCom.setAttribute('lazied','');tCom.addEventListener('click',function(){tCom.insertAdjacentElement('beforebegin',ifCom);this.remove();});})() /*]]>*/</script>
                         
                          <!--<script src='https://www.blogger.com/static/v1/jsbin/1875144490-comment_from_post_iframe.js'/>
                          <script>BLOG_CMT_createIframe(&#39;<data:post.appRpcRelayPath/>&#39;);</script>-->
                        </div>
                      </b:includable>
                      <b:includable id='threadedComments-modifV3' var='post'>
                        <b:tag class='cmSh fixi hidden' id='forComments' name='input' type='checkbox'/>
                        <b:tag class='cmAl hidden' cond='data:post.numberOfComments &gt; 0' id='forAllCm' name='input' type='checkbox'/>
                          
                        <!--[ Delete tag below to disable show/hide comment ]-->
                        <b:include cond='data:post.allowComments' name='post-commentButton'/>
                    
                        <section class='cm' expr:data-embed='data:post.embedCommentForm' expr:data-num-comments='data:post.numberOfComments' id='comments'>
                            
                          <!--[ Delete tag below to disable Pop-up comments ]-->
                          <b:class name='cmBr fixL'/>
                            
                          <b:tag class='cmBri' name='div'>
                            <b:class cond='data:post.numberOfComments == 0' name='mty'/>
                            <b:tag class='cmBrs fixLs' name='div'>
                    
                              <!--[ Comments title ]-->
                              <div class='cmH fixH'>
                                <b:include name='commentsTitle'/>
                        
                                <div class='cmI cl'>
                                  <!-- Sort comments -->
                                  <b:if cond='data:post.numberOfComments &gt; 0'>
                                    <label class='s' expr:aria-label='data:messages.manageComments' expr:data-new='data:messages.newest' expr:data-text='data:messages.oldest' for='forAllCm'/>
                                  </b:if>
                                  
                                  <label aria-label='Close' class='c' for='forComments'/>
                                </div>
                              </div>
                      
                              <div class='cmC'>
                                <b:if cond='data:post.numberOfComments &gt; 0'>
                                  <div class='cmCn'>
                          
                                    <ol class='cmHl' id='cmHolder'>
                                      <b:loop values='data:post.comments where (c =&gt; not c.inReplyTo or c.inReplyTo == 0)' var='commentLevel1'>
                                        <li class='c' expr:id='&quot;c&quot; + data:commentLevel1.id'>
                                          <b:class cond='data:cb.level.author == data:post.author.name' name='adm'/>
                                          <b:include data='{level: data:commentLevel1,d: true}' name='commentblock'/>
                                          <span expr:class='&quot;cmDl &quot; + data:commentLevel1.adminClass'>
                                            <a aria-label='Delete' expr:href='data:commentLevel1.deleteUrl' rel='nofollow noopener noreferrer' target='_blank'>
                                              <b:include name='delete-icon'/>
                                            </a>
                                          </span>
                                          
                                          <b:with value='data:post.comments where (c =&gt; c.inReplyTo == data:commentLevel1.id)' var='commentL2'>
                                            <b:if cond='data:commentL2.size != &quot;0&quot;'>
                                              <input class='cmRi hidden' expr:id='&quot;to-&quot; + data:commentLevel1.id' type='checkbox'/>
                                                
                                              <div class='cmRp'>
                                                <div class='cmTh' expr:id='&quot;c&quot; + data:commentLevel1.id + &quot;-rt&quot;'>
                                                  <label class='thTg' data-text='Hide replies' expr:aria-label='&quot;View &quot; +data:commentL2.length+ &quot; replies&quot;' expr:for='&quot;to-&quot; + data:commentLevel1.id'/>
                                    
                                                  <ol class='thCh'>
                                                    <b:loop values='data:commentL2' var='commentLevel2'>
                                                      <li class='c' expr:id='&quot;c&quot; + data:commentLevel2.id'>
                                                        <b:class cond='data:cb.level.author == data:post.author.name' name='adm'/>
                                                        <b:include data='{level: data:commentLevel2,d: true}' name='commentblock'/>
                                                        <span expr:class='&quot;cmDl rp &quot; + data:commentLevel2.adminClass'>
                                                          <a aria-label='Delete' expr:href='data:commentLevel2.deleteUrl' rel='nofollow noopener noreferrer' target='_blank'>
                                                            <b:include name='delete-icon'/>
                                                          </a>
                                                        </span>
                                                        &lt;/div&gt;
                                                      </li>
                                                    </b:loop>
                                                  </ol>
                                    
                                                </div>
                                                <b:if cond='data:post.allowNewComments'>
                                                  <div class='cmAc cmR'>
                                                    <a aria-label='Reply' class='rpTo' data-text='Reply' expr:data-reply-to='data:commentLevel1.id' href='javascript:;' target='_self'/>
                                                  </div>
                                                </b:if>
                                              </div>
                                            </b:if>
                                          </b:with>
                              
                                          <b:if cond='data:post.allowNewComments'>
                                            <div class='cmAc'>
                                              <a aria-label='Reply' class='rpTo' data-text='Reply' expr:data-reply-to='data:commentLevel1.id' href='javascript:;' target='_self'/>
                                            </div>
                                          </b:if>
                                          &lt;/div&gt;
                                        </li>
                                      </b:loop>
                                    </ol>
                                
                                    <!--[ Timeago script ]-->
                                    <b:if cond='data:blog.languageDirection != &quot;rtl&quot;'>
                                      <script>/*<![CDATA[*/ (function timeAgo(selector) { var templates = {prefix: "", suffix: "", seconds: "second ago", minute: "1 min", minutes: "%d min", hour: "1 hour", hours: "%d hour", day: "1 day", days: "%d days", month: "1 month", months: "%d month", year: "1 year", years: "%d years"}; var template = function(t, n) { return templates[t] && templates[t].replace(/%d/i, Math.abs(Math.round(n))); }; var timer = function(time) { if (!time) return; time = time.replace(/\.\d+/, ""); time = time.replace(/-/, "/").replace(/-/, "/"); time = time.replace(/T/, " ").replace(/Z/, " UTC"); time = time.replace(/([\+\-]\d\d)\:?(\d\d)/, " $1$2"); time = new Date(time * 1000 || time); var now = new Date(); var seconds = ((now.getTime() - time) * .001) >> 0; var minutes = seconds / 60; var hours = minutes / 60; var days = hours / 24; var years = days / 365; return templates.prefix + ( seconds < 45 && template('seconds', seconds) || seconds < 90 && template('minute', 1) || minutes < 45 && template('minutes', minutes) || minutes < 90 && template('hour', 1) || hours < 24 && template('hours', hours) || hours < 42 && template('day', 1) || days < 30 && template('days', days) || days < 45 && template('month', 1) || days < 365 && template('months', days / 30) || years < 1.5 && template('year', 1) || template('years', years) ) + templates.suffix; }; var elements = document.getElementsByClassName('dtTm'); for (var i in elements) { var $this = elements[i]; if (typeof $this === 'object') { $this.innerHTML = timer($this.getAttribute('title') || $this.getAttribute('data-datetime')); } } setTimeout(timeAgo, 60000); })(); /*]]>*/</script>
                                    </b:if>
                                  </div>
                      
                                  <b:if cond='data:post.allowNewComments'>
                                    <div class='cmAd hidden' id='addCm'>
                                      <div aria-label='Berkomentar' class='cmBtn button' role='button'>
                                        <!--[ Delete tag bellow to change button style ]-->
                                        <b:class name='ln'/>
                                          
                                        <b:message name='messages.postAComment'/>
                                      </div>
                                    </div>
                                  </b:if>
                              
                                  <script>var comment = true;</script>
                                  <b:else/>
                                  <script>var comment = false;</script>
                                </b:if>
                    
                                <div class='cmFrm'>
                                  <b:if cond='data:post.embedCommentForm'>
                                    <b:if cond='data:post.allowNewComments'>
                                      <b:include data='post' name='threadedComments-form'/>
                                      <b:else/>
                                      <!--[ If comment was disable ]-->
                                      <div class='cmDis'><data:post.noNewCommentsText/></div>
                                    </b:if>
                                    <b:else/>
                                    <b:if cond='data:post.allowComments'>
                                      <!--[ Comment message ]-->
                                      <b:if cond='data:this.messages.blogComment != &quot;&quot;'>
                                        <div class='cmMs note'>
                                          <data:this.messages.blogComment/>
                                        </div>
                                      </b:if>
                                        
                                      <a class='cmBtn button' expr:href='data:post.commentsUrl' expr:onclick='data:post.commentsUrlOnclick' rel='noreferrer' role='button' target='_blank'>
                                        <!--[ Delete tag bellow to change button style ]-->
                                        <b:class name='ln'/>
                                          
                                        <b:message name='messages.postAComment'/>
                                      </a>
                                    </b:if>
                                  </b:if>
                                </div>
                              </div>
                         
                            </b:tag>
                          </b:tag>
                        
                          <label class='fCls' for='forComments'/>
                        </section>
                  
                        <b:if cond='data:post.allowNewComments'>
                          <script>/*<![CDATA[*/ var observerK=new MutationObserver(function(mutations,me){var cekkomen=getid('comment-editor');if(cekkomen){var b=getid("comment-editor"),d=b.getAttribute("data-src");if(b.setAttribute("src",d),1==comment){var f=getclass("rpTo"),c=getid("commentForm"),h=f.length,k=function(b,d,e,f){b.addEventListener("click",function(){var c=b.getAttribute("data-reply-to");getid("c"+c).appendChild(d);getid("commentForm").className='cmRbox';getid("addCm").className='commentsReply';e.src=f+"&parentID="+c})};for(i=0;i<h;i++)k(f[i],c,b,d);var l=getclass("cmFrm")[0];getid("addCm").addEventListener("click",function(){l.appendChild(c);getid("commentForm").className='comment-replybox-thread';getid("addCm").className='hidden';b.src=d})};me.disconnect();return}});observerK.observe(document,{childList:true,subtree:true}); var bscom=getid('forComments'),bskom=getid('iCmn');if(bscom!==null){bscom.addEventListener('click',function(){if(bscom.checked==true){if(bskom!==null){bskom.click()};}})}; /*]]>*/</script>
                        </b:if>
                        <script>/*<![CDATA[*/ /* Comment tag, license: dte.web.id/teknis/paket-javascript-fitur-manipulasi */ function repText(id) {var a = document.getElementById(id); if (!a) return; var b = a.innerHTML; b = b.replace(/<i rel="image">(.*?)<\/i>/ig, "<img class='lazy' data-src='$1' src='data:image/png;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=' alt='Image Comment' \/>"); a.innerHTML = b;} repText('cmHolder'); /*]]>*/</script>
                      </b:includable>
                      <b:includable id='threaded_comment_js' var='post'>
  <script async='async' expr:src='data:post.commentSrc' type='text/javascript'/>

  <script type='text/javascript'>
    (function() {
      var items = <data:post.commentJso/>;
      var msgs = <data:post.commentMsgs/>;
      var config = <data:post.commentConfig/>;

// <![CDATA[
      var cursor = null;
      if (items && items.length > 0) {
        cursor = parseInt(items[items.length - 1].timestamp) + 1;
      }

      var bodyFromEntry = function(entry) {
        var text = (entry &&
                    ((entry.content && entry.content.$t) ||
                     (entry.summary && entry.summary.$t))) ||
            '';
        if (entry && entry.gd$extendedProperty) {
          for (var k in entry.gd$extendedProperty) {
            if (entry.gd$extendedProperty[k].name == 'blogger.contentRemoved') {
              return '<span class="deleted-comment">' + text + '</span>';
            }
          }
        }
        return text;
      }

      var parse = function(data) {
        cursor = null;
        var comments = [];
        if (data && data.feed && data.feed.entry) {
          for (var i = 0, entry; entry = data.feed.entry[i]; i++) {
            var comment = {};
            // comment ID, parsed out of the original id format
            var id = /blog-(\d+).post-(\d+)/.exec(entry.id.$t);
            comment.id = id ? id[2] : null;
            comment.body = bodyFromEntry(entry);
            comment.timestamp = Date.parse(entry.published.$t) + '';
            if (entry.author && entry.author.constructor === Array) {
              var auth = entry.author[0];
              if (auth) {
                comment.author = {
                  name: (auth.name ? auth.name.$t : undefined),
                  profileUrl: (auth.uri ? auth.uri.$t : undefined),
                  avatarUrl: (auth.gd$image ? auth.gd$image.src : undefined)
                };
              }
            }
            if (entry.link) {
              if (entry.link[2]) {
                comment.link = comment.permalink = entry.link[2].href;
              }
              if (entry.link[3]) {
                var pid = /.*comments\/default\/(\d+)\?.*/.exec(entry.link[3].href);
                if (pid && pid[1]) {
                  comment.parentId = pid[1];
                }
              }
            }
            comment.deleteclass = 'item-control blog-admin';
            if (entry.gd$extendedProperty) {
              for (var k in entry.gd$extendedProperty) {
                if (entry.gd$extendedProperty[k].name == 'blogger.itemClass') {
                  comment.deleteclass += ' ' + entry.gd$extendedProperty[k].value;
                } else if (entry.gd$extendedProperty[k].name == 'blogger.displayTime') {
                  comment.displayTime = entry.gd$extendedProperty[k].value;
                }
              }
            }
            comments.push(comment);
          }
        }
        return comments;
      };

      var paginator = function(callback) {
        if (hasMore()) {
          var url = config.feed + '?alt=json&v=2&orderby=published&reverse=false&max-results=50';
          if (cursor) {
            url += '&published-min=' + new Date(cursor).toISOString();
          }
          window.bloggercomments = function(data) {
            var parsed = parse(data);
            cursor = parsed.length < 50 ? null
                : parseInt(parsed[parsed.length - 1].timestamp) + 1
            callback(parsed);
            window.bloggercomments = null;
          }
          url += '&callback=bloggercomments';
          var script = document.createElement('script');
          script.type = 'text/javascript';
          script.src = url;
          document.getElementsByTagName('head')[0].appendChild(script);
        }
      };
      var hasMore = function() {
        return !!cursor;
      };
      var getMeta = function(key, comment) {
        if ('iswriter' == key) {
          var matches = !!comment.author
              && comment.author.name == config.authorName
              && comment.author.profileUrl == config.authorUrl;
          return matches ? 'true' : '';
        } else if ('deletelink' == key) {
          return config.baseUri + '/delete-comment.g?blogID='
               + config.blogId + '&postID=' + comment.id;
        } else if ('deleteclass' == key) {
          return comment.deleteclass;
        }
        return '';
      };

      var replybox = null;
      var replyUrlParts = null;
      var replyParent = undefined;

      var onReply = function(commentId, domId) {
        if (replybox == null) {
          // lazily cache replybox, and adjust to suit this style:
          replybox = document.getElementById('comment-editor');
          if (replybox != null) {
            replybox.height = '250px';
            replybox.style.display = 'block';
            replyUrlParts = replybox.src.split('#');
          }
        }
        if (replybox && (commentId !== replyParent)) {
          replybox.src = '';
          document.getElementById(domId).insertBefore(replybox, null);
          replybox.src = replyUrlParts[0]
              + (commentId ? '&parentID=' + commentId : '')
              + '#' + replyUrlParts[1];
          replyParent = commentId;
        }
      };

      var hash = (window.location.hash || '#').substring(1);
      var startThread, targetComment;
      if (/^comment-form_/.test(hash)) {
        startThread = hash.substring('comment-form_'.length);
      } else if (/^c[0-9]+$/.test(hash)) {
        targetComment = hash.substring(1);
      }

      // Configure commenting API:
      var configJso = {
        'maxDepth': config.maxThreadDepth
      };
      var provider = {
        'id': config.postId,
        'data': items,
        'loadNext': paginator,
        'hasMore': hasMore,
        'getMeta': getMeta,
        'onReply': onReply,
        'rendered': true,
        'initComment': targetComment,
        'initReplyThread': startThread,
        'config': configJso,
        'messages': msgs
      };

      var render = function() {
        if (window.goog && window.goog.comments) {
          var holder = document.getElementById('comment-holder');
          window.goog.comments.render(holder, provider);
        }
      };

      // render now, or queue to render when library loads:
      if (window.goog && window.goog.comments) {
        render();
      } else {
        window.goog = window.goog || {};
        window.goog.comments = window.goog.comments || {};
        window.goog.comments.loadQueue = window.goog.comments.loadQueue || [];
        window.goog.comments.loadQueue.push(render);
      }
    })();
// ]]>
  </script>
</b:includable>
                      <b:includable id='threaded_comments' var='post'>
  <div class='comments' id='comments'>
    <a name='comments'/>
    <h4><data:post.commentLabelFull/>:</h4>

    <div class='comments-content'>
      <b:include cond='data:post.embedCommentForm' data='post' name='threaded_comment_js'/>
      <div id='comment-holder'>
         <data:post.commentHtml/>
      </div>
    </div>

    <p class='comment-footer'>
      <b:if cond='data:post.allowNewComments'>
        <b:include data='post' name='threaded-comment-form'/>
      <b:else/>
        <data:post.noNewCommentsText/>
      </b:if>
    </p>

    <b:if cond='data:showCmtPopup'>
      <div id='comment-popup'>
        <iframe allowtransparency='true' frameborder='0' id='comment-actions' name='comment-actions' scrolling='no'>
        </iframe>
      </div>
    </b:if>

    <div id='backlinks-container'>
    <div expr:id='data:widget.instanceId + &quot;_backlinks-container&quot;'>
    </div>
    </div>
  </div>
</b:includable>
                    </b:widget>
                    <b:widget cond='data:view.isHomepage and !data:view.isPreview' id='HTML2' locked='true' title='Categorised Posts' type='HTML' version='2' visible='true'>
                      <b:widget-settings>
                        <b:widget-setting name='content'>Features</b:widget-setting>
                      </b:widget-settings>
                      <b:includable id='main'>
                        <!--[ Categorised Posts - 1 (Uses Feeds JSON) ]-->
                        <b:if cond='data:title != &quot;&quot;'>
                          <h2 class='title dt'><data:title/></h2>
                        </b:if>
                        <div class='ctgry'>
                          <div class='note'>Loading Posts...</div>
                          <script>/*<![CDATA[*/ /* Category Post */ function ctgryPst(f,y,e,T,x,Z){var a=blogUrl.replace(/.*?:\/\//g,"//"),t=0<f.length?"/-/"+f:"",e=a+"feeds/posts/default"+encodeURI(t)+"?alt=json&orderby=published&max-results="+e;Pu.gAj({url:e,async:!0,success:function(e,a){e=JSON.parse(e);var t,l,s,r,i,c,J,X,U,n="";if(qSel(y)!=null){a=e.feed;if(a.entry&&0!==a.entry.length){n+='<div class="blogPts cPst">';for(var d=a.entry,o=0;o<d.length;++o){for(var g=d[o],p=!1,a=0,m=g.media$thumbnail?g.media$thumbnail.url:"",b=g.title.$t,j=g.id.$t.split('post-')[1],w=g.link[1].title.split(' Comments')[0],h=("content"in g&&0<x?g.content.$t.split("<a name='more'>")[0].replace(/<\!--.*?-->/g,"").replace(/(<([^>]+)>)/gi,"").substring(0,x):""),v=g.link.length;a<v;++a)"alternate"==(t=g.link[a]).rel&&(p=t.href);s=m.includes("-rw")?"":"-rw-e30",m=(m=g.content&&""==m?(u=/<img +(.*?)src=(['"])([^'"]+?)(['"])(.*?) *\/?>/i.exec(g.content.$t))&&u[3]?u[3]:"":m).replace(/.*?:\/\//g,"//").replace(/\/s[0-9]+(\-c)?/,"/s"+T+s).replace(/\=s[0-9]+(\-c)?/,"=s"+T+s).replace(/\=.*w[0-9].*h[0-9].*-c+(\-c)?/,"=s"+T+s),j="<div class='iFxd'>"+(null!=w.match(/^[0-9]+$/)&&"0"!=w?"<a aria-label='Comments' class='cmnt' data-text='"+w+"' href='"+p+"#comment' role='button'><svg class='line' viewBox='0 0 24 24'><g transform='translate(2.000000, 2.000000)'><path d='M17.0710351,17.0698449 C14.0159481,20.1263505 9.48959549,20.7867004 5.78630747,19.074012 C5.23960769,18.8538953 1.70113357,19.8338667 0.933341969,19.0669763 C0.165550368,18.2990808 1.14639409,14.7601278 0.926307229,14.213354 C-0.787154393,10.5105699 -0.125888852,5.98259958 2.93020311,2.9270991 C6.83146881,-0.9756997 13.1697694,-0.9756997 17.0710351,2.9270991 C20.9803405,6.8359285 20.9723008,13.1680512 17.0710351,17.0698449 Z'/></g></svg></a>":"")+"<span class='pV pu-views hidden' data-id='"+j+"'><svg class='line' viewBox='0 0 24 24'><g transform='translate(2.000000, 4.000000)'><path class='svgC' d='M13.1643,8.0521 C13.1643,9.7981 11.7483,11.2141 10.0023,11.2141 C8.2563,11.2141 6.8403,9.7981 6.8403,8.0521 C6.8403,6.3051 8.2563,4.8901 10.0023,4.8901 C11.7483,4.8901 13.1643,6.3051 13.1643,8.0521 Z'/><path d='M0.7503,8.0521 C0.7503,11.3321 4.8923,15.3541 10.0023,15.3541 C15.1113,15.3541 19.2543,11.3351 19.2543,8.0521 C19.2543,4.7691 15.1113,0.7501 10.0023,0.7501 C4.8923,0.7501 0.7503,4.7721 0.7503,8.0521 Z'/></g></svg></span><span class='bM bmPs' aria-label='Read later' data-added='Remove' bm-id='"+j+"' bm-img='"+(m.replace("/s"+T,"/s240").replace("=s"+T,"=s240"))+"' bm-ttl='"+b+"' bm-url='"+p+"' role='button'><svg class='line' viewBox='0 0 24 24'><g transform='translate(4.500000, 2.500000)'><path d='M7.47024319,0 C1.08324319,0 0.00424318741,0.932 0.00424318741,8.429 C0.00424318741,16.822 -0.152756813,19 1.44324319,19 C3.03824319,19 5.64324319,15.316 7.47024319,15.316 C9.29724319,15.316 11.9022432,19 13.4972432,19 C15.0932432,19 14.9362432,16.822 14.9362432,8.429 C14.9362432,0.932 13.8572432,0 7.47024319,0 Z'/><line class='svgC v' transform='translate(-4.500000, -2.500000)' x1='12' x2='12' y1='6' y2='12'/><line class='svgC h' transform='translate(-4.500000, -2.500000)' x1='15' x2='9' y1='9' y2='9'/></g></svg></span></div><div class='blog-admin'><div class='iFxd l'><a arial-label='Edit this Post' class='edit' data-text='Edit' href='https://www.blogger.com/blog/post/edit/"+blogID+"/"+j+"' role='button' target='_blank'><svg class='line' viewBox='0 0 24 24'><g transform='translate(3.500000, 3.500000)'><line class='svgC' x1='9.8352' x2='16.2122' y1='16.0078' y2='16.0078'/><path d='M12.5578,1.3589 L12.5578,1.3589 C11.2138,0.3509 9.3078,0.6229 8.2998,1.9659 C8.2998,1.9659 3.2868,8.6439 1.5478,10.9609 C-0.1912,13.2789 1.4538,16.1509 1.4538,16.1509 C1.4538,16.1509 4.6978,16.8969 6.4118,14.6119 C8.1268,12.3279 13.1638,5.6169 13.1638,5.6169 C14.1718,4.2739 13.9008,2.3669 12.5578,1.3589 Z'/><line x1='7.0041' x2='11.8681' y1='3.7114' y2='7.3624'/></g></svg></a></div></div>",l=g.category[0]?"<a aria-label='"+g.category[0].term+"' data-text='"+g.category[0].term+"' href='"+blogUrl+"search/label/"+encodeURI(g.category[0].term)+"' rel='tag'></a>":"",s=g.category[1]?"<a aria-label='"+g.category[0].term+"' data-text='"+g.category[1].term+"' href='"+blogUrl+"search/label/"+encodeURI(g.category[1].term)+"' rel='tag'></a>":"",0,i=(r=(r=g.published.$t).substring(0,10)).substring(0,4),c=r.substring(5,7),r=r.substring(8,10),J=g.author?g.author[0].gd$image.src.replace(/\/s[0-9]+(\-c)?/,"/s20-rw").replace(/\=s[0-9]+(\-c)?/,"=s20-rw"):'',X=g.author?g.author[0].name.$t:'',U=X?"<div class='iFxd bl'><div class='aNm t'>"+(J?"<div class='im' style='background-image:url("+J+")'></div>":"")+"<bdi class='nm' data-text=\""+X+"\"></bdi><svg viewBox='0 0 24 24'><path d='M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z'></path></svg></div></div>":"";n+=""!=m?'<article class="ntry">':"<article class='ntry noThmb'>",n+=(""!=m?"<div class='pThmb'><a class='thmb' href='"+p+"'><img alt='"+b+"' class='imgThm' src='"+m+"'/></a>"+j+U+"</div>":"<div class='pThmb nul'><div class='thmb'><span class='imgThm' data-text='No image'</span></div>"+j+U+"</div>")+"<div class='pCntn'>"+(l?"<div class='pHdr pSml'><div class='pLbls' data-text='in'>"+l+s+"</div></div>":"")+"<h2 class='pTtl aTtl sml'><a data-text='"+b+"' href='"+p+"' rel='bookmark'>"+b+"</a></h2>"+(h?"<div class='pSnpt'>"+h+"</div>":"")+"<div class='pInf pSml'><time class='aTtmp pTtmp pbl' data-type='Published' data-text='"+(h=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][parseInt(c-1)]+" "+r+", "+i)+"' title='Published: "+h+"'></time><a aria-label='Read more' class='pJmp' data-text='Keep reading' href='"+p+"'></a></div></div>",n+="</article>"}n+="</div>",n+=true==Z?"<div class='blogPg'><a aria-label='View More' data-text='"+(""!=f?f+" - View More":"View More")+"' href='"+(""!=f?blogUrl+"search/label/"+encodeURI(f):blogUrl+"search")+"'></a></div>":"",addCt(qSel(y),'loaded'),qSel(y).innerHTML=n;if(typeof puViews=='function'){puViews()};if(typeof scrlRvl=='function'){scrlRvl()};if(typeof bkMrk=='function'){bkMrk()}}else qSel(y).innerHTML="<div class='note'>No Post was found with label '"+f+"'.</div>";}else{console.log("Sorry, Container was not found!")}},error:function(e){qSel(y).innerHTML="<div class='note wr'>Failed to load Posts :(</div>"}})}; /*]]>*/ const ctgryLb1 = &#39;<data:content/>&#39;;</script>
                        </div>
                      </b:includable>
                    </b:widget>
                    <b:widget cond='data:view.isHomepage and !data:view.isPreview' id='HTML3' locked='true' title='Read Documentation' type='HTML' version='2' visible='false'>
                      <b:widget-settings>
                        <b:widget-setting name='content'>Documentation</b:widget-setting>
                      </b:widget-settings>
                      <b:includable id='main'>
                        <!--[ Categorised Posts - 2 (Uses Feeds JSON) ]-->
                        <b:if cond='data:title != &quot;&quot;'>
                          <h2 class='title dt'><data:title/></h2>
                        </b:if>
                        <div class='ctgry'>
                          <div class='note'>Loading Posts...</div>
                          <script>const ctgryLb2 = &#39;<data:content/>&#39;;</script>
                        </div>
                      </b:includable>
                    </b:widget>
                    <b:widget cond='data:view.isPost and !data:view.isPreview' id='HTML01' locked='true' title='Middle Post Ad 01' type='HTML' version='2' visible='true'>
                      <b:widget-settings>
                        <b:widget-setting name='content'/>
                      </b:widget-settings>
                      <b:includable id='main'>
                        <div class='widget-content'>
                          <b:if cond='data:content != &quot;&quot;'>
                            <data:content/>
                          
                            <b:else/>
                            <!--[ Blank ad ]-->
                            <div class='adB h240' expr:data-text='data:messages.adsGoHere + &quot; (&quot; + data:title + &quot;)&quot;'/>
                          </b:if>
                        </div>
                        <!--[ Middle ads 01 - shows after 3rd paragraph ]-->
                        <script>/*<![CDATA[*/ middleAds('p',[['HTML01', 3]]) /*]]>*/</script>
                      </b:includable>
                    </b:widget>
                    <b:widget cond='data:view.isPost and !data:view.isPreview' id='HTML02' locked='true' title='Middle Post Ad 02' type='HTML' version='2' visible='true'>
                      <b:widget-settings>
                        <b:widget-setting name='content'/>
                      </b:widget-settings>
                      <b:includable id='main'>
                        <div class='widget-content'>
                          <b:if cond='data:content != &quot;&quot;'>
                            <data:content/>
                          
                            <b:else/>
                            <!--[ Blank ad ]-->
                            <div class='adB h240' expr:data-text='data:messages.adsGoHere + &quot; (&quot; + data:title + &quot;)&quot;'/>
                          </b:if>
                        </div>
                        <!--[ Middle ads 02 - shows after 6th paragraph ]-->
                        <script>/*<![CDATA[*/ middleAds('p',[['HTML02', 6]]) /*]]>*/</script>
                      </b:includable>
                    </b:widget>
                    <b:widget cond='data:view.isPost and !data:view.isPreview' id='HTML03' locked='true' title='Middle Post Ad 03' type='HTML' version='2' visible='true'>
                      <b:widget-settings>
                        <b:widget-setting name='content'/>
                      </b:widget-settings>
                      <b:includable id='main'>
                        <div class='widget-content'>
                          <b:if cond='data:content != &quot;&quot;'>
                            <data:content/>
                          
                            <b:else/>
                            <!--[ Blank ad ]-->
                            <div class='adB h240' expr:data-text='data:messages.adsGoHere + &quot; (&quot; + data:title + &quot;)&quot;'/>
                          </b:if>
                        </div>
                        <!--[ Middle ads 03 - shows after 9th paragraph ]-->
                        <script>/*<![CDATA[*/ middleAds('p',[['HTML03', 9]]) /*]]>*/</script>
                      </b:includable>
                    </b:widget>
                    <b:widget cond='data:view.isPost and !data:view.isPreview' id='HTML04' locked='true' title='Recurring Post Ad' type='HTML' version='2' visible='true'>
                      <b:widget-settings>
                        <b:widget-setting name='content'/>
                      </b:widget-settings>
                      <b:includable id='main'>
                        <div class='widget-content'>
                          <b:if cond='data:content != &quot;&quot;'>
                            <data:content/>
                          
                            <b:else/>
                            <!--[ Blank ad ]-->
                            <div class='adB h240' expr:data-text='data:messages.adsGoHere + &quot; (&quot; + data:title + &quot;)&quot;'/>
                          </b:if>
                        </div>
                        <!--[ Recurring Ads after every 4th paragraph ]-->
                        <script>/*<![CDATA[*/ recurAds('p', {ads:'HTML04', every:4}) /*]]>*/</script>
                      </b:includable>
                    </b:widget>
                  </b:section>
                
                  <b:section cond='!data:view.isPreview and !data:view.isSingleItem' id='add-widget' showaddelement='true'>
                    <b:widget cond='!data:view.isPreview' id='HTML94' locked='true' title='Ad Placement' type='HTML' version='2' visible='true'>
                      <b:widget-settings>
                        <b:widget-setting name='content'/>
                      </b:widget-settings>
                      <b:includable id='main'>
                        <div class='widget-content'>
                          <b:if cond='data:content != &quot;&quot;'>
                            <data:content/>
                          
                            <b:else/>
                            <!--[ Blank ad ]-->
                            <div class='adB' expr:data-text='data:messages.adsGoHere + &quot; (&quot; + data:title + &quot;)&quot;'/>
                          </b:if>
                        </div>
                      </b:includable>
                    </b:widget>
                  </b:section>
                </main>
                
                <b:if cond='data:view.isHomepage or data:view.isPost'>
                  <!--[ Sidebar content ]-->
                  <b:tag class='blogItm sidebar' name='aside'>
                    <b:tag class='sideIn' name='div'>
                      <b:section id='side-widget' showaddelement='true'>
                        <b:widget id='PopularPosts00' locked='false' title='Popular Posts' type='PopularPosts' version='2' visible='true'>
                          <b:widget-settings>
                            <b:widget-setting name='numItemsToShow'>5</b:widget-setting>
                            <b:widget-setting name='showThumbnails'>true</b:widget-setting>
                            <b:widget-setting name='showSnippets'>true</b:widget-setting>
                            <b:widget-setting name='timeRange'>LAST_WEEK</b:widget-setting>
                          </b:widget-settings>
                          <b:includable id='main' var='this'>
                            <b:if cond='data:title != &quot;&quot;'>
                              <h2 class='title dt'><data:title/></h2>
                            </b:if>
                            <b:include name='snippetedPosts'/>
                          </b:includable>
                          <b:includable id='blogThisShare'/>
                          <b:includable id='bylineByName' var='byline'/>
                          <b:includable id='bylineRegion' var='regionItms'/>
                          <b:includable id='commentsLink'/>
                          <b:includable id='commentsLinkIframe'/>
                          <b:includable id='emailPostIcon'/>
                          <b:includable id='facebookShare'/>
                          <b:includable id='footerBylines'/>
                          <b:includable id='googlePlusShare'/>
                          <b:includable id='headerByline'/>
                          <b:includable id='linkShare'/>
                          <b:includable id='otherSharingButton'/>
                          <b:includable id='platformShare'/>
                          <b:includable id='postAuthor'/>
                          <b:includable id='postCommentsLink'/>
                          <b:includable id='postInfo'>
                            <div class='iInf pSml'>
                              <b:if cond='data:post.labels any (label =&gt; label.name in [ &quot;Sponsored&quot; ])'>
                                <b:include name='postLabelSponsored'/>
                                <b:else/>
                                
                                <!--[ Post timestamp ]-->
                                <b:include cond='data:widgets.Blog.first.allBylineItems.timestamp and data:post.labels none (label =&gt; label.name in [ &quot;Product&quot; ])' name='postTimestamp'/>
                                <b:include cond='data:post.labels' name='postLabel'/>
                              </b:if>
                            </div>
                          </b:includable>
                          <b:includable id='postJumpLink' var='post'/>
                          <b:includable id='postLabels'/>
                          <b:includable id='postLocation'/>
                          <b:includable id='postReactions'/>
                          <b:includable id='postShareButtons'/>
                          <b:includable id='postTimestamp'>
                            <time class='aTtmp iTtmp pbl' expr:data-text='format(data:post.date, &quot;MMM d&quot;)' expr:datetime='data:post.date.iso8601' expr:title='&quot;Published: &quot; + data:post.date format &quot;MMMM d, YYYY&quot;'/>
                          </b:includable>
                          <b:includable id='sharingButton'/>
                          <b:includable id='sharingButtonContent'/>
                          <b:includable id='sharingButtons'/>
                          <b:includable id='sharingButtonsMenu'/>
                          <b:includable id='sharingPlatformIcon'/>
                          <b:includable id='snippetedPostByline'/>
                          <b:includable id='snippetedPostContent'>
                            <!--[ Post thumbnail ]-->
                            <b:include cond='data:i == 0 and (data:this.postDisplay.showFeaturedImage and data:post.featuredImage)' name='snippetedPostThumbnail'/>
                      
                            <b:include name='postInfo'/>
                              
                            <div class='iCtnt'>
                              <div class='iInr'>
                                <b:include cond='data:this.postDisplay.showTitle' name='snippetedPostTitle'/>
                                <b:include cond='data:this.postDisplay.showSnippet and data:i == 0' name='snippetedPostEntry'/>
                              </div>
                            </div>
                          </b:includable>
                          <b:includable id='snippetedPostEntry'>
                            <div class='pSnpt'>
                              <b:class cond='data:post.labels any (label =&gt; label.name in [ &quot;Product&quot; ])' name='nTag'/>
                              <b:include name='postEntrySnippet'/>
                            </div>
                          </b:includable>
                          <b:includable id='snippetedPostThumbnail'>
                            <div class='iThmb pThmb'>
                              <b:class cond='data:post.featuredImage.isYoutube' name='iyt'/>
                              <b:class cond='!data:this.postDisplay.showFeaturedImage or !data:post.featuredImage' name='nul'/>
                              <b:tag class='thmb' expr:name='data:this.postDisplay.showFeaturedImage and data:post.featuredImage ? &quot;a&quot; : &quot;div&quot;'>
                                <b:attr cond='data:this.postDisplay.showFeaturedImage and data:post.featuredImage' expr:value='data:post.url' name='href'/>
                                <b:if cond='data:this.postDisplay.showFeaturedImage and data:post.featuredImage'>
                                  <b:include name='postEntryThumbnail'/>
                                  <b:else/>
                                  <span class='imgThm' data-text='No image'/>
                                </b:if>
                              </b:tag>
                              
                              <!--[ Comments count ]-->
                              <div class='iFxd'>
                                <b:if cond='data:post.allowComments and data:post.numberOfComments &gt; 0 or data:post.labels any (label =&gt; label.name in [ &quot;Sponsored&quot; , &quot;Product&quot; ])' name='div'>
                                  <b:if cond='data:post.labels any (label =&gt; label.name in [ &quot;Sponsored&quot; ])'>
                                    <b:include name='postSponsored'/>
                                    <b:elseif cond='data:post.labels any (label =&gt; label.name in [ &quot;Product&quot; ])'/>
                                    <b:include name='postProduct'/>
                                  </b:if>
                                  <b:if cond='data:widgets.Blog.first.allBylineItems.comments and data:post.labels none (label =&gt; label.name in [ &quot;Product&quot; ])'>
                                    <b:include name='postCommentsLinks'/>
                                  </b:if>
                                </b:if>
                            
                                <!--[ Post Views ]-->
                                <span class='pV hidden pu-views' expr:data-id='data:post.id'><b:include name='eye-icon'/></span>
                                
                                <!--[ Post Bookmark ]-->
                                <b:include name='thmb-bookmark'/>
                              </div>
                              
                              <div class='iFxd bl'>
                                <!--[ Post Author ]-->
                                <b:include name='thmb-athr'/>
                              </div>
                          
                              <!--[ Quick Edit ]-->
                              <b:include name='thmb-quickedit'/>
                            </div>
                          </b:includable>
                          <b:includable id='snippetedPostTitle'>
                            <h3 class='iTtl aTtl'><a expr:href='data:post.url.canonical'><data:post.title.escaped/></a></h3>
                          </b:includable>
                          <b:includable id='snippetedPosts'>
                            <div class='itemPp' role='feed'>
                              <!-- Don't render the post that we're currently already looking at. -->
                              <b:loop index='i' values='data:posts filter (p =&gt; p.id != data:view.postId)' var='post'>
                                <article class='itm'>
                                  <b:class cond='data:i == 0 and data:this.postDisplay.showFeaturedImage and !data:post.featuredImage' name='nThmb'/>
                                  <b:class cond='data:i == 0 and data:this.postDisplay.showFeaturedImage and data:post.featuredImage' name='mostP'/>
                                  <b:class cond='data:post.labels any (label =&gt; label.name in [ &quot;Product&quot; ])' name='pTag'/>
                                  <b:include name='snippetedPostContent'/>
                                </article>
                              </b:loop>
                            </div>
                          </b:includable>
                        </b:widget>
                        <b:widget id='Label00' locked='false' title='Labels' type='Label' version='2' visible='true'>
                          <b:widget-settings>
                            <b:widget-setting name='sorting'>ALPHA</b:widget-setting>
                            <b:widget-setting name='display'>LIST</b:widget-setting>
                            <b:widget-setting name='selectedLabelsList'/>
                            <b:widget-setting name='showType'>ALL</b:widget-setting>
                            <b:widget-setting name='showFreqNumbers'>true</b:widget-setting>
                          </b:widget-settings>
                          <b:includable id='main' var='this'>
                            <b:if cond='data:title != &quot;&quot;'>
                              <h2 class='title dt'><data:title/></h2>
                            </b:if>
                            <b:include name='content'/>
                          </b:includable>
                          <b:includable id='cloud'>
                            <b:loop index='tags' values='data:labels' var='label'>
                         
                              <!--[ Only show 6 label ]-->
                              <b:if cond='data:tags &lt;= 5'>
                                <div class='lbSz'>
                                  <b:class expr:name='&quot;s-&quot; + data:label.cssSize'/>
                                  <b:tag class='lbN' expr:name='data:blog.url != data:label.url ? &quot;a&quot; : &quot;div&quot;'>
                                    <b:attr cond='data:blog.url != data:label.url' expr:value='data:label.url' name='href'/>
                                    <b:attr cond='data:blog.url != data:label.url' expr:value='data:label.name' name='aria-label'/>
                                    <span class='lbT'><data:label.name/></span>
                                    <b:tag class='lbC' cond='data:this.showFreqNumbers' expr:data-text='&quot;(&quot; + data:label.count + &quot;)&quot;' name='span'/>
                                  </b:tag>
                                </div>
                              </b:if>
                        
                              <!--[ Hide another label from 7th list ]-->
                              <b:if cond='data:tags == 6'>
                                <div class='lbSh'>
                                  <input class='lbIn hidden' id='lbAl-1' type='checkbox'/>
                                  <div class='lbAl'>
                                    <b:loop index='alltags' values='data:labels' var='label'>
                               
                                      <!--[ Show label from 7th list ]-->
                                      <b:if cond='data:alltags &gt;= 6'>
                                        <div class='lbSz'>
                                          <b:class expr:name='&quot;s-&quot; + data:label.cssSize'/>
                                          <b:tag class='lbN' expr:name='data:blog.url != data:label.url ? &quot;a&quot; : &quot;div&quot;'>
                                            <b:attr cond='data:blog.url != data:label.url' expr:value='data:label.url' name='href'/>
                                            <b:attr cond='data:blog.url != data:label.url' expr:value='data:label.name' name='aria-label'/>
                                            <span class='lbT'><data:label.name/></span>
                                            <b:tag class='lbC' cond='data:this.showFreqNumbers' expr:data-text='&quot;(&quot; + data:label.count + &quot;)&quot;' name='span'/>
                                          </b:tag>
                                        </div>
                                      </b:if>
                                
                                    </b:loop>
                                  </div>
                            
                                  <label aria-label='Show labels' class='lbM' expr:data-hide='data:messages.showLess' expr:data-show='data:messages.showMore' expr:data-text='&quot;(+&quot; + (data:labels.length - 6) + &quot;)&quot;' for='lbAl-1'/>
                                </div>
                              </b:if>
                            </b:loop>
                          </b:includable>
                          <b:includable id='content'>
                            <div class='wL pSml'>
                              <!--[ delete the comment tag below if you want to change style Label List ]-->
                              <b:class name='bg'/>
                                
                              <b:class expr:name='data:this.display == &quot;list&quot; ? &quot;ls&quot; : &quot;cl&quot;'/>
                              <b:include cond='data:this.display == &quot;list&quot;' name='list'/>
                              <b:include cond='data:this.display == &quot;cloud&quot;' name='cloud'/>
                            </div>
                          </b:includable>
                          <b:includable id='list'>
                            <ul>
                              <b:loop index='tags' values='data:labels' var='label'>
                              
                                <!--[ Only show 6 label ]-->
                                <b:if cond='data:tags &lt;= 5'>
                                  <li>
                                    <b:tag class='lbN' expr:name='data:blog.url != data:label.url ? &quot;a&quot; : &quot;div&quot;'>
                                      <b:attr cond='data:blog.url != data:label.url' expr:value='data:label.url' name='href'/>
                                      <b:attr cond='data:blog.url != data:label.url' expr:value='data:label.name' name='aria-label'/>
                                      <span class='lbT'><data:label.name/></span>
                                      <span class='lbR'>
                                        <b:tag class='lbC' cond='data:this.showFreqNumbers' expr:data-text='data:label.count' name='span'/>
                                        <b:include name='bookmark-icon'/>
                                      </span>
                                    </b:tag>
                                  </li>
                                </b:if>
                          
                              </b:loop>
                            </ul>
                              
                            <b:loop index='tags' values='data:labels' var='label'>
                              <!--[ Hide another label from 7th list ]-->
                              <b:if cond='data:tags == 6'>
                                <div class='lbSh'>
                                  <input class='lbIn hidden' id='lbAl-2' type='checkbox'/>
                                  <ul class='lbAl'>
                                    <b:loop index='alltags' values='data:labels' var='label'>
                                      
                                      <!--[ Show label from 7th list ]-->
                                      <b:if cond='data:alltags &gt;= 6'>
                                        <li>
                                          <b:tag class='lbN' expr:name='data:blog.url != data:label.url ? &quot;a&quot; : &quot;div&quot;'>
                                            <b:attr cond='data:blog.url != data:label.url' expr:value='data:label.url' name='href'/>
                                            <b:attr cond='data:blog.url != data:label.url' expr:value='data:label.name' name='aria-label'/>
                                            <span class='lbT'><data:label.name/></span>
                                            <span class='lbR'>
                                              <b:tag class='lbC' cond='data:this.showFreqNumbers' expr:data-text='data:label.count' name='span'/>
                                              <b:include name='bookmark-icon'/>
                                            </span>
                                          </b:tag>
                                        </li>
                                      </b:if>
                                  
                                    </b:loop>
                                  </ul>
                              
                                  <label aria-label='Show labels' class='lbM' expr:data-hide='data:messages.showLess' expr:data-show='data:messages.showMore' expr:data-text='&quot;(+&quot; + (data:labels.length - 6) + &quot;)&quot;' for='lbAl-2' role='button'/>
                                </div>
                              </b:if>
                            </b:loop>
                          </b:includable>
                        </b:widget>
                        <b:widget cond='data:view.isPost' id='HTML11' locked='false' title='Table of contents' type='HTML' version='2' visible='true'>
                          <b:widget-settings>
                            <b:widget-setting name='content'><![CDATA[<!--[ This is an automatic Table of Content feature that only appears on post pages ]-->]]></b:widget-setting>
                          </b:widget-settings>
                          <b:includable id='main'>
                            <input class='tocI hidden' id='forTocJs' type='checkbox'/>
                            <div class='tocL'>
                              <div class='tocLi'>
                                <div class='tocLs'>
                                  <label aria-label='Close' class='tocH fixH' for='forTocJs'>
                                    <div class='tocC'>
                                      <svg class='rad' viewBox='0 0 160 160'><path d='M0-10,150,0l10,150S137.643,80.734,100.143,43.234,0-10,0-10Z' transform='translate(0 10)'/></svg>
                                      <span><b:include name='document-icon'/></span>
                                      <svg class='rad in' viewBox='0 0 160 160'><path d='M0-10,150,0l10,150S137.643,80.734,100.143,43.234,0-10,0-10Z' transform='translate(0 10)'/></svg>
                                    </div>
                                    <div class='tocT fixT'>
                                      <b:attr expr:value='data:title != &quot;&quot; ? data:title : &quot;Contents&quot;' name='data-text'/>
                                      <span class='c cl' data-texxt='Close'/>
                                    </div>
                                  </label>
                                  <div class='tocIn' id='postToc'/>
                                </div>
                              </div>
                              <label class='fCls' for='forTocJs'/>
                            </div>
                          </b:includable>
                        </b:widget>
                      </b:section>
              
                      <!--[ Sidebar sticky ]-->
                      <b:section class='sideSticky' cond='data:view.isLayoutMode or (!data:view.isPreview and !data:blog.isMobileRequest)' id='side-sticky' showaddelement='true'>
                        <b:widget id='HTML95' locked='true' title='Sticky Ad - Desktop only' type='HTML' version='2' visible='true'>
                          <b:widget-settings>
                            <b:widget-setting name='content'/>
                          </b:widget-settings>
                          <b:includable id='main'>
                            <b:if cond='data:content != &quot;&quot;'>
                              <data:content/>
                          
                              <b:else/>
                              <!--[ Blank ad ]-->
                              <div class='adB h240' expr:data-text='data:messages.adsGoHere + &quot; (&quot; + data:title + &quot;)&quot;'/>
                            </b:if>
                          </b:includable>
                        </b:widget>
                      </b:section>
                    </b:tag>
                  </b:tag>
                </b:if>
              </b:tag>
              
              <!--[ Mobile Menu ]-->
              <b:section class='mobMn' id='mobile-menu' maxwidgets='1' showaddelement='false'>
                <b:widget id='TextList99' locked='true' title='Mobile Menu' type='TextList' version='2' visible='true'>
                  <b:widget-settings>
                    <b:widget-setting name='shownum'>7</b:widget-setting>
                    <b:widget-setting name='item-6'>Comments</b:widget-setting>
                    <b:widget-setting name='item-5'>Share</b:widget-setting>
                    <b:widget-setting name='item-4'>Top</b:widget-setting>
                    <b:widget-setting name='item-3'>Dark</b:widget-setting>
                    <b:widget-setting name='sorting'>NONE</b:widget-setting>
                    <b:widget-setting name='item-2'>Menu</b:widget-setting>
                    <b:widget-setting name='item-1'>Search</b:widget-setting>
                    <b:widget-setting name='item-0'>Home</b:widget-setting>
                  </b:widget-settings>
                  <b:includable id='main'>
                    <b:include name='content'/>
                  </b:includable>
                  <b:includable id='content'>
                    <ul>
                      
                      <b:loop index='m' values='data:items' var='item'>
                        <b:if cond='data:m &lt;= 6'>
                          <b:if cond='data:item == &quot;Home&quot;'>
                            <li class='mH'>
                              <b:class cond='data:view.isHomepage' name='nmH'/>
                              <b:tag expr:aria-label='data:item' expr:data-text='data:item' expr:name='data:view.url == data:blog.homepageUrl ? &quot;span&quot; : &quot;a&quot;' role='button'>
                                <b:attr cond='data:view.url != data:blog.homepageUrl' expr:value='data:blog.homepageUrl.canonical' name='href'/>
                                <b:attr cond='data:view.url != data:blog.homepageUrl' name='role' value='button'/>
                                <b:include name='home-icon'/>
                              </b:tag>
                            </li>
                            
                            <b:elseif cond='data:item == &quot;Search&quot;'/>
                            <li class='mS'>
                              <label expr:aria-label='data:item' expr:data-text='data:item' for='searchIn'><b:include name='search-icon'/></label>
                            </li>
                            
                            <b:elseif cond='data:item == &quot;Menu&quot;'/>
                            <li class='mN'>
                              <label expr:aria-label='data:item' expr:data-text='data:item' for='offNav'><b:include name='menu-icon'/></label>
                            </li>
                            
                            <b:elseif cond='data:item == &quot;Dark&quot;'/>
                            <b:if cond='!data:view.isPost'>
                              <li class='mD'>
                                <span aria-label='Mode' class='tDL' data-light='Light' expr:data-text='data:item' onclick='darkMode()' role='button'><b:include name='moon-sun-icon'/></span>
                              </li>
                            </b:if>
                            
                            <b:elseif cond='data:item == &quot;Light&quot;'/>
                            <b:if cond='!data:view.isPost'>
                              <li class='mL'>
                                <span aria-label='Mode' class='tDL' data-light='Dark' expr:data-text='data:item' onclick='darkMode()' role='button'><b:include name='sun-moon-icon'/></span>
                              </li>
                            </b:if>
                            
                            <b:elseif cond='data:item == &quot;Top&quot;'/>
                            <b:if cond='data:view.isHomepage'>
                              <li class='mS'>
                                <span expr:aria-label='data:item' expr:data-text='data:item' onclick='window.scrollTo({top: 0});' role='button'><b:include name='arow-up-circle-icon'/></span>
                              </li>
                            </b:if>
                            
                            <b:elseif cond='data:item == &quot;Share&quot;'/>
                            <b:if cond='data:view.isPost'>
                              <li class='mS'>
                                <label expr:aria-label='data:item' expr:data-text='data:item' for='forShare'><b:include name='share-alt-icon'/></label>
                              </li>
                            </b:if>
                            
                            <b:elseif cond='data:item == &quot;Comments&quot;'/>
                            <b:if cond='data:view.isPost'>
                              <li class='mC'>
                                <label expr:aria-label='data:item' expr:data-text='data:item' for='forComments'><b:include name='messages-icon'/></label>
                              </li>
                            </b:if>
                            
                          </b:if>
                        </b:if>
                      </b:loop>
                    </ul>
                  </b:includable>
                </b:widget>
              </b:section>
        
              <!--[ Footer section ]-->
              <footer>
                <div class='footer'>
                  <div class='fotIn'>
                    <b:section id='footer-widget-1' maxwidgets='2' showaddelement='true'>
                      <b:widget id='HTML77' locked='false' title='Made with coffee by' type='HTML' version='2' visible='true'>
                        <b:widget-settings>
                          <b:widget-setting name='content'>&lt;div&gt;&lt;div class=&#39;lazy abtL&#39; data-style=&#39;background-image:url(https://cdn.jsdelivr.net/gh/ngylduy/images/icon-192-ios.png)&#39;&gt;&lt;/div&gt;&lt;div class=&#39;abtT&#39;&gt;&lt;h2 class=&#39;tl&#39;&gt;0286 Team&lt;/h2&gt;&lt;p class=&#39;abtD&#39;&gt;Good design makes the ordinary extraordinary.&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;
&lt;p class=&#39;hidden pu-views&#39; data-id=&#39;WebsiteStats&#39;&gt;&lt;/p&gt;</b:widget-setting>
                        </b:widget-settings>
                        <b:includable id='main'>
                          <div class='widget-content abtU' expr:data-text='data:title'><data:content/></div>
                        </b:includable>
                      </b:widget>
                      <b:widget id='LinkList1' locked='false' title='Social Media Link' type='LinkList' version='2' visible='true'>
                        <b:widget-settings>
                          <b:widget-setting name='link-3'>#</b:widget-setting>
                          <b:widget-setting name='sorting'>NONE</b:widget-setting>
                          <b:widget-setting name='link-4'>#</b:widget-setting>
                          <b:widget-setting name='text-1'>Instagram</b:widget-setting>
                          <b:widget-setting name='link-1'>#</b:widget-setting>
                          <b:widget-setting name='text-0'>Facebook</b:widget-setting>
                          <b:widget-setting name='link-2'>#</b:widget-setting>
                          <b:widget-setting name='text-3'>Youtube</b:widget-setting>
                          <b:widget-setting name='link-0'>#</b:widget-setting>
                          <b:widget-setting name='text-2'>Twitter</b:widget-setting>
                          <b:widget-setting name='text-4'>LinkedIn</b:widget-setting>
                        </b:widget-settings>
                        <b:includable id='main'>
                          <b:include name='content'/>
                        </b:includable>
                        <b:includable id='content'>                    
                          <ul class='sL'>                      
                            <b:loop values='data:links' var='link'>
                              <li>
                                <b:tag class='l' expr:data-text='data:link.name' expr:name='data:link.target != &quot;#&quot; ? &quot;a&quot; : &quot;span&quot;'>
                                  <b:attr cond='data:link.target != &quot;#&quot;' expr:value='data:link.target' name='href'/>
                                  <b:attr cond='data:link.target != &quot;#&quot;' expr:value='data:link.name' name='aria-label'/>
                                  <b:attr cond='data:link.target != &quot;#&quot;' name='target' value='_blank'/>
                                  <b:attr cond='data:link.target != &quot;#&quot;' name='rel' value='noopener'/>
                                </b:tag>
                              </li>
                            </b:loop>
                          </ul>
                        </b:includable>
                      </b:widget>
                    </b:section>
                    <b:section id='footer-widget-2' maxwidgets='2' showaddelement='true'>
                      <b:widget id='LinkList2' locked='false' title='Company' type='LinkList' version='2' visible='true'>
                        <b:widget-settings>
                          <b:widget-setting name='sorting'>NONE</b:widget-setting>
                          <b:widget-setting name='text-1'>Blog</b:widget-setting>
                          <b:widget-setting name='link-1'>#</b:widget-setting>
                          <b:widget-setting name='text-0'>About</b:widget-setting>
                          <b:widget-setting name='link-0'>#</b:widget-setting>
                        </b:widget-settings>
                        <b:includable id='main'>
                          <b:include name='widget-title'/>
                          <b:include name='content'/>
                        </b:includable>
                        <b:includable id='content'>
                          <div class='widget-content'>
                            <ul>
                              <b:loop values='data:links' var='link'>
                                <li>
                                  <a expr:href='data:link.target'>
                                    <span><data:link.name/></span>
                                  </a>
                                </li>
                              </b:loop>
                            </ul>
                          </div>
                        </b:includable>
                      </b:widget>
                    </b:section>
                    <b:section id='footer-widget-3' maxwidgets='2' showaddelement='true'>
                      <b:widget id='LinkList3' locked='false' title='Product &amp;amp; Service' type='LinkList' version='2' visible='true'>
                        <b:widget-settings>
                          <b:widget-setting name='sorting'>NONE</b:widget-setting>
                          <b:widget-setting name='text-1'>Other Product</b:widget-setting>
                          <b:widget-setting name='link-1'>#</b:widget-setting>
                          <b:widget-setting name='text-0'>Blogger Theme</b:widget-setting>
                          <b:widget-setting name='link-0'>#</b:widget-setting>
                        </b:widget-settings>
                        <b:includable id='main'>
                          <b:include name='widget-title'/>
                          <b:include name='content'/>
                        </b:includable>
                        <b:includable id='content'>
                          <div class='widget-content'>
                            <ul>
                              <b:loop values='data:links' var='link'>
                                <li>
                                  <a expr:href='data:link.target'>
                                    <span><data:link.name/></span>
                                  </a>
                                </li>
                              </b:loop>
                            </ul>
                          </div>
                        </b:includable>
                      </b:widget>
                    </b:section>
                    <b:section id='footer-widget-4' maxwidgets='2' showaddelement='true'>
                      <b:widget id='LinkList4' locked='false' title='Support' type='LinkList' version='2' visible='true'>
                        <b:widget-settings>
                          <b:widget-setting name='sorting'>NONE</b:widget-setting>
                          <b:widget-setting name='text-1'>Documentation</b:widget-setting>
                          <b:widget-setting name='link-1'>#</b:widget-setting>
                          <b:widget-setting name='text-0'>Contact</b:widget-setting>
                          <b:widget-setting name='link-0'>#</b:widget-setting>
                        </b:widget-settings>
                        <b:includable id='main'>
                          <b:include name='widget-title'/>
                          <b:include name='content'/>
                        </b:includable>
                        <b:includable id='content'>
                          <div class='widget-content'>
                            <ul>
                              <b:loop values='data:links' var='link'>
                                <li>
                                  <a expr:href='data:link.target'>
                                    <span><data:link.name/></span>
                                  </a>
                                </li>
                              </b:loop>
                            </ul>
                          </div>
                        </b:includable>
                      </b:widget>
                    </b:section>
                  </div>
            
                  <!--[ Credit ]-->
                  <b:section class='cdtIn' id='credit-widget' maxwidgets='2' showaddelement='false'>
                    <b:widget id='HTML88' locked='true' title='Credit' type='HTML' version='2' visible='true'>
                      <b:widget-settings>
                        <b:widget-setting name='content'/>
                      </b:widget-settings>
                      <b:includable id='main'>
                        <div class='fotCd'>
                          <span class='credit'>
                            <b:if cond='data:content == &quot;&quot;'><span style='font-family:Arial, Helvetica, sans-serif'>&#169;</span> <span id='getYear'><b:if cond='data:view.url != data:view.url params { amp: &quot;1&quot; }'><script>/*<![CDATA[*/ var d = new Date(); var n = d.getFullYear(); document.getElementById('getYear').innerHTML = n; /*]]>*/</script><b:else/>2022</b:if></span> &#8231; <bdi><a expr:href='data:blog.homepageUrl.canonical'><data:blog.title/><b:include name='verified-icon'/></a></bdi> &#8231; All rights reserved.<b:else/><data:content/></b:if>
                          </span>
                          <span class='creator'>Powered By <a href='https://www.Dr-FarFar.com'>Dr.FarFar</a></span>
                        </div>
                      </b:includable>
                    </b:widget>
                    <b:widget id='TextList88' locked='true' title='Back to top' type='TextList' version='2' visible='true'>
                      <b:widget-settings>
                        <b:widget-setting name='shownum'>1</b:widget-setting>
                        <b:widget-setting name='sorting'>NONE</b:widget-setting>
                        <b:widget-setting name='item-1'>Top</b:widget-setting>
                        <b:widget-setting name='item-0'>Top Sticky</b:widget-setting>
                      </b:widget-settings>
                      <b:includable id='main'>
                        <b:include name='content'/>
                      </b:includable>
                      <b:includable id='content'>
                        <b:loop index='t' values='data:items' var='item'>
                          <b:if cond='data:t &lt;= 0'>
                            <b:if cond='data:item == &quot;Top&quot;'>
                              <!--[ Back top button ]-->
                              <b:tag class='toTop tTop' expr:data-text='data:item' expr:name='data:view.url != data:view.url params { amp: &quot;1&quot; } ? &quot;div&quot; : &quot;a&quot;'>
                                <b:attr cond='data:view.url != data:view.url params { amp: &quot;1&quot; }' name='onclick' value='window.scrollTo({top: 0});'/>
                                <b:attr cond='data:view.url == data:view.url params { amp: &quot;1&quot; }' name='href' value='#mainCont'/>
                                <b:include name='arow-up-icon'/>
                              </b:tag>
                        
                              <b:elseif cond='data:item == &quot;Top Sticky&quot;'/>
                              <!--[ Back top button fixed/sticky ]-->
                              <b:include name='button-backtoTop'/>
                            </b:if>
                          </b:if>
                        </b:loop>
                      </b:includable>
                    </b:widget>
                  </b:section>
                </div>
              </footer>
            </div>
          </div> 
        </div>
        
        <!--[ Fixed Back to Top ]-->
        <b:include name='button-backtoTop'/>

        <!--[ Waves Animation ]-->
        <div class='wvC'><div class='wvS'><svg class='waves' preserveAspectRatio='none' shape-rendering='auto' viewBox='0 24 150 28'><defs><path d='M-160 44c30 0 58-18 88-18s 58 18 88 18 58-18 88-18 58 18 88 18 v44h-352z' id='wave-bg'/></defs><g class='plx'><use x='48' xlink:href='#wave-bg' y='0'/><use x='48' xlink:href='#wave-bg' y='3'/><use x='48' xlink:href='#wave-bg' y='5'/><use x='48' xlink:href='#wave-bg' y='7'/></g></svg></div><div class='wvH'/></div>
        
        <b:if cond='!data:view.isPreview'>
          <!--[ Delete 'or data:blog.isMobileRequest' if you want to show ad in both(desktop and mobile) ]-->
          <b:section cond='data:view.isLayoutMode or data:blog.isMobileRequest' id='anchor-ad' showaddelement='false'>
            <b:widget id='HTML99' locked='true' title='Anchor Ad - Mobile only' type='HTML' version='2' visible='false'>
              <b:widget-settings>
                <b:widget-setting name='content'/>
              </b:widget-settings>
              <b:includable id='main'>
                <!--[ Sticky ad ]-->
                <input class='ancrI hidden' id='ancrI' type='checkbox'/>
                <div class='ancrA'>
                  <label aria-label='Close Menu' class='ancrC' for='ancrI'><svg viewBox='0 0 24 24'><polyline points='5 9 12 16 19 9'/></svg></label>
                  <div class='ancrCn'>
                    <b:if cond='data:content != &quot;&quot;'>
                      <data:content/>
                          
                      <b:else/>
                      <!--[ Blank ad ]-->
                      <div class='adB' expr:data-text='data:messages.adsGoHere + &quot; (&quot; + data:title + &quot;)&quot;'/>
                    </b:if>
                  </div>
                </div>
              </b:includable>
            </b:widget>
          </b:section>
        </b:if>
        
      </b:if>
    </b:tag>
    
    <b:tag class='erroP' cond='data:view.isLayoutMode or data:view.isError' name='section'>
      <b:section class='erroC' cond='data:view.isLayoutMode or data:view.isError' id='error-404' showaddelement='false'>
        <b:widget id='HTML404' locked='true' title='Take me back' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='content'>&lt;h3&gt;&lt;span class=&#39;e&#39; title=&#39;404&#39;&gt;404&lt;/span&gt;&lt;span&gt;Something Wrong!&lt;/span&gt;&lt;/h3&gt;

&lt;p&gt;The page you&#39;ve requested can&#39;t be found. Why don&#39;t you browse around?&lt;/p&gt;</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <data:content/>
            <a class='button' expr:href='data:blog.homepageUrl.canonical'><data:title/></a>
          </b:includable>
        </b:widget>
      </b:section>
    </b:tag>
    
    <!--[ Addons ]-->
    <b:tag class='addonsWidgets' cond='data:view.isLayoutMode' name='div'>
      <b:section cond='!data:view.isError' id='addons-widget' showaddelement='false'>
        <b:widget cond='!data:view.isPreview' id='HTML21' locked='true' title='Cookie Consent' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='content'>We serve cookies on this site to analyze traffic, remember your preferences, and optimize your experience.</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <!--[ Cookies Consent ]-->
            <div class='ckW' id='ckWrap'><div class='ckA'><div class='ckH'><data:title/></div><div class='ckD'><data:content/></div></div><div class='ckF'><button class='ckB' id='ckAccept'>Accept</button><a class='ckB' href='https://policies.google.com/technologies/cookies' target='_blank' title='Cookie Policy'>More Details</a></div></div>
          </b:includable>
        </b:widget>
        <b:widget cond='!data:view.isPreview' id='HTML22' locked='true' title='Oops!' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='content'>It seems there is something wrong with your internet connection. Please connect to the internet and start browsing again.</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <!--[ No Internet ]-->
            <div class='fxPu' id='noInternet'><div class='fxPuW'><div class='fxPuC'><div class='fxPuS'><b:include name='wifioff-icon'/></div><div class='fxPuH'><data:title/></div><div class='fxPuD'><data:content/></div><div class='fxPuB'><a class='btn' expr:href='data:blog.url.canonical'><svg class='line' viewBox='0 0 24 24'><polyline points='23 4 23 10 17 10'/><path d='M20.49 15a9 9 0 1 1-2.12-9.36L23 10'/></svg></a></div></div></div></div>
          </b:includable>
        </b:widget>
        <b:widget cond='!data:view.isPreview and !data:view.isHomepage' id='HTML23' locked='true' title='AdBlock Detected!' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='content'><![CDATA[We have detected that you are using adblocking plugin in your browser.<br />The revenue we earn by the advertisements is used to manage this website, we request you to whitelist our website in your adblocking plugin.]]></b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <!--[ Anti AdBlock ]-->
            <div class='fxPu' id='antiAdB'><div class='fxPuW'><div class='fxPuC'><div class='fxPuCl hidden'/><div class='fxPuS'><b:include name='alert-circle-icon'/></div><div class='fxPuH'><data:title/></div><div class='fxPuD'><data:content/></div></div></div></div>
          </b:includable>
        </b:widget>
        <b:widget cond='!data:view.isPreview' id='HTML25' locked='true' title='Enable JavaScript!' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='content'>Plus UI works well with JavaScript enabled. Please enable JavaScript to continue browsing.</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <!--[ No Script ]-->
            <noscript><div class='fxPu visible'><div class='fxPuW'><div class='fxPuC'><div class='fxPuS'><svg class='line' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'><circle cx='12' cy='12' r='10'/><line x1='12' x2='12' y1='8' y2='12'/><line x1='12' x2='12.01' y1='16' y2='16'/></svg></div><div class='fxPuH'><data:title/></div><div class='fxPuD'><data:content/></div><div class='fxPuB'><button class='btn'><svg class='line' viewBox='0 0 24 24'><polyline points='23 4 23 10 17 10'/><path d='M20.49 15a9 9 0 1 1-2.12-9.36L23 10'/></svg></button></div></div></div></div></noscript>
          </b:includable>
        </b:widget>
        <b:widget cond='!data:view.isPreview' id='HTML26' locked='true' title='Country Block' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='content'/>
          </b:widget-settings>
          <b:includable id='main'>
            <!--[ Country Block Pop-Up ]-->
            <div class='fxPu' id='cntryBlk'><div class='fxPuW'><div class='fxPuC'><div class='fxPuS'><b:include name='alert-circle-icon'/></div><div class='fxPuH'>Site is Blocked</div><div class='fxPuD'>Sorry! This site is not available in your country.</div></div></div></div>
            <script> function checkCntry(){var dtctCntry=(Intl.DateTimeFormat().resolvedOptions().timeZone);var blkCntry=&#39;<data:content/>&#39;;console.log(&#39;Time Zone: &#39;+dtctCntry);if(dtctCntry===blkCntry){setInterval(function(){addCt(getid(&#39;cntryBlk&#39;),&#39;visible&#39;)},1000);}}checkCntry(); </script>
          </b:includable>
        </b:widget>
      </b:section>
    </b:tag>
    
    <!--[ Javascript disable condition ]-->
    <!--<noscript>
      <input class='nJs hidden' id='forNoJS' type='checkbox'/>
      <div class='noJs' expr:data-text='data:blog.title + &quot; works best with JavaScript enabled&quot;'>
        <label for='forNoJS'/>
      </div>
    </noscript>-->
    
    <b:if cond='!data:view.isError'>
      <!--[ Load More Pagination - Delete this section if you want to disable this feature ]-->
      <b:include cond='data:view.isHomepage or data:view.url == data:blog.homepageUrl.canonical path &quot;search&quot;' name='post-pagination1'/>
      <!--[ Numbered Pagination - Delete this section if you want to disable this feature ]-->
      <!--<b:include cond='!data:view.isSingleItem and !data:blog.isPrivateBlog' name='post-pagination2'/>-->
    </b:if>

    <!--[ additional javascript ]-->
<script>
/*<![CDATA[*/
/* Dark Mode */ function darkMode(){var e=qSel("#mainCont");Pu.sLS("webMode","drK"===Pu.gLS("webMode")?"lgT":"drK"),"drK"===Pu.gLS("webMode")?(e.classList.remove("syD","lgT"),addCt(e,"drK")):(e.classList.remove("drK","syD"),addCt(e,"lgT")),themeColor("themeC")};
/* Header Scroll */ function headScroll(){var e=window.pageYOffset||document.documentElement.scrollTop,d=qSel("#header"),l=qSel("#mobile-menu");20<e?(addCt(d,"stick"),addCt(l,"slide")):(remCt(d,"stick"),remCt(l,"slide"))}window.addEventListener("scroll",headScroll);
/* Private Blog Notif */ "true"==isPrivateBlog&&toastNotif('<i class="check"></i>'+blogTitle+" Blog is Private.");
/* Images */ (function(){var imgU=qSell('img.imgThm, .sImg .im, .cmAv .im, .pIm .im, .admIm .im, .sldC .sldIm');for(var i=0;i<imgU.length;i++){if(imgU[i].getAttribute('data-src')){var uImg=imgU[i].getAttribute('data-src');if((uImg.includes('blogspot')==!0||uImg.includes('googleusercontent')==!0)&&(uImg.includes('-pd')==!0||uImg.includes('-p-k-no-nu')==!0)&&uImg.includes('-rw')==!1){imgU[i].setAttribute('data-src',uImg.replace('-nu','-nu-rw-e30').replace('-pd','-pd-rw-e30'))}}else if(imgU[i].getAttribute('src')){var uImg=imgU[i].getAttribute('src');if((uImg.includes('blogspot')==!0||uImg.includes('googleusercontent')==!0)&&uImg.includes('p-k-no-nu')==!0&&uImg.includes('-rw')==!1){imgU[i].setAttribute('data-src',uImg.replace('-nu','-nu-rw-e30'))}else{imgU[i].setAttribute('data-src',uImg)};addCt(imgU[i],'lazy');imgU[i].setAttribute('src','data:image/png;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=')}else if(imgU[i].getAttribute('data-style')){var uImg=imgU[i].getAttribute('data-style');if((uImg.includes('blogspot')==!0||uImg.includes('googleusercontent')==!0)&&uImg.includes('w60')==!0&&uImg.includes('-rw')==!1){imgU[i].setAttribute('data-style',uImg.replace('w60','w60-rw-e30'))}else if((uImg.includes('blogspot')==!0||uImg.includes('googleusercontent')==!0)&&uImg.includes('p-k-no-nu')==!0&&uImg.includes('-rw')==!1){imgU[i].setAttribute('data-style',uImg.replace('-nu','-nu-rw-e30'))}else if((uImg.includes('=s')==!0||uImg.includes('/s')==!0)&&uImg.includes('-rw')==!1){imgU[i].setAttribute('data-style',uImg.replace(/\/s[0-9]+(\-c)?/,'/s1280-rw-e30').replace(/\=s[0-9]+(\-c)?/,'=s1280-rw-e30'))}}};})();
/* Defer Img */ Defer.dom('.lazy', 100, 'loaded', null, {rootMargin:'1px'}),'undefined'!=typeof infinite_scroll&&infinite_scroll.on('load',function(){Defer.dom('.lazy', 100, 'loaded', null, {rootMargin:'1px'}) });
/* Push Ads - remove if you are adding the push script below ins tag */ pushAds();
/* LAZYLOAD SCRIPTS - DON'T REMOVE FUNCTIONS */
function lazyCustomJs(){
  /* lazy category post */ if(getid('HTML2')!=null){ctgryPst(ctgryLb1, '#HTML2 .ctgry', 6, 600, 200, true);if(getid('HTML3')!=null){ctgryPst(ctgryLb2, '#HTML3 .ctgry', 6, 600, 200, true)}};
  /* YOUR CUSTOM JS */
};
function scrollCustomJs(){
  /* YOUR CUSTOM JS */
};
/*]]>*/
<b:if cond='data:view.isSingleItem'>/*<![CDATA[*/
/* lazy youtube */ !function(){for(var t=qSell(".lazyYt"),e=0;e<t.length;e++){var a="https://img.youtube.com/vi/"+t[e].dataset.embed+"/sddefault.jpg",s=new Image;s.setAttribute("class","lazy"),s.setAttribute("data-src",a),s.setAttribute("src","data:image/png;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs="),s.setAttribute("alt","Youtube video"),s.addEventListener("load",void t[e].appendChild(s)),t[e].addEventListener("click",function(){var t=document.createElement("iframe");t.setAttribute("frameborder","0"),t.setAttribute("allowfullscreen",""),t.setAttribute("src","https://www.youtube.com/embed/"+this.dataset.embed+"?rel=0&showinfo=0&autoplay=1"),this.innerHTML="",this.appendChild(t)})}}();
/* Lightbox image */ for(var imageslazy=qSell(".pS .separator img, .pS .tr-caption-container img, .pS .psImg >img, .pS .btImg >img"),i=0;i<imageslazy.length;i++)imageslazy[i].setAttribute("onclick","return false");function wrap(i,a,t){for(var r=document.querySelectorAll(a),e=0;e<r.length;e++){var m=i+r[e].outerHTML+t;r[e].outerHTML=m}}wrap('<div class="zmImg">',".pS .separator >a","</div>"),wrap('<div class="zmImg">',".pS .tr-caption-container td >a","</div>"),wrap('<div class="zmImg">',".pS .separator >img","</div>"),wrap('<div class="zmImg">',".pS .tr-caption-container td >img","</div>"),wrap('<div class="zmImg">',".pS .psImg img","</div>");for(var containerimg=document.getElementsByClassName("zmImg"),i=0;i<containerimg.length;i++)containerimg[i].onclick=function(){this.classList.toggle("s")};
/*]]>*/</b:if>
</script>
      
    <!--[ Main js ]-->
    <script>
<b:include name='settings-object'/>
/*<![CDATA[*/
/* Template js */ eval(function(p,l,u,s,f,d){while(u--){if(s[u]){p=p.replace(new RegExp('\\b'+u+'\\b','g'),s[u])}}return p}('15 106(11,14){22 17(11- -69,14)}!15(){15 11(11,14){22 17(14-60,11)}36 14=79();15 16(11,14){22 17(11- -397,14)}104(;;)262{215(258==+37(11(105,60))+ -37(11(57,61))/2+37(16(-192,-197))/ 3 + 37(16(-247, -253))/4*(-37(11(24,64))/ 5) + 37(16(-210, -208))/6+ -37(11(96,66))/ 7 * (-37(16(-165, -208))/8)+37(11(108,68))/9)250;14.90(14.209())}249(11){14.90(14.209())}}();248 25=19.23("255");15 264(){15 11(11,14){22 17(11- -275,14)}15 14(11,14){22 17(14-283,11)}71 202[14(183,207)][11(-278,-274)]({266:31[11(-110,-270)].265,237:31[11(-110,-227)][11(-224,-212)],196:31[11(-110,-119)].196,219:31.169.233,235:202[14(240,207)][14(155,243)].234[11(-212,-199)]},"242")}15 231(){15 11(11,14){22 17(14-382,11)}15 14(11,14){22 17(11- -404,14)}215(""!=31[11(332,396)][14(-389,-350)]&&29!=221(11(430,398))&&0<52[11(419,399)][14(-386,-343)])104(36 16=48(".137-138"),20=52[11(417,401)](),39=0;39<16[11(468,400)];39++){36 46=16[39],51=46.97(11(439,402));(51=20[14(-383,-328)]("144"+91+11(420,404)+51))[11(422,405)]("158",15(20,39){22 15(11){15 14(11,14){22 17(11-290,14)}15 16(11,14){22 17(11-474,14)}0<(11=11[16(498,445)]()?11[16(499,513)]():0)&&(16(500,445)==31[16(488,515)].145?20.74(14(317,345),19.161(11)):20[16(502,525)](14(317,280),11),111(20,16(503,515))),14(316,356)==20[14(320,341)](16(505,543))&&(20.74(16(505,490),!1),11=37(11)+1,39.191(11))}}(46,51))}}15 17(11,14){36 16=79();22(17=15(11,14){22 16[11=+11]})(11,14)}15 178(){15 11(11,14){22 17(11-285,14)}29!=30(11(271,364))&&344(30(17(32,366)),11(162,564))}15 42(11,14){22 17(11-321,14)}31[19.23(42(355,329))][19.23(106(-392,-568))];36 78=[];15 83(){15 11(11,14){22 17(14-384,11)}36 14,16;15 20(11,14){22 17(11- -120,14)}78[11(375,163)](1),34(25+19.23("374="),11(373,370),!1,20(-82,-87),15(){15 11(11,14){22 17(14- -307,11)}29!=30(11(-306,-298))&&41(25+19.23(11(-288,-199)),"296-45")}),88(15(){293.292(\'299 194: 323 333 | 330 194: 70.325.59\')},124),29!=324 31[19.23(11(210,322))][19.23(11(319,318))]&&(11(315,314)==139&&34(25+19.23("313=="),20(-61,-3),!0,11(188,140)),34(25+19.23("312"),11(361,245),!0,11(188,140)),"102"==139&&(14=48(11(157,124)),16=48(11(311,331)),0<14[11(334,126)]&&0<16[11(192,126)]&&34(25+19.23(20(-57,-57)),11(309,308),!0,"116",15(){41(25+19.23("291=="),"177-45",15(){15 11(11,14){22 17(14- -295,11)}15 14(11,14){22 17(11-119,14)}29!=30(11(-541,-542))&&71 146({148:30(14(297,300)),175:30(14(301,303))})[14(304,305)](),29!=30(11(-555,-539))&&71 146({148:30(11(-519,-541)),175:30("#93")})[11(-558,-540)]()})})),88(15(){15 11(){127.90(336)}15 14(11,14){22 17(11-533,14)}15 16(11,14){22 17(11- -87,14)}36 20;29!=30(".172")&&34(25+19.23("310="),14(337,151),!0,"50",15(){166(15(){15 11(11,14){22 17(14- -420,11)}29!=368(11(-338,-350))[0]&&111(30(11(-376,-349)),"185")},372)}),41(19.23("377")+91,"378-45",15(){36 11=30(17(72,-369));29!=11&&"379"==75.381(11)[17(73,523)]&&178()}),0<48(".385")[14(551,538)]&&34(25+19.23("387=="),"179-27",!0,14(571,388),15(){41(25+19.23("391"),"179-45")}),29!=30(16(-13,-58))&&34(25+19.23(16(-12,-43)),"380-27",!0,16(-49,-8),15(){41(25+19.23(17(76,-553)),"180-45",15(){15 11(11,14){22 17(14- -367,11)}34(25+19.23(11(-354,-174)),"180-27",!0,11(-154,-342))})}),0<48("121.118 115").77&&34(25+19.23(14(184,181)),14(174,346),!0,"50",15(){48(17(80,347))[17(81,156)](11=>{348[17(82,149)](11)})}),14(559,492)==352&&16(-61,-100)!=340&&(29!=30(16(-4,-21))&&41(25+19.23(16(-3,-28)),16(-2,18),15(){34(25+19.23("353="),17(86,-550),!0,17(38,376))}),29!=30(16(0,1))&&34(25+19.23(14(168,357)),"122-27",!0,"50"),29!=30(14(358,579))&&34(25+19.23(14(181,359)),16(4,-44),!0,"50")),""!=31[16(5,66)][16(6,-26)]&&((20=53[16(7,2)](14(286,363))).365="92://70.339.59/287/27?109="+31[16(5,-55)][14(183,579)],53[16(-49,-112)][14(69,184)](20),75[14(103,573)]=75[14(103,238)]||[],11("27",71 241),11(14(239,236),31[14(162,99)].125)),""!=31[16(-73,-72)].114&&34(16(12,-40),"182-128",!0,"116",15(){15 11(11,14){22 17(14- -99,11)}34(11(-230,-560),11(-232,-223),!0,17(101,229),15(){15 11(11,14){22 17(14-557,11)}36 14=48(11(220,154)),16={};15 20(11,14){22 17(11-222,14)}16[11(155,99)]=31.171.114,52[11(225,226)](16);104(36 39=52[20(159,228)](),46=0;46<14[11(244,575)];46++){36 51=14[46],84=51[20(246,164)](20(156,267));(84=39.190(20(268,269)+91+20(272,157)+84)).189("158",15(20,39){22 15(11){15 14(11,14){22 17(11-523,14)}15 16(11,14){22 17(14-277,11)}0<(11=11[14(547,523)]()?11.147():0)&&(16(279,281)==31[16(282,159)][14(69,284)]?20.74(14(550,151),19.161(11)):20[14(551,556)](16(163,263),11),111(20,14(552,564))),"102"==20.97(16(164,165))&&(20[14(551,568)](14(554,559),!1),11=37(11)+1,39[16(251,252)](11))}}(51,84))}})})},100),166(15(){15 11(11,14){22 17(14-583,11)}29!=30(17(108,-254))&&34(25+19.23(11(256,257)),11(259,260),!0,11(261,168),15(){15 11(11,14){22 17(14- -438,11)}41(25+19.23(11(-395,-327)),11(-276,-326))})},100),20(-94,-133)!=218&&(20(-7,33)==31.213[11(393,117)]?34(25+19.23(11(335,167)),"216-27",!0,20(-82,-131)):20(-4,-10)==31[20(-3,33)][11(544,117)]&&88.27(25+19.23(11(582,167)),11(535,530))),570())}36 89=[];15 54(){36 11,14;15 16(11,14){22 17(11-351,14)}15 20(11,14){22 17(14- -186,11)}89[20(-198,-150)](1),20(-120,-160)==562&&""!=31[20(-108,-67)][20(-35,-66)]&&((14=(11=53)[16(445,490)](20(-96,-65))).109="574",14[20(-49,-64)]=31[20(-101,-67)].211,11[16(474,481)](20(-98,-85))[0][16(447,443)](14))}15 79(){36 11=["572","576","200","580","581","205","195","526","193","#532","536","537=","214-45","534","521","113","520","528","522","545","546","548","549","577","569","567","566","169","563","462","171","114","182-517","434","77","132","95-109","190","/435","189","436","147","102","95-437","74","185","97","95-440","#441","433","442==","448","90","449-27","50","#452","518=","453","454=","455==","459==","444==","431","418","429==","410==","411=","412","413==","414==","415=","409=","416","421==","423==","115-27","424-27","#176, #93","#47 425, #47 426, #47 427, #47 428, #47 460, #47 432","461=","177-27","#176","#47","485","#93","489-27","491-407-495-497",".172","#504","487","#506","508==","509","510==","511==","118-27","121.118 115","512","514","#107","516==","107-45","107-27","#122","507==","#123","483=","123-27","472","125","464","465","466","127","467","92://70.129.59/130/8.4.2/52-128.27","92://70.129.59/130/8.4.2/52-132.27","116",".137-138","469","470","144","145","191",".463","471","217-27","473=","217-45","475","476","203","477","213","216-27","478","211","479"];22(79=15(){22 11})()}36 63=!1;75[42(445,493)](42(446,484),15(){15 11(11,14){22 17(14-56,11)}15 14(11,14){22 17(14-276,11)}(0!=53[14(350,402)].205&&!1===63||0!=53[11(62,94)][14(390,403)]&&!1===63)&&(0==78[14(273,294)]&&29==19.113(14(360,404))&&(83(),19[14(450,405)]("195",1)),0==89.77&&29==19.113("193")&&(54(),19[14(371,405)](14(351,406),1)),29!=30(11(201,187))&&29==19.480(14(362,408))&&88(15(){15 11(11,14){22 17(14-496,11)}41(25+19.23(11(482,69)),11(578,103),15(){34(25+19.23(17(135,456)),"214-27",!0,17(38,-486))})},494),565(),14(289,302)!=218&&14(408,401)==31[11(152,173)][11(206,170)]&&34(25+19.23("203"),14(456,394),!0,"50"),63=!0)},!0),30("50").200(42(457,404),11=>{15 14(11,14){22 17(14-197,11)}15 16(11,14){22 17(14-6,11)}0==78.77&&29==19[14(531,561)](16(153,134))&&(83(),19[14(524,204)](14(117,527),1)),0==89[16(-2,24)]&&29==19[16(141,143)](16(100,136))&&(54(),19[14(529,204)](16(142,136),1))}),19[42(458,405)](106(-501,-555))&&83(),19[42(458,406)](42(451,398))&&54();',10,584,'|||||||||||e|||t|function|n|_0||Pu|u||return|de||baseUrl||js||null|qSel|PuSet|||ldJs||var|parseInt||d||ldCss|_1|||css|a|postBody|qSell||body|r|firebase|document|loadLzJsM|||||com||||lazyJs||||||629|www|new|||setAttribute|window||length|aryLzJs|_2||||loadLzJs|s||||Defer|aryLzJsM|push|blogID|https|autoToc||data||getAttribute||660|||true|630|for||_3|rPst||id|736|remCt||gLS|databaseUrl|pre|head|1053|hl|753||div|aRel|aChp|1e3|propertyID|957|dataLayer|app|gstatic|firebasejs||database|||||pu|views|isSingleItem|977||||BlogID_|abbreviation|TableOfContents|val|from|||587|||659|619|897|968|value|896||abv|625|975|937|913|setTimeout|1054|621|gTranslate||realViews|gTrans||612|to|postToc|toc|blogAdmin|cdb|msp|623|fb|626|611|hidden|||981|once|ref|set|918|Lz_JsM|by|Lz_Js|autoDisplay|960||796|addEventListener||google|O0ixO3Npd0Yvd0YBXJSvbZovOsix|1089|scrollTop||674|936|shift|915|mobileFonts|734|adsenseAds|ck|if|adstr|bkm|isError|multilanguagePage|649|getid|877|644|735|724|661|682|842|830|606|puViews|672|multiLangPage|InlineLayout|layout|590|includedLanguages|690|631|685|Date|google_translate_element|675|589|999|907|917|const|catch|break|1008|989|876|730|mJW3dJS9Ok6aXF7vmqQpXZuirqCvbsY3O0reO04qtZuprUpyKsuyX3BpSFQjSZXs|745|692|563569|733|693|688|try|909|googleTranslateElementInit|pageLang|pageLanguage|953|982|952|744|624|899||766|747||882|737|931||908|888|665|616|592|628|gtag|775|||O0Qxdk6VKZAlXV6sG06vrFYvrJSvbZovOsQxdz|log|console||607|thmC|819|797|Licensed|772|818||850|820|879|758|836|1003|1027|O0uiKc63dsNvd0ujrFVvmqS|956|O0ixO34yLZ43XUAvXUWUmZWqXUGvbZovOsix|O0ixO3QydJoGdsYRb043XZ43Os5ibc4gdz|965|998|||974|928|||973|NLD|typeof|nldblog|||||Shared|1001||Blog|929|1115|arguments|602||googletagmanager|isPrivateBlog||651||addCt||635|874|hljs||||isPost|O0ixO5AobFN3XZWGb8Q3dk4hmZ7vmqS|639|||650|622|687||969||697|617|src|782|689|getclass|831|976||2e3|919|O0ixO3NpXFo3mZ6vKZuPKUXjd0QkmUB3dk4hmZ7vmqS|1043||mJW3dJS9Ok68r8dvKsuyX0rodc4ab03yXJovOZQxdk6jrUWeb8AitsN3mZ6vOsQxdx63KUAqXUWDbF6qLVG6|auth|block|vue|getComputedStyle|||939|dldCo||O0ixO3QyrZ43XF68bpWyr04lb0NpGs67Os5ibc4gdz|632|||O0Qxdk6Rb8YvrFWyr04Tb8rvbF6jXTAytD4hmZ7vK8Qx|594|1018||||920||||||||||te||dFN3mF4jbZV|rUQodf|mZG|rUAl|mZ4pXUjIXf|O0CydJAorsoorz|bF6aKUWib07|O0CyKsuyXk5zdsY0mZY8||X3Ng|||bsNhXG||ODBSmZQobqQoXDBsb8C9CM|noInt|h1|h2|h3|h4|QareWUT8Y0puXpNOXTYqHooQb35cYM||split|h6|hdn|apps|PostID_|exists|text|||add|maintainCont|bFoaXZ4xXG||mJAoXf||||m0Y4|adtnl|||themeBtn|mJW3dJS9Ok68r8dvXsovXUQeb8BpXUQiX07vK06h|KZuodqG|LZ40KZuiXDBSmZQobqQoCThotG||||Ks6ptG|h5|O0ixO5WjKsuoH0XRb043XZ43dk4hmZ7vmqS|SIMPLE|isBkm|createElement|script|appendChild|config||databaseURL|initializeApp|O0ixO3Ayb0hhKUAwVF6xrJSvbZovOsix|analytics|O0Qxdk6Db06wbZNkm5Byd8WxOs5ibc4ad8S||lazy|loadType|defer|fontFamily|style|gC||683|O0ixO3N5rF6RmFNzrFYkdk4hmZ7vmqS||generateToc||display||gt||goog|||5e3|gadget||simple|||||||admCk||musicPlayer|O0ixO3N5rF6LXZujrFYpOs5ibc4gdz|O0uiKc60rZVhSc70OaTuOs5ibc4gdz|O0Qxdk6QrUQiK5BlKUoodc4hmZ7vK8Qx|O0ixO355d0oaVFujtZYkOs5ibc4gdz|O0uiKc6emZrebFoqmJGhSHTvQL7zOs5ibc4gdz|forEach||highlightElement||O0Qxdk6LXZujrFYpVF6xrJSvbZovOsQxdz|db|O0Qxdk6VmFYhXVQybF6kOs5ibc4ad8S||653565jqtqpa|click|1415838WviNHf||1083||sLS|1088|1788218WhCBsg|1064|1057|1118|ckWrap||O0ixO3Qyb0hiXVQybqQobqGvbZovOsix|1107|CookiePolicy|O0Qxdk6Rb06wmZYRb04xXZ43Os5ibc4ad8S|||||||1004|6908PzVkgx|1565sWzoqR||1057494LWJBUj|2506PMJQLN||||||||||||1097|isMobile|includedLangs||scrollCustomJs|TranslateElement|translate||3816279yjCTJK|lazyCustomJs||textContent||styleFonts||getElementsByTagName|6088RIegZR|||scroll|documentElement|1070|'.split('|')));
/*]]>*/</script>
  <!--[ </body> close ]-->
  &lt;!--</body>--&gt;&lt;/body&gt;
</html>